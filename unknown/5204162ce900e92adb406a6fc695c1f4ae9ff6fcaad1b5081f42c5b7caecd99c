{"version": 3, "sources": ["src/sdk/SpeechRecognitionCanceledEventArgs.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAElC,OAAO,EACH,yBAAyB,GAC5B,MAAM,6BAA6B,CAAC;AAErC,MAAM,OAAO,kCAAmC,SAAQ,yBAAyB;CAChF", "file": "SpeechRecognitionCanceledEventArgs.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    CancellationEventArgsBase,\r\n} from \"./CancellationEventArgsBase\";\r\n\r\nexport class SpeechRecognitionCanceledEventArgs extends CancellationEventArgsBase {\r\n}\r\n"]}