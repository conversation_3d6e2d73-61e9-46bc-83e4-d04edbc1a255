{"version": 3, "sources": ["src/common.speech/RequestSession.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIlC,6CAO2B;AAC3B,yDAM6B;AAC7B,yFAA+E;AAE/E;IAsBI,wBAAY,aAAqB;QAAjC,iBAQC;QA7BO,mBAAc,GAAY,KAAK,CAAC;QAEhC,oBAAe,GAAkB,IAAI,KAAK,EAAe,CAAC;QAM1D,4BAAuB,GAAY,KAAK,CAAC;QACzC,sBAAiB,GAAY,KAAK,CAAC;QACnC,sBAAiB,GAAY,KAAK,CAAC;QACnC,6BAAwB,GAAW,CAAC,CAAC;QACrC,uBAAkB,GAAW,CAAC,CAAC;QAC/B,2BAAsB,GAAY,KAAK,CAAC;QACxC,kBAAa,GAAW,CAAC,CAAC;QAC1B,oBAAe,GAAW,CAAC,CAAC;QAG5B,eAAU,GAAY,KAAK,CAAC;QAC5B,2BAAsB,GAAW,CAAC,CAAC;QAiFpC,yBAAoB,GAAG,UAAC,gBAAwB,EAAE,YAAoB;YACzE,KAAI,CAAC,oBAAoB,GAAG,gBAAgB,CAAC;YAC7C,KAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,KAAI,CAAC,OAAO,CAAC,IAAI,4CAAwB,CAAC,KAAI,CAAC,aAAa,EAAE,KAAI,CAAC,oBAAoB,EAAE,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QAClH,CAAC,CAAA;QAoCM,+BAA0B,GAAG;YAChC,IAAI,CAAC,CAAC,KAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,KAAI,CAAC,UAAU,EAAE;gBAC9C,0DAA0D;gBAC1D,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,gDAAgD,CAAC,CAAC;gBAC/E,2EAA2E;gBAC3E,6BAA6B;gBAC7B,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,cAAQ,CAAC,CAAC,CAAC;aACzD;YACD,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,KAAI,CAAC,gBAAgB,GAAG,IAAI,kBAAQ,EAAQ,CAAC;QACjD,CAAC,CAAA;QA0CM,iBAAY,GAAG;YAClB,IAAI,KAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE;gBAChD,OAAO,KAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,CAAC;aAC3D;iBAAM;gBACH,OAAO,IAAI,CAAC;aACf;QACL,CAAC,CAAA;QAWS,YAAO,GAAG,UAAC,KAA6B;YAC9C,IAAI,CAAC,CAAC,KAAI,CAAC,4BAA4B,EAAE;gBACrC,KAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACpD;YACD,gBAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAA;QAhMG,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,0BAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,0BAAgB,EAAE,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,kBAAQ,EAAQ,CAAC;QAE7C,mCAAmC;QACnC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IACpC,CAAC;IAED,sBAAW,qCAAS;aAApB;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,sBAAW,qCAAS;aAApB;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,sBAAW,uCAAW;aAAtB;YACI,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC;;;OAAA;IAED,sBAAW,iDAAqB;aAAhC;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QACzC,CAAC;;;OAAA;IAED,sBAAW,yCAAa;aAAxB;YACI,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC;;;OAAA;IAED,sBAAW,yCAAa;aAAxB;YACI,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC;;;OAAA;IAED,sBAAW,kDAAsB;aAAjC;YACI,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACzC,CAAC;;;OAAA;IAED,sBAAW,uCAAW;aAAtB;YACI,OAAO,IAAI,CAAC,eAAe,CAAC;QAChC,CAAC;;;OAAA;IAED,sBAAW,iDAAqB;aAAhC;YACI,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACvC,CAAC;;;OAAA;IAID,sBAAW,qCAAS;QAFpB,uDAAuD;QACvD,+DAA+D;aAC/D;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IACM,kDAAyB,GAAhC,UAAiC,WAAwC;QACrE,IAAI,CAAC,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACrC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;SAC5F;IACL,CAAC;IAEM,4CAAmB,GAA1B;QACI,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,0BAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,4BAA4B,GAAG,IAAI,4DAAwB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACnI,IAAI,CAAC,OAAO,CAAC,IAAI,6CAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IAClI,CAAC;IAEY,qDAA4B,GAAzC,UAA0C,SAA8B,EAAE,OAAgB,EAAE,KAAc;;;;;wBACtG,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;wBAC/B,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;6BAEjC,OAAO,EAAP,wBAAO;wBACP,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAC;;;wBAExB,IAAI,CAAC,OAAO,CAAC,IAAI,yCAAqB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;;;;;;KAErI;IAQY,wCAAe,GAA5B,UAA6B,OAAgB,EAAE,KAAc;;;;;6BACrD,OAAO,EAAP,wBAAO;wBACP,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAC;;;;;;KAE/B;IAEY,uDAA8B,GAA3C,UAA4C,UAAkB,EAAE,MAAe;;;;;6BACvE,CAAA,UAAU,KAAK,GAAG,CAAA,EAAlB,wBAAkB;wBAClB,IAAI,CAAC,OAAO,CAAC,IAAI,2CAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;wBACvJ,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;4BACtB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;yBAC/B;wBACD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC;wBACxD,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;wBACvB,sBAAO;;6BACA,CAAA,UAAU,KAAK,GAAG,CAAA,EAAlB,wBAAkB;wBACzB,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAC;;;;;;KAE/B;IAEY,iDAAwB,GAArC,UAAsC,qBAA8B;;;;;wBAChE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;6BAE5B,CAAA,CAAC,qBAAqB,IAAI,IAAI,CAAC,aAAa,CAAA,EAA5C,wBAA4C;wBAC5C,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAC;wBACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;;;wBAExB,2BAA2B;wBAC3B,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC;wBACxD,IAAI,CAAC,aAAa,GAAG,0BAAgB,EAAE,CAAC;wBACxC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;;;;;;KAEnC;IAcM,qCAAY,GAAnB,UAAoB,MAAc;QAC9B,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;SACrG;IACL,CAAC;IAEM,2CAAkB,GAAzB,UAA0B,MAAc;QACpC,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9F,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEM,4CAAmB,GAA1B,UAA2B,MAAc;QACrC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;QACjC,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;IACpC,CAAC;IAEM,oCAAW,GAAlB,UAAmB,SAAiB;QAChC,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC;IACpC,CAAC;IAEM,0CAAiB,GAAxB;QACI,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAEY,gCAAO,GAApB,UAAqB,KAAc;;;;;;6BAC3B,CAAC,IAAI,CAAC,cAAc,EAApB,wBAAoB;wBACpB,uEAAuE;wBACvE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;8BACkB,EAApB,KAAA,IAAI,CAAC,eAAe;;;6BAApB,CAAA,cAAoB,CAAA;wBAAlC,UAAU;wBACjB,qBAAM,UAAU,CAAC,MAAM,EAAE,EAAA;;wBAAzB,SAAyB,CAAC;;;wBADL,IAAoB,CAAA;;;wBAI7C,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,CAAC;wBAC5C,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;;;;;;KAEtC;IAUY,0CAAiB,GAA9B;;;;4BACI,qBAAM,IAAI,CAAC,UAAU,EAAE,EAAA;;wBAAvB,SAAuB,CAAC;;;;;KAC3B;IAED,+FAA+F;IACxF,sCAAa,GAApB;QACI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IASa,mCAAU,GAAxB;;;;;6BACQ,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAxB,wBAAwB;wBACxB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBAC/B,qBAAM,IAAI,CAAC,eAAe,EAAE,EAAA;;wBAA5B,SAA4B,CAAC;;;;;;KAEpC;IAEa,wCAAe,GAA7B;;;;;6BACQ,CAAC,IAAI,CAAC,uBAAuB,EAA7B,wBAA6B;wBAC7B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;6BAChC,IAAI,CAAC,aAAa,EAAlB,wBAAkB;wBAClB,qBAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAA;;wBAAjC,SAAiC,CAAC;;;;;;KAG7C;IACL,qBAAC;AAAD,CAxOA,AAwOC,IAAA;AAxOY,wCAAc", "file": "RequestSession.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport { Z_NO_COMPRESSION } from \"zlib\";\r\nimport { ReplayableAudioNode } from \"../common.browser/Exports\";\r\nimport {\r\n    createNoDashGuid,\r\n    Deferred,\r\n    Events,\r\n    IDetachable,\r\n    IEventSource,\r\n    PlatformEvent\r\n} from \"../common/Exports\";\r\nimport {\r\n    ConnectingToServiceEvent,\r\n    ListeningStartedEvent,\r\n    RecognitionStartedEvent,\r\n    RecognitionTriggeredEvent,\r\n    SpeechRecognitionEvent,\r\n} from \"./RecognitionEvents\";\r\nimport { ServiceTelemetryListener } from \"./ServiceTelemetryListener.Internal\";\r\n\r\nexport class RequestSession {\r\n    private privIsDisposed: boolean = false;\r\n    private privServiceTelemetryListener: ServiceTelemetryListener;\r\n    private privDetachables: IDetachable[] = new Array<IDetachable>();\r\n    private privRequestId: string;\r\n    private privAudioSourceId: string;\r\n    private privAudioNodeId: string;\r\n    private privAudioNode: ReplayableAudioNode;\r\n    private privAuthFetchEventId: string;\r\n    private privIsAudioNodeDetached: boolean = false;\r\n    private privIsRecognizing: boolean = false;\r\n    private privIsSpeechEnded: boolean = false;\r\n    private privTurnStartAudioOffset: number = 0;\r\n    private privLastRecoOffset: number = 0;\r\n    private privHypothesisReceived: boolean = false;\r\n    private privBytesSent: number = 0;\r\n    private privRecogNumber: number = 0;\r\n    private privSessionId: string;\r\n    private privTurnDeferral: Deferred<void>;\r\n    private privInTurn: boolean = false;\r\n    private privConnectionAttempts: number = 0;\r\n\r\n    constructor(audioSourceId: string) {\r\n        this.privAudioSourceId = audioSourceId;\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privAudioNodeId = createNoDashGuid();\r\n        this.privTurnDeferral = new Deferred<void>();\r\n\r\n        // We're not in a turn, so resolve.\r\n        this.privTurnDeferral.resolve();\r\n    }\r\n\r\n    public get sessionId(): string {\r\n        return this.privSessionId;\r\n    }\r\n\r\n    public get requestId(): string {\r\n        return this.privRequestId;\r\n    }\r\n\r\n    public get audioNodeId(): string {\r\n        return this.privAudioNodeId;\r\n    }\r\n\r\n    public get turnCompletionPromise(): Promise<void> {\r\n        return this.privTurnDeferral.promise;\r\n    }\r\n\r\n    public get isSpeechEnded(): boolean {\r\n        return this.privIsSpeechEnded;\r\n    }\r\n\r\n    public get isRecognizing(): boolean {\r\n        return this.privIsRecognizing;\r\n    }\r\n\r\n    public get currentTurnAudioOffset(): number {\r\n        return this.privTurnStartAudioOffset;\r\n    }\r\n\r\n    public get recogNumber(): number {\r\n        return this.privRecogNumber;\r\n    }\r\n\r\n    public get numConnectionAttempts(): number {\r\n        return this.privConnectionAttempts;\r\n    }\r\n\r\n    // The number of bytes sent for the current connection.\r\n    // Counter is reset to 0 each time a connection is established.\r\n    public get bytesSent(): number {\r\n        return this.privBytesSent;\r\n    }\r\n    public listenForServiceTelemetry(eventSource: IEventSource<PlatformEvent>): void {\r\n        if (!!this.privServiceTelemetryListener) {\r\n            this.privDetachables.push(eventSource.attachListener(this.privServiceTelemetryListener));\r\n        }\r\n    }\r\n\r\n    public startNewRecognition(): void {\r\n        this.privIsSpeechEnded = false;\r\n        this.privIsRecognizing = true;\r\n        this.privTurnStartAudioOffset = 0;\r\n        this.privLastRecoOffset = 0;\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privRecogNumber++;\r\n        this.privServiceTelemetryListener = new ServiceTelemetryListener(this.privRequestId, this.privAudioSourceId, this.privAudioNodeId);\r\n        this.onEvent(new RecognitionTriggeredEvent(this.requestId, this.privSessionId, this.privAudioSourceId, this.privAudioNodeId));\r\n    }\r\n\r\n    public async onAudioSourceAttachCompleted(audioNode: ReplayableAudioNode, isError: boolean, error?: string): Promise<void> {\r\n        this.privAudioNode = audioNode;\r\n        this.privIsAudioNodeDetached = false;\r\n\r\n        if (isError) {\r\n            await this.onComplete();\r\n        } else {\r\n            this.onEvent(new ListeningStartedEvent(this.privRequestId, this.privSessionId, this.privAudioSourceId, this.privAudioNodeId));\r\n        }\r\n    }\r\n\r\n    public onPreConnectionStart = (authFetchEventId: string, connectionId: string): void => {\r\n        this.privAuthFetchEventId = authFetchEventId;\r\n        this.privSessionId = connectionId;\r\n        this.onEvent(new ConnectingToServiceEvent(this.privRequestId, this.privAuthFetchEventId, this.privSessionId));\r\n    }\r\n\r\n    public async onAuthCompleted(isError: boolean, error?: string): Promise<void> {\r\n        if (isError) {\r\n            await this.onComplete();\r\n        }\r\n    }\r\n\r\n    public async onConnectionEstablishCompleted(statusCode: number, reason?: string): Promise<void> {\r\n        if (statusCode === 200) {\r\n            this.onEvent(new RecognitionStartedEvent(this.requestId, this.privAudioSourceId, this.privAudioNodeId, this.privAuthFetchEventId, this.privSessionId));\r\n            if (!!this.privAudioNode) {\r\n                this.privAudioNode.replay();\r\n            }\r\n            this.privTurnStartAudioOffset = this.privLastRecoOffset;\r\n            this.privBytesSent = 0;\r\n            return;\r\n        } else if (statusCode === 403) {\r\n            await this.onComplete();\r\n        }\r\n    }\r\n\r\n    public async onServiceTurnEndResponse(continuousRecognition: boolean): Promise<void> {\r\n        this.privTurnDeferral.resolve();\r\n\r\n        if (!continuousRecognition || this.isSpeechEnded) {\r\n            await this.onComplete();\r\n            this.privInTurn = false;\r\n        } else {\r\n            // Start a new request set.\r\n            this.privTurnStartAudioOffset = this.privLastRecoOffset;\r\n            this.privRequestId = createNoDashGuid();\r\n            this.privAudioNode.replay();\r\n        }\r\n    }\r\n\r\n    public onServiceTurnStartResponse = (): void => {\r\n        if (!!this.privTurnDeferral && !!this.privInTurn) {\r\n            // What? How are we starting a turn with another not done?\r\n            this.privTurnDeferral.reject(\"Another turn started before current completed.\");\r\n            // Avoid UnhandledPromiseRejection if privTurnDeferral is not being awaited\r\n            /* tslint:disable:no-empty */\r\n            this.privTurnDeferral.promise.then().catch(() => { });\r\n        }\r\n        this.privInTurn = true;\r\n        this.privTurnDeferral = new Deferred<void>();\r\n    }\r\n\r\n    public onHypothesis(offset: number): void {\r\n        if (!this.privHypothesisReceived) {\r\n            this.privHypothesisReceived = true;\r\n            this.privServiceTelemetryListener.hypothesisReceived(this.privAudioNode.findTimeAtOffset(offset));\r\n        }\r\n    }\r\n\r\n    public onPhraseRecognized(offset: number): void {\r\n        this.privServiceTelemetryListener.phraseReceived(this.privAudioNode.findTimeAtOffset(offset));\r\n        this.onServiceRecognized(offset);\r\n    }\r\n\r\n    public onServiceRecognized(offset: number): void {\r\n        this.privLastRecoOffset = offset;\r\n        this.privHypothesisReceived = false;\r\n        this.privAudioNode.shrinkBuffers(offset);\r\n        this.privConnectionAttempts = 0;\r\n    }\r\n\r\n    public onAudioSent(bytesSent: number): void {\r\n        this.privBytesSent += bytesSent;\r\n    }\r\n\r\n    public onRetryConnection(): void {\r\n        this.privConnectionAttempts++;\r\n    }\r\n\r\n    public async dispose(error?: string): Promise<void> {\r\n        if (!this.privIsDisposed) {\r\n            // we should have completed by now. If we did not its an unknown error.\r\n            this.privIsDisposed = true;\r\n            for (const detachable of this.privDetachables) {\r\n                await detachable.detach();\r\n            }\r\n\r\n            this.privServiceTelemetryListener.dispose();\r\n            this.privIsRecognizing = false;\r\n        }\r\n    }\r\n\r\n    public getTelemetry = (): string => {\r\n        if (this.privServiceTelemetryListener.hasTelemetry) {\r\n            return this.privServiceTelemetryListener.getTelemetry();\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    public async onStopRecognizing(): Promise<void> {\r\n        await this.onComplete();\r\n    }\r\n\r\n    // Should be called with the audioNode for this session has indicated that it is out of speech.\r\n    public onSpeechEnded(): void {\r\n        this.privIsSpeechEnded = true;\r\n    }\r\n\r\n    protected onEvent = (event: SpeechRecognitionEvent): void => {\r\n        if (!!this.privServiceTelemetryListener) {\r\n            this.privServiceTelemetryListener.onEvent(event);\r\n        }\r\n        Events.instance.onEvent(event);\r\n    }\r\n\r\n    private async onComplete(): Promise<void> {\r\n        if (!!this.privIsRecognizing) {\r\n            this.privIsRecognizing = false;\r\n            await this.detachAudioNode();\r\n        }\r\n    }\r\n\r\n    private async detachAudioNode(): Promise<void> {\r\n        if (!this.privIsAudioNodeDetached) {\r\n            this.privIsAudioNodeDetached = true;\r\n            if (this.privAudioNode) {\r\n                await this.privAudioNode.detach();\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}