import React, { type ChangeEvent, type ComponentType, type FocusEvent, type KeyboardEvent, type SyntheticEvent } from 'react';
export type InputTargetProps<H> = {
    onChange?: (event: ChangeEvent<H>) => void;
    onFocus?: (event: FocusEvent<H>) => void;
    onKeyDown?: (event: KeyboardEvent<H>) => void;
    onSelect?: (event: SyntheticEvent<H>) => void;
    value?: string;
};
type PropsOf<T> = T extends ComponentType<infer P> ? P : never;
export default function withEmoji<T extends ComponentType<P>, P extends InputTargetProps<H> = PropsOf<T>, H extends HTMLInputElement | HTMLTextAreaElement = P extends InputTargetProps<infer H> ? H : never>(componentType: T): React.ForwardRefExoticComponent<React.PropsWithoutRef<Readonly<Omit<P, "onChange" | "emojiMap"> & {
    emojiMap?: Map<string, string>;
    onChange?: (value: string | undefined) => void;
}>> & React.RefAttributes<H>>;
export {};
//# sourceMappingURL=withEmoji.d.ts.map