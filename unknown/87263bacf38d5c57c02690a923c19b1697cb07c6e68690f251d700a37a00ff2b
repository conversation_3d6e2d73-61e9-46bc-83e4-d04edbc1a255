{"version": 3, "sources": ["src/common.speech/RequestSession.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;AAIlC,OAAO,EACH,gBAAgB,EAChB,QAAQ,EACR,MAAM,EAIT,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACH,wBAAwB,EACxB,qBAAqB,EACrB,uBAAuB,EACvB,yBAAyB,GAE5B,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,wBAAwB,EAAE,MAAM,qCAAqC,CAAC;AAE/E,MAAM,OAAO,cAAc;IAsBvB,YAAY,aAAqB;QArBzB,mBAAc,GAAY,KAAK,CAAC;QAEhC,oBAAe,GAAkB,IAAI,KAAK,EAAe,CAAC;QAM1D,4BAAuB,GAAY,KAAK,CAAC;QACzC,sBAAiB,GAAY,KAAK,CAAC;QACnC,sBAAiB,GAAY,KAAK,CAAC;QACnC,6BAAwB,GAAW,CAAC,CAAC;QACrC,uBAAkB,GAAW,CAAC,CAAC;QAC/B,2BAAsB,GAAY,KAAK,CAAC;QACxC,kBAAa,GAAW,CAAC,CAAC;QAC1B,oBAAe,GAAW,CAAC,CAAC;QAG5B,eAAU,GAAY,KAAK,CAAC;QAC5B,2BAAsB,GAAW,CAAC,CAAC;QAiFpC,yBAAoB,GAAG,CAAC,gBAAwB,EAAE,YAAoB,EAAQ,EAAE;YACnF,IAAI,CAAC,oBAAoB,GAAG,gBAAgB,CAAC;YAC7C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,wBAAwB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QAClH,CAAC,CAAA;QAoCM,+BAA0B,GAAG,GAAS,EAAE;YAC3C,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC9C,0DAA0D;gBAC1D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,gDAAgD,CAAC,CAAC;gBAC/E,2EAA2E;gBAC3E,6BAA6B;gBAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;aACzD;YACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,gBAAgB,GAAG,IAAI,QAAQ,EAAQ,CAAC;QACjD,CAAC,CAAA;QA0CM,iBAAY,GAAG,GAAW,EAAE;YAC/B,IAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE;gBAChD,OAAO,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,CAAC;aAC3D;iBAAM;gBACH,OAAO,IAAI,CAAC;aACf;QACL,CAAC,CAAA;QAWS,YAAO,GAAG,CAAC,KAA6B,EAAQ,EAAE;YACxD,IAAI,CAAC,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACrC,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aACpD;YACD,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAA;QAhMG,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,gBAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,eAAe,GAAG,gBAAgB,EAAE,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,IAAI,QAAQ,EAAQ,CAAC;QAE7C,mCAAmC;QACnC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IACpC,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACzC,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,IAAW,sBAAsB;QAC7B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,uDAAuD;IACvD,+DAA+D;IAC/D,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACM,yBAAyB,CAAC,WAAwC;QACrE,IAAI,CAAC,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACrC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;SAC5F;IACL,CAAC;IAEM,mBAAmB;QACtB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,gBAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,4BAA4B,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACnI,IAAI,CAAC,OAAO,CAAC,IAAI,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IAClI,CAAC;IAEY,4BAA4B,CAAC,SAA8B,EAAE,OAAgB,EAAE,KAAc;;YACtG,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;YAC/B,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;YAErC,IAAI,OAAO,EAAE;gBACT,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;aAC3B;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,IAAI,qBAAqB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;aACjI;QACL,CAAC;KAAA;IAQY,eAAe,CAAC,OAAgB,EAAE,KAAc;;YACzD,IAAI,OAAO,EAAE;gBACT,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;aAC3B;QACL,CAAC;KAAA;IAEY,8BAA8B,CAAC,UAAkB,EAAE,MAAe;;YAC3E,IAAI,UAAU,KAAK,GAAG,EAAE;gBACpB,IAAI,CAAC,OAAO,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBACvJ,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE;oBACtB,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;iBAC/B;gBACD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBACxD,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;gBACvB,OAAO;aACV;iBAAM,IAAI,UAAU,KAAK,GAAG,EAAE;gBAC3B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;aAC3B;QACL,CAAC;KAAA;IAEY,wBAAwB,CAAC,qBAA8B;;YAChE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAEhC,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC9C,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;gBACxB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;aAC3B;iBAAM;gBACH,2BAA2B;gBAC3B,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBACxD,IAAI,CAAC,aAAa,GAAG,gBAAgB,EAAE,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;aAC/B;QACL,CAAC;KAAA;IAcM,YAAY,CAAC,MAAc;QAC9B,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;SACrG;IACL,CAAC;IAEM,kBAAkB,CAAC,MAAc;QACpC,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9F,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAEM,mBAAmB,CAAC,MAAc;QACrC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;QACjC,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;IACpC,CAAC;IAEM,WAAW,CAAC,SAAiB;QAChC,IAAI,CAAC,aAAa,IAAI,SAAS,CAAC;IACpC,CAAC;IAEM,iBAAiB;QACpB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAEY,OAAO,CAAC,KAAc;;YAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,uEAAuE;gBACvE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE;oBAC3C,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;iBAC7B;gBAED,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,CAAC;gBAC5C,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;aAClC;QACL,CAAC;KAAA;IAUY,iBAAiB;;YAC1B,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC5B,CAAC;KAAA;IAED,+FAA+F;IACxF,aAAa;QAChB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IASa,UAAU;;YACpB,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBAC/B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;aAChC;QACL,CAAC;KAAA;IAEa,eAAe;;YACzB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;iBACrC;aACJ;QACL,CAAC;KAAA;CACJ", "file": "RequestSession.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport { Z_NO_COMPRESSION } from \"zlib\";\r\nimport { ReplayableAudioNode } from \"../common.browser/Exports\";\r\nimport {\r\n    createNoDashGuid,\r\n    Deferred,\r\n    Events,\r\n    IDetachable,\r\n    IEventSource,\r\n    PlatformEvent\r\n} from \"../common/Exports\";\r\nimport {\r\n    ConnectingToServiceEvent,\r\n    ListeningStartedEvent,\r\n    RecognitionStartedEvent,\r\n    RecognitionTriggeredEvent,\r\n    SpeechRecognitionEvent,\r\n} from \"./RecognitionEvents\";\r\nimport { ServiceTelemetryListener } from \"./ServiceTelemetryListener.Internal\";\r\n\r\nexport class RequestSession {\r\n    private privIsDisposed: boolean = false;\r\n    private privServiceTelemetryListener: ServiceTelemetryListener;\r\n    private privDetachables: IDetachable[] = new Array<IDetachable>();\r\n    private privRequestId: string;\r\n    private privAudioSourceId: string;\r\n    private privAudioNodeId: string;\r\n    private privAudioNode: ReplayableAudioNode;\r\n    private privAuthFetchEventId: string;\r\n    private privIsAudioNodeDetached: boolean = false;\r\n    private privIsRecognizing: boolean = false;\r\n    private privIsSpeechEnded: boolean = false;\r\n    private privTurnStartAudioOffset: number = 0;\r\n    private privLastRecoOffset: number = 0;\r\n    private privHypothesisReceived: boolean = false;\r\n    private privBytesSent: number = 0;\r\n    private privRecogNumber: number = 0;\r\n    private privSessionId: string;\r\n    private privTurnDeferral: Deferred<void>;\r\n    private privInTurn: boolean = false;\r\n    private privConnectionAttempts: number = 0;\r\n\r\n    constructor(audioSourceId: string) {\r\n        this.privAudioSourceId = audioSourceId;\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privAudioNodeId = createNoDashGuid();\r\n        this.privTurnDeferral = new Deferred<void>();\r\n\r\n        // We're not in a turn, so resolve.\r\n        this.privTurnDeferral.resolve();\r\n    }\r\n\r\n    public get sessionId(): string {\r\n        return this.privSessionId;\r\n    }\r\n\r\n    public get requestId(): string {\r\n        return this.privRequestId;\r\n    }\r\n\r\n    public get audioNodeId(): string {\r\n        return this.privAudioNodeId;\r\n    }\r\n\r\n    public get turnCompletionPromise(): Promise<void> {\r\n        return this.privTurnDeferral.promise;\r\n    }\r\n\r\n    public get isSpeechEnded(): boolean {\r\n        return this.privIsSpeechEnded;\r\n    }\r\n\r\n    public get isRecognizing(): boolean {\r\n        return this.privIsRecognizing;\r\n    }\r\n\r\n    public get currentTurnAudioOffset(): number {\r\n        return this.privTurnStartAudioOffset;\r\n    }\r\n\r\n    public get recogNumber(): number {\r\n        return this.privRecogNumber;\r\n    }\r\n\r\n    public get numConnectionAttempts(): number {\r\n        return this.privConnectionAttempts;\r\n    }\r\n\r\n    // The number of bytes sent for the current connection.\r\n    // Counter is reset to 0 each time a connection is established.\r\n    public get bytesSent(): number {\r\n        return this.privBytesSent;\r\n    }\r\n    public listenForServiceTelemetry(eventSource: IEventSource<PlatformEvent>): void {\r\n        if (!!this.privServiceTelemetryListener) {\r\n            this.privDetachables.push(eventSource.attachListener(this.privServiceTelemetryListener));\r\n        }\r\n    }\r\n\r\n    public startNewRecognition(): void {\r\n        this.privIsSpeechEnded = false;\r\n        this.privIsRecognizing = true;\r\n        this.privTurnStartAudioOffset = 0;\r\n        this.privLastRecoOffset = 0;\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privRecogNumber++;\r\n        this.privServiceTelemetryListener = new ServiceTelemetryListener(this.privRequestId, this.privAudioSourceId, this.privAudioNodeId);\r\n        this.onEvent(new RecognitionTriggeredEvent(this.requestId, this.privSessionId, this.privAudioSourceId, this.privAudioNodeId));\r\n    }\r\n\r\n    public async onAudioSourceAttachCompleted(audioNode: ReplayableAudioNode, isError: boolean, error?: string): Promise<void> {\r\n        this.privAudioNode = audioNode;\r\n        this.privIsAudioNodeDetached = false;\r\n\r\n        if (isError) {\r\n            await this.onComplete();\r\n        } else {\r\n            this.onEvent(new ListeningStartedEvent(this.privRequestId, this.privSessionId, this.privAudioSourceId, this.privAudioNodeId));\r\n        }\r\n    }\r\n\r\n    public onPreConnectionStart = (authFetchEventId: string, connectionId: string): void => {\r\n        this.privAuthFetchEventId = authFetchEventId;\r\n        this.privSessionId = connectionId;\r\n        this.onEvent(new ConnectingToServiceEvent(this.privRequestId, this.privAuthFetchEventId, this.privSessionId));\r\n    }\r\n\r\n    public async onAuthCompleted(isError: boolean, error?: string): Promise<void> {\r\n        if (isError) {\r\n            await this.onComplete();\r\n        }\r\n    }\r\n\r\n    public async onConnectionEstablishCompleted(statusCode: number, reason?: string): Promise<void> {\r\n        if (statusCode === 200) {\r\n            this.onEvent(new RecognitionStartedEvent(this.requestId, this.privAudioSourceId, this.privAudioNodeId, this.privAuthFetchEventId, this.privSessionId));\r\n            if (!!this.privAudioNode) {\r\n                this.privAudioNode.replay();\r\n            }\r\n            this.privTurnStartAudioOffset = this.privLastRecoOffset;\r\n            this.privBytesSent = 0;\r\n            return;\r\n        } else if (statusCode === 403) {\r\n            await this.onComplete();\r\n        }\r\n    }\r\n\r\n    public async onServiceTurnEndResponse(continuousRecognition: boolean): Promise<void> {\r\n        this.privTurnDeferral.resolve();\r\n\r\n        if (!continuousRecognition || this.isSpeechEnded) {\r\n            await this.onComplete();\r\n            this.privInTurn = false;\r\n        } else {\r\n            // Start a new request set.\r\n            this.privTurnStartAudioOffset = this.privLastRecoOffset;\r\n            this.privRequestId = createNoDashGuid();\r\n            this.privAudioNode.replay();\r\n        }\r\n    }\r\n\r\n    public onServiceTurnStartResponse = (): void => {\r\n        if (!!this.privTurnDeferral && !!this.privInTurn) {\r\n            // What? How are we starting a turn with another not done?\r\n            this.privTurnDeferral.reject(\"Another turn started before current completed.\");\r\n            // Avoid UnhandledPromiseRejection if privTurnDeferral is not being awaited\r\n            /* tslint:disable:no-empty */\r\n            this.privTurnDeferral.promise.then().catch(() => { });\r\n        }\r\n        this.privInTurn = true;\r\n        this.privTurnDeferral = new Deferred<void>();\r\n    }\r\n\r\n    public onHypothesis(offset: number): void {\r\n        if (!this.privHypothesisReceived) {\r\n            this.privHypothesisReceived = true;\r\n            this.privServiceTelemetryListener.hypothesisReceived(this.privAudioNode.findTimeAtOffset(offset));\r\n        }\r\n    }\r\n\r\n    public onPhraseRecognized(offset: number): void {\r\n        this.privServiceTelemetryListener.phraseReceived(this.privAudioNode.findTimeAtOffset(offset));\r\n        this.onServiceRecognized(offset);\r\n    }\r\n\r\n    public onServiceRecognized(offset: number): void {\r\n        this.privLastRecoOffset = offset;\r\n        this.privHypothesisReceived = false;\r\n        this.privAudioNode.shrinkBuffers(offset);\r\n        this.privConnectionAttempts = 0;\r\n    }\r\n\r\n    public onAudioSent(bytesSent: number): void {\r\n        this.privBytesSent += bytesSent;\r\n    }\r\n\r\n    public onRetryConnection(): void {\r\n        this.privConnectionAttempts++;\r\n    }\r\n\r\n    public async dispose(error?: string): Promise<void> {\r\n        if (!this.privIsDisposed) {\r\n            // we should have completed by now. If we did not its an unknown error.\r\n            this.privIsDisposed = true;\r\n            for (const detachable of this.privDetachables) {\r\n                await detachable.detach();\r\n            }\r\n\r\n            this.privServiceTelemetryListener.dispose();\r\n            this.privIsRecognizing = false;\r\n        }\r\n    }\r\n\r\n    public getTelemetry = (): string => {\r\n        if (this.privServiceTelemetryListener.hasTelemetry) {\r\n            return this.privServiceTelemetryListener.getTelemetry();\r\n        } else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    public async onStopRecognizing(): Promise<void> {\r\n        await this.onComplete();\r\n    }\r\n\r\n    // Should be called with the audioNode for this session has indicated that it is out of speech.\r\n    public onSpeechEnded(): void {\r\n        this.privIsSpeechEnded = true;\r\n    }\r\n\r\n    protected onEvent = (event: SpeechRecognitionEvent): void => {\r\n        if (!!this.privServiceTelemetryListener) {\r\n            this.privServiceTelemetryListener.onEvent(event);\r\n        }\r\n        Events.instance.onEvent(event);\r\n    }\r\n\r\n    private async onComplete(): Promise<void> {\r\n        if (!!this.privIsRecognizing) {\r\n            this.privIsRecognizing = false;\r\n            await this.detachAudioNode();\r\n        }\r\n    }\r\n\r\n    private async detachAudioNode(): Promise<void> {\r\n        if (!this.privIsAudioNodeDetached) {\r\n            this.privIsAudioNodeDetached = true;\r\n            if (this.privAudioNode) {\r\n                await this.privAudioNode.detach();\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}