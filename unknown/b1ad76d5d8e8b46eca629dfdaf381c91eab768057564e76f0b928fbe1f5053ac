{"version": 3, "sources": ["src/sdk/SpeechRecognitionCanceledEventArgs.ts"], "names": [], "mappings": "AAGA,OAAO,EACH,yBAAyB,EAC5B,MAAM,6BAA6B,CAAC;AAErC,qBAAa,kCAAmC,SAAQ,yBAAyB;CAChF", "file": "SpeechRecognitionCanceledEventArgs.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    CancellationEventArgsBase,\r\n} from \"./CancellationEventArgsBase\";\r\n\r\nexport class SpeechRecognitionCanceledEventArgs extends CancellationEventArgsBase {\r\n}\r\n"]}