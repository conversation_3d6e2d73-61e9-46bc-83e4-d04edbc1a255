{"version": 3, "sources": ["src/sdk/PronunciationAssessmentGradingSystem.ts"], "names": [], "mappings": "AAGA;;;;GAIG;AACH,oBAAY,oCAAoC;IAC5C;;;OAGG;IACH,SAAS,IAAI;IAEb;;;OAGG;IACH,WAAW,IAAA;CACd", "file": "PronunciationAssessmentGradingSystem.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\n/**\r\n * Defines the point system for pronunciation score calibration; default value is FivePoint.\r\n * Added in version 1.15.0\r\n * @class PronunciationAssessmentGradingSystem\r\n */\r\nexport enum PronunciationAssessmentGradingSystem {\r\n    /**\r\n     * Five point calibration\r\n     * @member PronunciationAssessmentGradingSystem.FivePoint\r\n     */\r\n    FivePoint = 1,\r\n\r\n    /**\r\n     * Hundred mark\r\n     * @member PronunciationAssessmentGradingSystem.HundredMark\r\n     */\r\n    HundredMark,\r\n}\r\n"]}