{"version": 3, "sources": ["src/sdk/SpeechRecognitionCanceledEventArgs.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;AAElC,yEAEqC;AAErC;IAAwD,sDAAyB;IAAjF;;IACA,CAAC;IAAD,yCAAC;AAAD,CADA,AACC,CADuD,qDAAyB,GAChF;AADY,gFAAkC", "file": "SpeechRecognitionCanceledEventArgs.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    CancellationEventArgsBase,\r\n} from \"./CancellationEventArgsBase\";\r\n\r\nexport class SpeechRecognitionCanceledEventArgs extends CancellationEventArgsBase {\r\n}\r\n"]}