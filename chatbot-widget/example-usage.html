<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Widget - Single File Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        .example-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .method {
            background: #fff;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .method h4 {
            margin-top: 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>🤖 Chatbot Widget - Single File Distribution</h1>
    
    <p>This page demonstrates how to use the chatbot widget as a single distributable file.</p>

    <div class="example-section">
        <h2>📦 Method 1: Auto-initialization with Data Attributes</h2>
        <p>The simplest way - just add data attributes to any element:</p>
        
        <div class="code">
&lt;!-- The widget will auto-initialize on page load --&gt;
&lt;div id="chatbot-container" 
     data-chatbot-widget
     data-directline-token="YOUR_TOKEN_HERE"
     data-user-id="user123"
     data-username="John Doe"
     data-position="bottom-right"
     data-minimized="true"
     data-debug="true"&gt;
&lt;/div&gt;

&lt;script src="./dist/chatbot-widget.umd.js"&gt;&lt;/script&gt;
        </div>
    </div>

    <div class="example-section">
        <h2>🔧 Method 2: Manual Initialization</h2>
        <p>For more control, initialize programmatically:</p>
        
        <div class="code">
&lt;div id="my-chatbot"&gt;&lt;/div&gt;

&lt;script src="./dist/chatbot-widget.umd.js"&gt;&lt;/script&gt;
&lt;script&gt;
  const widget = ChatbotWidget.init({
    containerId: 'my-chatbot',
    directLineToken: 'YOUR_TOKEN_HERE',
    userID: 'user123',
    username: 'John Doe',
    position: 'bottom-right',
    minimized: true,
    debug: true,
    onToggle: (isOpen) => console.log('Chat toggled:', isOpen),
    onError: (error) => console.error('Chat error:', error)
  });
&lt;/script&gt;
        </div>
    </div>

    <div class="example-section">
        <h2>🌍 Method 3: Environment Variables (Build Time)</h2>
        <p>Configure defaults at build time using .env file:</p>
        
        <div class="code">
# .env file
VITE_DIRECTLINE_TOKEN=your_token_here
VITE_USER_ID=default_user
VITE_USERNAME=Default User
VITE_DEBUG=true
        </div>
        
        <p>Then use without configuration:</p>
        <div class="code">
&lt;div id="chatbot"&gt;&lt;/div&gt;
&lt;script src="./dist/chatbot-widget.umd.js"&gt;&lt;/script&gt;
&lt;script&gt;
  // Uses environment variables as defaults
  ChatbotWidget.init({ containerId: 'chatbot' });
&lt;/script&gt;
        </div>
    </div>

    <h2>📚 API Reference</h2>

    <div class="method">
        <h4>ChatbotWidget.init(config)</h4>
        <p><strong>Parameters:</strong></p>
        <ul>
            <li><code>containerId</code> (string, optional): ID of container element. Default: 'chatbot-widget-container'</li>
            <li><code>directLineToken</code> (string): Direct Line token or secret</li>
            <li><code>userID</code> (string, optional): User identifier</li>
            <li><code>username</code> (string, optional): Display name for user</li>
            <li><code>position</code> (string, optional): 'bottom-right', 'bottom-left', 'top-right', 'top-left'</li>
            <li><code>minimized</code> (boolean, optional): Start minimized. Default: true</li>
            <li><code>debug</code> (boolean, optional): Enable debug logging</li>
            <li><code>styleOptions</code> (object, optional): Custom styling options</li>
            <li><code>onToggle</code> (function, optional): Callback when widget is toggled</li>
            <li><code>onError</code> (function, optional): Error callback</li>
        </ul>
        <p><strong>Returns:</strong> Widget instance with <code>destroy()</code> and <code>update()</code> methods</p>
    </div>

    <div class="method">
        <h4>ChatbotWidget.autoInit()</h4>
        <p>Automatically initializes all elements with <code>data-chatbot-widget</code> attribute.</p>
        <p><strong>Returns:</strong> Array of widget instances</p>
    </div>

    <h2>🎯 Live Demo</h2>
    <p>The chatbot widget should appear in the bottom-right corner of this page!</p>

    <!-- Auto-initialization example -->
    <div id="demo-chatbot" 
         data-chatbot-widget
         data-user-id="demo-user"
         data-username="Demo User"
         data-position="bottom-right"
         data-minimized="true"
         data-debug="true">
    </div>

    <!-- Load the widget -->
    <script src="./dist/chatbot-widget.umd.js"></script>
    
    <script>
        // Additional manual initialization example
        setTimeout(() => {
            console.log('Chatbot Widget loaded!');
            console.log('Available methods:', Object.keys(window.ChatbotWidget));
        }, 1000);
    </script>
</body>
</html>
