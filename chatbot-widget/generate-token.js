// Generate a Direct Line token from secret
const SECRET = "rD8nsFmme2HPerfuMuhnqxAzpGK4tXY9RG2mlOqq3ygcOgu7vJutJQQJ99BAACi5YpzAArohAAABAZBS2FVD.10uUUSUGercwr9x6MVOY28kQiZHNEa0cd88jORWQF7mYMzX0WapQJQQJ99BFAC5RqLJAArohAAABAZBS3eHt";

console.log('🔄 Generating Direct Line token from secret...\n');

fetch('https://directline.botframework.com/v3/directline/tokens/generate', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${SECRET}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    User: { Id: 'user123' }
  })
})
.then(response => {
  if (response.ok) {
    return response.json().then(data => {
      console.log('✅ SUCCESS! Generated a valid token:');
      console.log(`\n🎫 Your new token:`);
      console.log(`${data.token}\n`);
      
      console.log('📋 Token details:');
      console.log(`   Expires: ${data.expires_in} seconds from now`);
      console.log(`   Conversation ID: ${data.conversationId || 'Will be generated'}\n`);
      
      console.log('🔧 Next steps:');
      console.log('1. Copy the token above');
      console.log('2. Replace the directLineToken in src/main.tsx');
      console.log('3. Refresh your browser\n');
      
      console.log('💡 Pro tip: Tokens expire, but you can always generate new ones using this script!');
    });
  } else {
    return response.text().then(errorText => {
      console.error(`❌ Failed to generate token: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      
      if (response.status === 401) {
        console.error('\n🔑 Your secret key might be invalid or expired.');
        console.error('Get a fresh secret from Azure Portal:');
        console.error('1. Go to https://portal.azure.com');
        console.error('2. Navigate to your Bot Service');
        console.error('3. Go to Channels → Direct Line');
        console.error('4. Click "Show" next to Secret keys');
        console.error('5. Copy a fresh secret key');
      }
    });
  }
})
.catch(error => {
  console.error(`❌ Network Error: ${error.message}`);
  console.error('Check your internet connection and try again.');
});
