import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'ChatbotWidget',
      fileName: 'chatbot-widget',
      formats: ['umd']
    },
    rollupOptions: {
      external: [],
      output: {
        globals: {},
        // Ensure single file output
        inlineDynamicImports: true,
        manualChunks: undefined
      }
    },
    outDir: 'dist',
    // Optimize for single file distribution
    cssCodeSplit: false,
    // Inline all assets
    assetsInlineLimit: 100000000, // 100MB - inline everything
    // Minify for smaller file size
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false, // Keep console logs for debugging
        drop_debugger: true
      }
    }
  },
  define: {
    'process.env.NODE_ENV': '"production"'
  }
})
