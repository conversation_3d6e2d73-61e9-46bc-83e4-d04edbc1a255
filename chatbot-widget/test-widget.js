// Simple test script to validate the chatbot widget functionality
// This can be run in a browser console to test the widget

console.log('🧪 Starting Chatbot Widget Tests...');

// Test 1: Check if the widget files exist and are accessible
async function testFileAccessibility() {
    console.log('📁 Testing file accessibility...');
    
    try {
        // Test CSS file
        const cssResponse = await fetch('./dist/chatbot-widget.css');
        if (cssResponse.ok) {
            console.log('✅ CSS file is accessible');
        } else {
            console.error('❌ CSS file not accessible');
            return false;
        }
        
        // Test JS file
        const jsResponse = await fetch('./dist/chatbot-widget.umd.cjs');
        if (jsResponse.ok) {
            console.log('✅ JavaScript bundle is accessible');
        } else {
            console.error('❌ JavaScript bundle not accessible');
            return false;
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error testing file accessibility:', error);
        return false;
    }
}

// Test 2: Check if ChatbotWidget is available globally
function testGlobalAvailability() {
    console.log('🌐 Testing global availability...');
    
    if (typeof window.ChatbotWidget !== 'undefined') {
        console.log('✅ ChatbotWidget is available globally');
        
        if (typeof window.ChatbotWidget.init === 'function') {
            console.log('✅ ChatbotWidget.init function is available');
            return true;
        } else {
            console.error('❌ ChatbotWidget.init function not found');
            return false;
        }
    } else {
        console.error('❌ ChatbotWidget not available globally');
        return false;
    }
}

// Test 3: Test widget initialization
function testWidgetInitialization() {
    console.log('🚀 Testing widget initialization...');
    
    try {
        // Create a test container
        const testContainer = document.createElement('div');
        testContainer.id = 'test-chatbot-container';
        document.body.appendChild(testContainer);
        
        // Initialize widget with test configuration
        const widget = window.ChatbotWidget.init({
            containerId: 'test-chatbot-container',
            directLineToken: 'test-token',
            userID: 'test-user',
            username: 'Test User',
            position: 'bottom-right',
            minimized: true,
            onToggle: function(isOpen) {
                console.log('📝 Widget toggle event fired:', isOpen);
            }
        });
        
        if (widget && typeof widget.destroy === 'function') {
            console.log('✅ Widget initialized successfully');
            
            // Clean up test widget
            widget.destroy();
            document.body.removeChild(testContainer);
            
            return true;
        } else {
            console.error('❌ Widget initialization failed');
            return false;
        }
    } catch (error) {
        console.error('❌ Error during widget initialization:', error);
        return false;
    }
}

// Test 4: Test different positions
function testPositioning() {
    console.log('📍 Testing widget positioning...');
    
    const positions = ['bottom-right', 'bottom-left', 'top-right', 'top-left'];
    let allPositionsWork = true;
    
    positions.forEach((position, index) => {
        try {
            const testContainer = document.createElement('div');
            testContainer.id = `test-position-${index}`;
            document.body.appendChild(testContainer);
            
            const widget = window.ChatbotWidget.init({
                containerId: testContainer.id,
                directLineToken: 'test-token',
                position: position,
                minimized: true
            });
            
            if (widget) {
                console.log(`✅ Position "${position}" works`);
                widget.destroy();
            } else {
                console.error(`❌ Position "${position}" failed`);
                allPositionsWork = false;
            }
            
            document.body.removeChild(testContainer);
        } catch (error) {
            console.error(`❌ Error testing position "${position}":`, error);
            allPositionsWork = false;
        }
    });
    
    return allPositionsWork;
}

// Run all tests
async function runAllTests() {
    console.log('🎯 Running comprehensive widget tests...\n');
    
    const results = {
        fileAccessibility: await testFileAccessibility(),
        globalAvailability: testGlobalAvailability(),
        initialization: testWidgetInitialization(),
        positioning: testPositioning()
    };
    
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    Object.entries(results).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    });
    
    const allPassed = Object.values(results).every(result => result === true);
    
    console.log('\n' + '='.repeat(40));
    console.log(`🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
    console.log('='.repeat(40));
    
    return allPassed;
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runAllTests, testFileAccessibility, testGlobalAvailability, testWidgetInitialization, testPositioning };
} else {
    // Auto-run tests if in browser
    window.runChatbotWidgetTests = runAllTests;
    console.log('💡 Run "runChatbotWidgetTests()" to execute all tests');
}
