import React from 'react';
import { createRoot } from 'react-dom/client';
import ChatbotWidget from './ChatbotWidget';

export { default as ChatbotWidget } from './ChatbotWidget';
export type { ChatbotWidgetProps } from './ChatbotWidget';

// Configuration interface
export interface ChatbotConfig {
  containerId?: string;
  directLineToken?: string;
  userID?: string;
  username?: string;
  locale?: string;
  styleOptions?: any;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  minimized?: boolean;
  debug?: boolean;
  onToggle?: (isOpen: boolean) => void;
  onError?: (error: any) => void;
}

// Function to initialize the chatbot widget on any website
export function initChatbotWidget(config: ChatbotConfig = {}) {
  const {
    containerId = 'chatbot-widget-container',
    directLineToken = (import.meta.env?.VITE_DIRECTLINE_TOKEN) || '',
    userID = (import.meta.env?.VITE_USER_ID) || 'user',
    username = (import.meta.env?.VITE_USERNAME) || 'User',
    locale = 'en-US',
    styleOptions = {},
    position = 'bottom-right',
    minimized = true,
    debug = (import.meta.env?.VITE_DEBUG === 'true'),
    onToggle,
    onError = (error) => console.error('Chatbot Widget Error:', error)
  } = config;

  if (!directLineToken) {
    throw new Error('Direct Line token is required. Provide it in config or set VITE_DIRECTLINE_TOKEN environment variable.');
  }

  // Create container if it doesn't exist
  let container = document.getElementById(containerId);
  if (!container) {
    container = document.createElement('div');
    container.id = containerId;
    document.body.appendChild(container);
  }

  // Render the widget
  const root = createRoot(container);
  root.render(
    React.createElement(ChatbotWidget, {
      directLineToken,
      userID,
      username,
      locale,
      styleOptions,
      position,
      minimized,
      debug,
      onToggle,
      onError
    })
  );

  return {
    destroy: () => {
      root.unmount();
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
      }
    },
    update: (newConfig: Partial<ChatbotConfig>) => {
      const updatedProps = {
        directLineToken, userID, username, locale, styleOptions,
        position, minimized, debug, onToggle, onError,
        ...newConfig
      };
      root.render(React.createElement(ChatbotWidget, updatedProps));
    }
  };
}

// Auto-initialization function that looks for data attributes
export function autoInit() {
  const containers = document.querySelectorAll('[data-chatbot-widget]');
  const instances: any[] = [];

  containers.forEach((container) => {
    const config: ChatbotConfig = {
      containerId: container.id || 'chatbot-widget-container',
      directLineToken: container.getAttribute('data-directline-token') || undefined,
      userID: container.getAttribute('data-user-id') || undefined,
      username: container.getAttribute('data-username') || undefined,
      position: (container.getAttribute('data-position') as any) || 'bottom-right',
      minimized: container.getAttribute('data-minimized') !== 'false',
      debug: container.getAttribute('data-debug') === 'true'
    };

    try {
      const instance = initChatbotWidget(config);
      instances.push(instance);
    } catch (error) {
      console.error('Failed to initialize chatbot widget:', error);
    }
  });

  return instances;
}

// Global initialization for script tag usage
if (typeof window !== 'undefined') {
  (window as any).ChatbotWidget = {
    init: initChatbotWidget,
    autoInit: autoInit,
    Component: ChatbotWidget
  };

  // Auto-initialize on DOM ready if auto-init containers are found
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      if (document.querySelectorAll('[data-chatbot-widget]').length > 0) {
        autoInit();
      }
    });
  } else {
    // DOM is already ready
    if (document.querySelectorAll('[data-chatbot-widget]').length > 0) {
      autoInit();
    }
  }
}
