import React from 'react';
import { createRoot } from 'react-dom/client';
import ChatbotWidget from './ChatbotWidget';

export { default as ChatbotWidget } from './ChatbotWidget';
export type { ChatbotWidgetProps } from './ChatbotWidget';

// Function to initialize the chatbot widget on any website
export function initChatbotWidget(config: {
  containerId?: string;
  directLineToken: string;
  userID?: string;
  username?: string;
  locale?: string;
  styleOptions?: any;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  minimized?: boolean;
  onToggle?: (isOpen: boolean) => void;
}) {
  const {
    containerId = 'chatbot-widget-container',
    directLineToken,
    userID = 'user',
    username = 'User',
    locale = 'en-US',
    styleOptions = {},
    position = 'bottom-right',
    minimized = true,
    onToggle
  } = config;

  // Create container if it doesn't exist
  let container = document.getElementById(containerId);
  if (!container) {
    container = document.createElement('div');
    container.id = containerId;
    document.body.appendChild(container);
  }

  // Render the widget
  const root = createRoot(container);
  root.render(
    React.createElement(ChatbotWidget, {
      directLineToken,
      userID,
      username,
      locale,
      styleOptions,
      position,
      minimized,
      onToggle
    })
  );

  return {
    destroy: () => {
      root.unmount();
      if (container && container.parentNode) {
        container.parentNode.removeChild(container);
      }
    }
  };
}

// Global initialization for script tag usage
if (typeof window !== 'undefined') {
  (window as any).ChatbotWidget = {
    init: initChatbotWidget,
    Component: ChatbotWidget
  };
}
