import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import ChatbotWidget from './ChatbotWidget'

// Demo page for testing the chatbot widget
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Chatbot Widget Demo</h1>
      <p>This is a demo page to test the chatbot widget. The widget should appear in the bottom-right corner.</p>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>

      <ChatbotWidget
        directLineToken="rD8nsFmme2HPerfuMuhnqxAzpGK4tXY9RG2mlOqq3ygcOgu7vJutJQQJ99BAACi5YpzAArohAAABAZBS2FVD.10uUUSUGercwr9x6MVOY28kQiZHNEa0cd88jORWQF7mYMzX0WapQJQQJ99BFAC5RqLJAArohAAABAZBS3eHt"
        userID="demo-user"
        username="Demo User"
        position="bottom-right"
        minimized={true}
        debug={true}
        onError={(error) => {
          console.error('Chatbot Widget Error:', error);
          alert(`Chatbot Error: ${error}`);
        }}
      />
    </div>
  </StrictMode>,
)
