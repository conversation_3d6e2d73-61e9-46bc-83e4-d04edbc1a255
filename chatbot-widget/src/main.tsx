import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import ChatbotWidget from './ChatbotWidget'

// Demo page for testing the chatbot widget
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Chatbot Widget Demo</h1>
      <p>This is a demo page to test the chatbot widget. The widget should appear in the bottom-right corner.</p>
      <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>

      <ChatbotWidget
        directLineToken="62X4VdknW7epEzf091L2isZbcWwJ6deBbEH8ZFhuuCyIq4uZpiALJQQJ99BGACi5YpzAArohAAABAZBS4Y7x.2U3ah1vf6QutJ8Ntz64tA6x6idXYYHKNGyvAhM4Y3PtsEwqm1I12JQQJ99BGACi5YpzAArohAAABAZBS3Up7"
        userID="demo-user"
        username="Demo User"
        position="bottom-right"
        minimized={true}
        debug={true}
        onError={(error) => {
          console.error('Chatbot Widget Error:', error);
          alert(`Chatbot Error: ${error}`);
        }}
      />
    </div>
  </StrictMode>,
)
