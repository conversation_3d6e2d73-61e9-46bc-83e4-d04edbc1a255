.chatbot-widget {
  position: fixed;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Position variants */
.chatbot-widget--bottom-right {
  bottom: 20px;
  right: 20px;
}

.chatbot-widget--bottom-left {
  bottom: 20px;
  left: 20px;
}

.chatbot-widget--top-right {
  top: 20px;
  right: 20px;
}

.chatbot-widget--top-left {
  top: 20px;
  left: 20px;
}

/* Toggle button */
.chatbot-widget__toggle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.chatbot-widget__toggle:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 120, 212, 0.4);
}

.chatbot-widget__toggle:active {
  transform: scale(0.95);
}

/* Chat window */
.chatbot-widget__window {
  position: absolute;
  width: 600px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

/* Position the window relative to toggle button */
.chatbot-widget--bottom-right .chatbot-widget__window,
.chatbot-widget--top-right .chatbot-widget__window {
  right: 0;
}

.chatbot-widget--bottom-left .chatbot-widget__window,
.chatbot-widget--top-left .chatbot-widget__window {
  left: 0;
}

.chatbot-widget--bottom-right .chatbot-widget__window,
.chatbot-widget--bottom-left .chatbot-widget__window {
  bottom: 70px;
}

.chatbot-widget--top-right .chatbot-widget__window,
.chatbot-widget--top-left .chatbot-widget__window {
  top: 70px;
}

/* Header */
.chatbot-widget__header {
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e1e1e1;
}

.chatbot-widget__header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chatbot-widget__close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.chatbot-widget__close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Content area */
.chatbot-widget__content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Override WebChat styles for compact view */
.chatbot-widget__content > div {
  height: 100% !important;
}

/* Animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .chatbot-widget__window {
    width: calc(100vw - 40px);
    height: calc(100vh - 140px);
    max-width: 350px;
    max-height: 500px;
  }
  
  .chatbot-widget--bottom-right,
  .chatbot-widget--bottom-left {
    left: 20px;
    right: 20px;
  }
  
  .chatbot-widget--bottom-right .chatbot-widget__window,
  .chatbot-widget--bottom-left .chatbot-widget__window {
    left: 0;
    right: 0;
  }
}

/* Error and loading states */
.chatbot-widget__error {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  margin: 20px;
  color: #c53030;
}

.chatbot-widget__error .error-icon {
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.chatbot-widget__error .error-message {
  flex: 1;
}

.chatbot-widget__error .error-message strong {
  display: block;
  margin-bottom: 8px;
  font-size: 16px;
}

.chatbot-widget__error .error-message p {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.chatbot-widget__error details {
  margin-top: 12px;
  font-size: 12px;
  font-family: monospace;
}

.chatbot-widget__error summary {
  cursor: pointer;
  font-weight: bold;
  margin-bottom: 8px;
}

.chatbot-widget__connecting {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.chatbot-widget__connecting .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.chatbot-widget__connecting p {
  margin: 0;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Ensure WebChat components fit properly */
.chatbot-widget__content .webchat__basic-transcript {
  max-height: none !important;
}

.chatbot-widget__content .webchat__send-box {
  border-top: 1px solid #e1e1e1 !important;
}
