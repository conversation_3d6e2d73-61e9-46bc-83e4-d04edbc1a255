import React, { useState, useEffect, useRef } from 'react';
import { createDirectLine, createStore } from 'botframework-webchat';
import ReactWebChat from 'botframework-webchat';
import './ChatbotWidget.css';

export interface ChatbotWidgetProps {
  directLineToken: string;
  userID?: string;
  username?: string;
  locale?: string;
  styleOptions?: any;
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  minimized?: boolean;
  onToggle?: (isOpen: boolean) => void;
  onError?: (error: any) => void;
  debug?: boolean;
}

const ChatbotWidget: React.FC<ChatbotWidgetProps> = ({
  directLineToken,
  userID = 'user',
  username = 'User',
  locale = 'en-US',
  styleOptions = {},
  className = '',
  position = 'bottom-right',
  minimized = true,
  onToggle,
  onError,
  debug = false
}) => {
  const [isOpen, setIsOpen] = useState(!minimized);
  const [directLine, setDirectLine] = useState<any>(null);
  const [store, setStore] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error' | 'disconnected'>('connecting');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const webChatRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (directLineToken) {
      try {
        if (debug) {
          console.log('ChatbotWidget: Initializing Direct Line connection...');
        }

        // Check if this is a secret (longer format) or token (JWT format)
        const isSecret = directLineToken.length > 100 && !directLineToken.includes('.');

        const dl = createDirectLine(isSecret ? {
          secret: directLineToken,
          domain: 'https://directline.botframework.com/v3/directline',
          pollingInterval: 1000
        } : {
          token: directLineToken,
          domain: 'https://directline.botframework.com/v3/directline',
          pollingInterval: 1000
        });

        if (debug) {
          console.log('ChatbotWidget: Using', isSecret ? 'secret' : 'token', 'authentication');
        }
        const st = createStore();

        // Add connection event listeners
        dl.connectionStatus$.subscribe({
          next: (connectionStatus: number) => {
            if (debug) {
              console.log('ChatbotWidget: Connection status changed:', connectionStatus);
            }

            switch (connectionStatus) {
              case 0: // Uninitialized
                setConnectionStatus('connecting');
                break;
              case 1: // Connecting
                setConnectionStatus('connecting');
                break;
              case 2: // Online
                setConnectionStatus('connected');
                setErrorMessage('');
                break;
              case 3: // ExpiredToken
                setConnectionStatus('error');
                setErrorMessage('Token expired. Please refresh the page.');
                onError?.('Token expired');
                break;
              case 4: // FailedToConnect
                setConnectionStatus('error');
                setErrorMessage('Failed to connect to bot service.');
                onError?.('Failed to connect');
                break;
              case 5: // Ended
                setConnectionStatus('disconnected');
                break;
            }
          },
          error: (error: any) => {
            if (debug) {
              console.error('ChatbotWidget: Connection error:', error);
            }
            setConnectionStatus('error');
            setErrorMessage('Connection error occurred.');
            onError?.(error);
          }
        });

        setDirectLine(dl);
        setStore(st);

      } catch (error) {
        if (debug) {
          console.error('ChatbotWidget: Initialization error:', error);
        }
        setConnectionStatus('error');
        setErrorMessage('Failed to initialize chat widget.');
        onError?.(error);
      }
    }
  }, [directLineToken, debug, onError]);

  const handleToggle = () => {
    const newIsOpen = !isOpen;
    setIsOpen(newIsOpen);
    onToggle?.(newIsOpen);
  };

  const defaultStyleOptions = {
    backgroundColor: 'white',
    primaryFont: '14px Segoe UI, sans-serif',
    bubbleBackground: '#0078d4',
    bubbleFromUserBackground: '#0078d4',
    bubbleFromUserTextColor: 'white',
    bubbleTextColor: 'white',
    sendBoxBackground: 'white',
    sendBoxTextColor: 'black',
    sendBoxBorderColor: '#e1e1e1',
    sendBoxPlaceholderColor: '#767676',
    ...styleOptions
  };

  if (!directLine || !store) {
    return null;
  }

  return (
    <div className={`chatbot-widget ${className} chatbot-widget--${position}`}>
      {/* Chat Toggle Button */}
      <button
        className="chatbot-widget__toggle"
        onClick={handleToggle}
        aria-label={isOpen ? 'Close chat' : 'Open chat'}
      >
        {isOpen ? (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        ) : (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
          </svg>
        )}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="chatbot-widget__window">
          <div className="chatbot-widget__header">
            <h3>Chat Support</h3>
            <button
              className="chatbot-widget__close"
              onClick={handleToggle}
              aria-label="Close chat"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
          <div className="chatbot-widget__content" ref={webChatRef}>
            {connectionStatus === 'error' && (
              <div className="chatbot-widget__error">
                <div className="error-icon">⚠️</div>
                <div className="error-message">
                  <strong>Connection Error</strong>
                  <p>{errorMessage}</p>
                  {debug && (
                    <details>
                      <summary>Debug Information</summary>
                      <p>Token: {directLineToken.substring(0, 20)}...</p>
                      <p>Status: {connectionStatus}</p>
                    </details>
                  )}
                </div>
              </div>
            )}

            {connectionStatus === 'connecting' && (
              <div className="chatbot-widget__connecting">
                <div className="spinner"></div>
                <p>Connecting to chat service...</p>
              </div>
            )}

            {(connectionStatus === 'connected' || connectionStatus === 'disconnected') && (
              <ReactWebChat
                directLine={directLine}
                store={store}
                userID={userID}
                username={username}
                locale={locale}
                styleOptions={defaultStyleOptions}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatbotWidget;
