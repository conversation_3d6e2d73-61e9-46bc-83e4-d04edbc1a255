<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Widget Integration Example</title>
    <link rel="stylesheet" href="./dist/chatbot-widget.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            line-height: 1.6;
            color: #666;
            margin-bottom: 15px;
        }
        .demo-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chatbot Widget Integration Example</h1>
        
        <p>This page demonstrates how to integrate the chatbot widget into any website. The widget should appear in the bottom-right corner of your screen.</p>
        
        <div class="demo-section">
            <h2>Features Demonstrated:</h2>
            <ul>
                <li>✅ Compact chatbox widget with toggle functionality</li>
                <li>✅ Responsive design that works on mobile and desktop</li>
                <li>✅ Microsoft Bot Framework WebChat integration</li>
                <li>✅ Customizable positioning and styling</li>
                <li>✅ Easy script tag integration</li>
            </ul>
        </div>

        <h2>Integration Code</h2>
        <p>Here's the code used to add the chatbot widget to this page:</p>
        
        <div class="code-block">
&lt;!-- Include the CSS file --&gt;
&lt;link rel="stylesheet" href="./dist/chatbot-widget.css"&gt;

&lt;!-- Include the JavaScript bundle --&gt;
&lt;script src="./dist/chatbot-widget.umd.cjs"&gt;&lt;/script&gt;

&lt;!-- Initialize the widget --&gt;
&lt;script&gt;
    ChatbotWidget.init({
        directLineToken: 'YOUR_DIRECT_LINE_TOKEN',
        userID: 'demo-user-' + Date.now(),
        username: 'Website Visitor',
        position: 'bottom-right',
        minimized: true,
        onToggle: function(isOpen) {
            console.log('Chat widget toggled:', isOpen);
        }
    });
&lt;/script&gt;
        </div>

        <h2>Try It Out</h2>
        <p>Click the chat button in the bottom-right corner to open the chatbot widget. You can:</p>
        <ul>
            <li>Send messages to test the Bot Framework integration</li>
            <li>Minimize and maximize the chat window</li>
            <li>See how it responds on different screen sizes</li>
        </ul>

        <p><strong>Note:</strong> This demo uses a sample Direct Line token. In production, you would replace this with your own bot's token.</p>

        <div class="demo-section">
            <h3>Next Steps</h3>
            <p>To integrate this widget into your own website:</p>
            <ol>
                <li>Copy the <code>dist/chatbot-widget.umd.cjs</code> and <code>dist/chatbot-widget.css</code> files to your website</li>
                <li>Include both files in your HTML as shown above</li>
                <li>Replace the <code>directLineToken</code> with your own Bot Framework token</li>
                <li>Customize the styling and positioning as needed</li>
            </ol>
        </div>
    </div>

    <!-- Include the chatbot widget -->
    <script src="./dist/chatbot-widget.umd.cjs"></script>
    <script>
        // Initialize the chatbot widget
        ChatbotWidget.init({
            directLineToken: 'rD8nsFmme2HPerfuMuhnqxAzpGK4tXY9RG2mlOqq3ygcOgu7vJutJQQJ99BAACi5YpzAArohAAABAZBS2FVD.10uUUSUGercwr9x6MVOY28kQiZHNEa0cd88jORWQF7mYMzX0WapQJQQJ99BFAC5RqLJAArohAAABAZBS3eHt',
            userID: 'demo-user-' + Date.now(),
            username: 'Website Visitor',
            position: 'bottom-right',
            minimized: true,
            onToggle: function(isOpen) {
                console.log('Chat widget toggled:', isOpen);
            }
        });
    </script>
</body>
</html>
