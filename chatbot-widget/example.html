<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot Widget - Single File Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        .example {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
            font-size: 14px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
    <!-- Load the CSS -->
    <link rel="stylesheet" href="./dist/chatbot-widget.css">
</head>
<body>
    <h1>🤖 Chatbot Widget - Single File Distribution</h1>
    
    <div class="success">
        ✅ <strong>Success!</strong> The chatbot widget has been built as a single distributable file.
    </div>

    <p>This page demonstrates how to use the chatbot widget from the single UMD file.</p>

    <div class="example">
        <h2>📦 Method 1: Auto-initialization with Data Attributes</h2>
        <p>The widget will automatically initialize when it finds elements with <code>data-chatbot-widget</code>:</p>
        
        <div class="code">
&lt;div id="chatbot-auto" 
     data-chatbot-widget
     data-user-id="auto-user"
     data-username="Auto User"
     data-position="bottom-right"
     data-minimized="true"
     data-debug="true"&gt;
&lt;/div&gt;
        </div>
    </div>

    <div class="example">
        <h2>🔧 Method 2: Manual Initialization</h2>
        <p>Initialize programmatically for more control:</p>
        
        <div class="code">
const widget = ChatbotWidget.init({
  containerId: 'my-chatbot',
  userID: 'manual-user',
  username: 'Manual User',
  position: 'bottom-left',
  minimized: false,
  debug: true,
  onToggle: (isOpen) => console.log('Chat toggled:', isOpen),
  onError: (error) => console.error('Chat error:', error)
});
        </div>
        
        <button onclick="initManualWidget()">Initialize Manual Widget</button>
        <button onclick="destroyManualWidget()">Destroy Manual Widget</button>
    </div>

    <div class="example">
        <h2>📁 Files Generated</h2>
        <ul>
            <li><code>dist/chatbot-widget.umd.cjs</code> - Single JavaScript file (~3.7MB, includes all dependencies)</li>
            <li><code>dist/chatbot-widget.css</code> - Stylesheet (~3.8KB)</li>
        </ul>
        <p><strong>Total size:</strong> ~3.7MB (compressed to ~966KB with gzip)</p>
    </div>

    <div class="example">
        <h2>🚀 Usage in Any Website</h2>
        <p>To use this widget on any website, just include these files:</p>
        
        <div class="code">
&lt;!-- Include CSS --&gt;
&lt;link rel="stylesheet" href="chatbot-widget.css"&gt;

&lt;!-- Include JavaScript --&gt;
&lt;script src="chatbot-widget.umd.cjs"&gt;&lt;/script&gt;

&lt;!-- Auto-init container --&gt;
&lt;div data-chatbot-widget 
     data-user-id="your-user-id"
     data-username="Your User"&gt;
&lt;/div&gt;
        </div>
    </div>

    <h2>🎯 Live Demo</h2>
    <p>The chatbot widget should appear in the bottom-right corner!</p>

    <!-- Auto-initialization container -->
    <div id="chatbot-auto" 
         data-chatbot-widget
         data-user-id="demo-user"
         data-username="Demo User"
         data-position="bottom-right"
         data-minimized="true"
         data-debug="true">
    </div>

    <!-- Manual initialization container -->
    <div id="manual-chatbot"></div>

    <!-- Load the widget script -->
    <script src="./dist/chatbot-widget.umd.cjs"></script>
    
    <script>
        let manualWidget = null;

        function initManualWidget() {
            if (manualWidget) {
                console.log('Manual widget already exists');
                return;
            }

            try {
                manualWidget = ChatbotWidget.init({
                    containerId: 'manual-chatbot',
                    userID: 'manual-user',
                    username: 'Manual User',
                    position: 'bottom-left',
                    minimized: false,
                    debug: true,
                    onToggle: (isOpen) => console.log('Manual widget toggled:', isOpen),
                    onError: (error) => console.error('Manual widget error:', error)
                });
                console.log('Manual widget initialized!');
            } catch (error) {
                console.error('Failed to initialize manual widget:', error);
                alert('Error: ' + error.message);
            }
        }

        function destroyManualWidget() {
            if (manualWidget) {
                manualWidget.destroy();
                manualWidget = null;
                console.log('Manual widget destroyed!');
            } else {
                console.log('No manual widget to destroy');
            }
        }

        // Log when the widget is loaded
        window.addEventListener('load', () => {
            console.log('🤖 Chatbot Widget loaded!');
            console.log('Available methods:', Object.keys(window.ChatbotWidget));
            
            // Check if auto-init worked
            setTimeout(() => {
                const autoContainer = document.getElementById('chatbot-auto');
                if (autoContainer && autoContainer.children.length > 0) {
                    console.log('✅ Auto-initialization successful!');
                } else {
                    console.log('⚠️ Auto-initialization may have failed - check console for errors');
                }
            }, 2000);
        });
    </script>
</body>
</html>
