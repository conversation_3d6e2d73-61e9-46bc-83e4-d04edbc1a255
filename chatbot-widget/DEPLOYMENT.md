# 🚀 Deployment Guide

This guide explains how to deploy and use the chatbot widget single file distribution.

## 📦 Files to Deploy

After running `npm run build`, you'll have these files in the `dist/` folder:

```
dist/
├── chatbot-widget.umd.cjs  (~3.7MB, ~966KB gzipped)
└── chatbot-widget.css      (~3.8KB, ~1.2KB gzipped)
```

## 🌐 Deployment Options

### Option 1: CDN Deployment (Recommended)

1. **Upload files to your CDN:**
   ```
   https://your-cdn.com/chatbot-widget.umd.cjs
   https://your-cdn.com/chatbot-widget.css
   ```

2. **Use in any website:**
   ```html
   <link rel="stylesheet" href="https://your-cdn.com/chatbot-widget.css">
   <script src="https://your-cdn.com/chatbot-widget.umd.cjs"></script>
   
   <div data-chatbot-widget 
        data-user-id="visitor"
        data-username="Website Visitor">
   </div>
   ```

### Option 2: Self-Hosted

1. **Copy files to your web server:**
   ```bash
   cp dist/chatbot-widget.umd.cjs /var/www/html/js/
   cp dist/chatbot-widget.css /var/www/html/css/
   ```

2. **Include in your pages:**
   ```html
   <link rel="stylesheet" href="/css/chatbot-widget.css">
   <script src="/js/chatbot-widget.umd.cjs"></script>
   ```

### Option 3: Inline Embedding

For maximum portability, you can inline the CSS and reference the JS file:

```html
<style>
/* Paste contents of chatbot-widget.css here */
</style>

<script src="chatbot-widget.umd.cjs"></script>
```

## 🔧 Configuration Methods

### Method 1: Environment Variables (Build Time)

Set defaults when building:

```bash
# .env file
VITE_DIRECTLINE_TOKEN=your_token_here
VITE_USER_ID=default_user
VITE_USERNAME=Default User
VITE_DEBUG=false

# Build with these defaults
npm run build
```

Then use without configuration:
```html
<div data-chatbot-widget></div>
```

### Method 2: Data Attributes (Runtime)

```html
<div data-chatbot-widget
     data-directline-token="your_token"
     data-user-id="user123"
     data-username="John Doe"
     data-position="bottom-right"
     data-minimized="true"
     data-debug="false">
</div>
```

### Method 3: JavaScript API (Runtime)

```javascript
ChatbotWidget.init({
  directLineToken: 'your_token',
  userID: 'user123',
  username: 'John Doe',
  position: 'bottom-right',
  minimized: true,
  debug: false
});
```

## 🔒 Security Considerations

### Direct Line Tokens

**Development:**
```javascript
// OK for development - using secret directly
ChatbotWidget.init({
  directLineToken: 'your_secret_key'
});
```

**Production:**
```javascript
// Better for production - generate tokens server-side
fetch('/api/bot-token')
  .then(response => response.json())
  .then(data => {
    ChatbotWidget.init({
      directLineToken: data.token
    });
  });
```

### Content Security Policy (CSP)

If you use CSP, add these directives:

```
script-src 'self' 'unsafe-inline' https://directline.botframework.com;
connect-src 'self' https://directline.botframework.com wss://directline.botframework.com;
img-src 'self' data: https:;
```

## 📊 Performance Optimization

### Gzip Compression

Enable gzip compression on your server:

```nginx
# Nginx
location ~* \.(js|css)$ {
    gzip on;
    gzip_types text/css application/javascript;
}
```

```apache
# Apache
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css application/javascript
</IfModule>
```

### Caching Headers

Set appropriate cache headers:

```nginx
# Nginx
location ~* \.(js|css)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### Lazy Loading

Load the widget only when needed:

```javascript
// Load widget when user scrolls down or clicks a button
function loadChatWidget() {
  if (!window.ChatbotWidget) {
    const script = document.createElement('script');
    script.src = 'chatbot-widget.umd.cjs';
    script.onload = () => {
      ChatbotWidget.init({ /* config */ });
    };
    document.head.appendChild(script);
  }
}

// Trigger on user interaction
document.getElementById('chat-button').onclick = loadChatWidget;
```

## 🧪 Testing Deployment

### Quick Test Script

```html
<!DOCTYPE html>
<html>
<head>
    <title>Chatbot Widget Test</title>
    <link rel="stylesheet" href="chatbot-widget.css">
</head>
<body>
    <h1>Testing Chatbot Widget</h1>
    
    <div data-chatbot-widget 
         data-user-id="test-user"
         data-username="Test User"
         data-debug="true">
    </div>
    
    <script src="chatbot-widget.umd.cjs"></script>
    <script>
        window.addEventListener('load', () => {
            console.log('Widget loaded:', !!window.ChatbotWidget);
            
            setTimeout(() => {
                const container = document.querySelector('[data-chatbot-widget]');
                console.log('Widget initialized:', container.children.length > 0);
            }, 2000);
        });
    </script>
</body>
</html>
```

### Troubleshooting

**Widget doesn't appear:**
1. Check browser console for errors
2. Verify files are loading (Network tab)
3. Check Direct Line token validity
4. Enable debug mode: `data-debug="true"`

**Connection issues:**
1. Verify Azure Bot Service is running
2. Check Direct Line channel configuration
3. Test token with: `curl -H "Authorization: Bearer YOUR_TOKEN" https://directline.botframework.com/v3/directline/conversations`

## 📈 Analytics Integration

Track widget usage:

```javascript
ChatbotWidget.init({
  directLineToken: 'your_token',
  onToggle: (isOpen) => {
    // Google Analytics
    gtag('event', 'chatbot_toggle', {
      'event_category': 'engagement',
      'event_label': isOpen ? 'opened' : 'closed'
    });
    
    // Custom analytics
    analytics.track('Chatbot Toggled', { isOpen });
  },
  onError: (error) => {
    // Error tracking
    console.error('Chatbot error:', error);
    analytics.track('Chatbot Error', { error: error.message });
  }
});
```

## 🔄 Updates

To update the widget:

1. Build new version: `npm run build`
2. Replace files on your server/CDN
3. Update cache-busting parameters if needed:
   ```html
   <script src="chatbot-widget.umd.cjs?v=1.1.0"></script>
   ```

## 📞 Support

For deployment issues:
- Check browser console with debug mode enabled
- Verify all files are accessible via HTTP
- Test Direct Line connectivity separately
- Review Azure Bot Service logs
