# Chatbot Widget

A reusable TypeScript/React chatbot widget that wraps Microsoft Bot Framework WebChat functionality in a compact, embeddable format.

## Features

- 🚀 **Easy Integration**: Single file distribution for easy embedding
- 💬 **Compact Design**: Minimizable chatbox widget that doesn't interfere with your site
- 🎨 **Customizable**: Configurable styling, positioning, and behavior
- 📱 **Responsive**: Works on desktop and mobile devices
- 🔧 **TypeScript**: Full TypeScript support with type definitions
- 🤖 **Bot Framework**: Full compatibility with Microsoft Bot Framework

## Quick Start

### Option 1: Script Tag Integration (Recommended for most websites)

1. Download the built files from the `dist/` folder:
   - `chatbot-widget.umd.cjs` - The main JavaScript bundle
   - `chatbot-widget.css` - Required styles

2. Include the files in your HTML:

```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="path/to/chatbot-widget.css">
</head>
<body>
    <!-- Your website content -->

    <script src="path/to/chatbot-widget.umd.cjs"></script>
    <script>
        // Initialize the chatbot widget
        ChatbotWidget.init({
            directLineToken: 'YOUR_DIRECT_LINE_TOKEN',
            userID: 'user-123',
            username: 'Website Visitor',
            position: 'bottom-right',
            minimized: true
        });
    </script>
</body>
</html>
```

### Option 2: NPM Package Integration (For React/Node.js projects)

```bash
npm install ./path/to/chatbot-widget
```

```tsx
import React from 'react';
import { ChatbotWidget } from 'chatbot-widget';

function App() {
    return (
        <div>
            {/* Your app content */}

            <ChatbotWidget
                directLineToken="YOUR_DIRECT_LINE_TOKEN"
                userID="user-123"
                username="App User"
                position="bottom-right"
                minimized={true}
            />
        </div>
    );
}
```

## Configuration Options

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `directLineToken` | `string` | **Required** | Your Bot Framework Direct Line token |
| `userID` | `string` | `'user'` | Unique identifier for the user |
| `username` | `string` | `'User'` | Display name for the user |
| `locale` | `string` | `'en-US'` | Language locale for the chat |
| `position` | `'bottom-right' \| 'bottom-left' \| 'top-right' \| 'top-left'` | `'bottom-right'` | Widget position on screen |
| `minimized` | `boolean` | `true` | Whether the widget starts minimized |
| `styleOptions` | `object` | `{}` | Custom styling options for WebChat |
| `onToggle` | `(isOpen: boolean) => void` | `undefined` | Callback when widget is opened/closed |

## Advanced Usage

### Custom Styling

You can customize the appearance by passing `styleOptions`:

```javascript
ChatbotWidget.init({
    directLineToken: 'YOUR_TOKEN',
    styleOptions: {
        backgroundColor: '#f8f9fa',
        primaryFont: '16px Arial, sans-serif',
        bubbleBackground: '#007bff',
        bubbleFromUserBackground: '#28a745',
        sendBoxBackground: '#ffffff',
        sendBoxBorderColor: '#dee2e6'
    }
});
```

### Event Handling

```javascript
const widget = ChatbotWidget.init({
    directLineToken: 'YOUR_TOKEN',
    onToggle: (isOpen) => {
        console.log('Chat widget is now:', isOpen ? 'open' : 'closed');
        // Track analytics, update UI, etc.
    }
});

// Programmatically destroy the widget
// widget.destroy();
```

## Getting Your Direct Line Token

1. Go to the [Azure Portal](https://portal.azure.com)
2. Navigate to your Bot Service
3. Go to "Channels" and configure the "Direct Line" channel
4. Generate a secret key - this is your `directLineToken`

⚠️ **Security Note**: Never expose your Direct Line secret in client-side code for production. Use token generation endpoints instead.

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## File Sizes

- JavaScript bundle: ~3.8MB (1MB gzipped)
- CSS file: ~2.8KB (0.9KB gzipped)

The bundle includes all necessary dependencies including React, Bot Framework WebChat, and related libraries.

## Development

To build the widget from source:

```bash
npm install
npm run build
```

To run the development server with demo:

```bash
npm run dev
```

## License

MIT License - see LICENSE file for details.
