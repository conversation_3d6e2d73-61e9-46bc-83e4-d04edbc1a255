/*! For license information please see adaptivecards.min.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.AdaptiveCards=t():e.AdaptiveCards=t()}(this,(function(){return(()=>{"use strict";var e={920:function(e,t){var i,n,r=this&&this.__extends||(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},i(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.LoginRequestResponse=t.ErrorResponse=t.SuccessResponse=t.ActivityResponse=t.ActivityRequestError=t.ActivityRequestTrigger=void 0,(n=t.ActivityRequestTrigger||(t.ActivityRequestTrigger={})).Automatic="automatic",n.Manual="manual";t.ActivityRequestError=function(e,t){this.code=e,this.message=t};var o=function(e){this.request=e};t.ActivityResponse=o;var s=function(e){function t(t,i){var n=e.call(this,t)||this;return n.request=t,n.rawContent=i,n}return r(t,e),t}(o);t.SuccessResponse=s;var a=function(e){function t(t,i){var n=e.call(this,t)||this;return n.request=t,n.error=i,n}return r(t,e),t}(o);t.ErrorResponse=a;var l=function(e){function t(t,i){var n=e.call(this,t)||this;n.request=t,n._auth=i;for(var r=0,o=n._auth.buttons;r<o.length;r++){var s=o[r];if("signin"===s.type&&void 0!==s.value)try{new URL(s.value),n.signinButton=s;break}catch(e){}}return n}return r(t,e),Object.defineProperty(t.prototype,"tokenExchangeResource",{get:function(){return this._auth.tokenExchangeResource},enumerable:!1,configurable:!0}),t}(o);t.LoginRequestResponse=l},684:function(e,t,i){var n=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))},r=this&&this.__generator||function(e,t){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};Object.defineProperty(t,"__esModule",{value:!0}),t.AdaptiveApplet=void 0;var o=i(10),s=i(755),a=i(764),l=i(920),p=i(653),c=i(651),u=i(475);function d(e,t){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];if(a.GlobalSettings.applets.logEnabled)if(a.GlobalSettings.applets.onLogEvent)a.GlobalSettings.applets.onLogEvent(e,t,i);else switch(e){case o.LogLevel.Warning:console.warn(t,i);break;case o.LogLevel.Error:console.error(t,i);break;default:console.log(t,i)}}var h=function(){function e(e,t,i){this.action=e,this.trigger=t,this.consecutiveActions=i,this.attemptNumber=0}return e.prototype.retryAsync=function(){return n(this,void 0,void 0,(function(){return r(this,(function(e){return this.onSend&&this.onSend(this),[2]}))}))},e}(),f=function(){function e(){this._allowAutomaticCardUpdate=!1,this.renderedElement=document.createElement("div"),this.renderedElement.className="aaf-cardHost",this.renderedElement.style.position="relative",this.renderedElement.style.display="flex",this.renderedElement.style.flexDirection="column",this._cardHostElement=document.createElement("div"),this._refreshButtonHostElement=document.createElement("div"),this._refreshButtonHostElement.className="aaf-refreshButtonHost",this._refreshButtonHostElement.style.display="none",this.renderedElement.appendChild(this._cardHostElement),this.renderedElement.appendChild(this._refreshButtonHostElement)}return e.prototype.displayCard=function(e){if(!e.renderedElement)throw new Error("displayCard: undefined card.");s.clearElementChildren(this._cardHostElement),this._refreshButtonHostElement.style.display="none",this._cardHostElement.appendChild(e.renderedElement)},e.prototype.showManualRefreshButton=function(e){var t=this;if(!this.onShowManualRefreshButton||this.onShowManualRefreshButton(this)){this._refreshButtonHostElement.style.display="none";var i=void 0;if(this.onRenderManualRefreshButton)i=this.onRenderManualRefreshButton(this);else{var n=p.Strings.runtime.refreshThisCard();if(a.GlobalSettings.applets.refresh.mode===o.RefreshMode.Automatic){var r=p.Strings.runtime.automaticRefreshPaused();" "!==r[r.length-1]&&(r+=" "),n=p.Strings.runtime.clckToRestartAutomaticRefresh()}var d={type:"AdaptiveCard",version:"1.2",body:[{type:"RichTextBlock",horizontalAlignment:"right",inlines:[{type:"TextRun",text:n,selectAction:{type:"Action.Submit",id:"refreshCard"}}]}]},h=new c.AdaptiveCard;h.parse(d,new c.SerializationContext(u.Versions.v1_2)),h.onExecuteAction=function(i){"refreshCard"===i.id&&(s.clearElementChildren(t._refreshButtonHostElement),t.internalExecuteAction(e,l.ActivityRequestTrigger.Automatic,0))},i=h.render()}i&&(s.clearElementChildren(this._refreshButtonHostElement),this._refreshButtonHostElement.appendChild(i),this._refreshButtonHostElement.style.removeProperty("display"))}},e.prototype.createActivityRequest=function(e,t,i){var n=this;if(this.card){var r=new h(e,t,i);return r.onSend=function(e){e.attemptNumber++,n.internalSendActivityRequestAsync(r)},this.onPrepareActivityRequest&&!this.onPrepareActivityRequest(this,r,e)?void 0:r}throw new Error("createActivityRequest: no card has been set.")},e.prototype.createMagicCodeInputCard=function(t){var i={type:"AdaptiveCard",version:"1.0",body:[{type:"TextBlock",color:"attention",text:1===t?void 0:"That didn't work... let's try again.",wrap:!0,horizontalAlignment:"center"},{type:"TextBlock",text:'Please login in the popup. You will obtain a magic code. Paste that code below and select "Submit"',wrap:!0,horizontalAlignment:"center"},{type:"Input.Text",id:"magicCode",placeholder:"Enter magic code"},{type:"ActionSet",horizontalAlignment:"center",actions:[{type:"Action.Submit",id:e._submitMagicCodeActionId,title:"Submit"},{type:"Action.Submit",id:e._cancelMagicCodeAuthActionId,title:"Cancel"}]}]},n=new c.AdaptiveCard;return n.parse(i),n},e.prototype.cancelAutomaticRefresh=function(){this._allowAutomaticCardUpdate&&d(o.LogLevel.Warning,"Automatic card refresh has been cancelled as a result of the user interacting with the card."),this._allowAutomaticCardUpdate=!1},e.prototype.createSerializationContext=function(){return this.onCreateSerializationContext?this.onCreateSerializationContext(this):new c.SerializationContext},e.prototype.internalSetCard=function(e,t){var i=this;if("object"==typeof e&&"AdaptiveCard"===e.type&&(this._cardPayload=e),this._cardPayload)try{var n=new c.AdaptiveCard;this.hostConfig&&(n.hostConfig=this.hostConfig);var r=this.createSerializationContext();if(n.parse(this._cardPayload,r),(!this.onCardChanging||this.onCardChanging(this,this._cardPayload))&&(this._card=n,this._card.authentication&&this._card.authentication.tokenExchangeResource&&this.onPrefetchSSOToken&&this.onPrefetchSSOToken(this,this._card.authentication.tokenExchangeResource),this._card.onExecuteAction=function(e){i.cancelAutomaticRefresh(),i.internalExecuteAction(e,l.ActivityRequestTrigger.Manual,0)},this._card.onInputValueChanged=function(e){var t,n,r;if(i.cancelAutomaticRefresh(),e instanceof c.ChoiceSetInput&&e.isDynamicTypeahead()){var o=e.getFilterForDynamicSearch();if(o){var s=new c.DataQuery;s.filter=o,s.dataset=(null===(t=e.choicesData)||void 0===t?void 0:t.dataset)||"",s.count=null===(n=e.choicesData)||void 0===n?void 0:n.count,s.skip=null===(r=e.choicesData)||void 0===r?void 0:r.skip,i._choiceSet=e,i.internalExecuteAction(s,l.ActivityRequestTrigger.Manual,0)}}},this._card.render(),this._card.renderedElement&&(this.displayCard(this._card),this.onCardChanged&&this.onCardChanged(this),this._card.refresh)))if(a.GlobalSettings.applets.refresh.mode===o.RefreshMode.Automatic&&t<a.GlobalSettings.applets.refresh.maximumConsecutiveAutomaticRefreshes)if(a.GlobalSettings.applets.refresh.timeBetweenAutomaticRefreshes<=0)d(o.LogLevel.Info,"Triggering automatic card refresh number "+(t+1)),this.internalExecuteAction(this._card.refresh.action,l.ActivityRequestTrigger.Automatic,t+1);else{d(o.LogLevel.Info,"Scheduling automatic card refresh number "+(t+1)+" in "+a.GlobalSettings.applets.refresh.timeBetweenAutomaticRefreshes+"ms");var s=this._card.refresh.action;this._allowAutomaticCardUpdate=!0,window.setTimeout((function(){i._allowAutomaticCardUpdate&&i.internalExecuteAction(s,l.ActivityRequestTrigger.Automatic,t+1)}),a.GlobalSettings.applets.refresh.timeBetweenAutomaticRefreshes)}else a.GlobalSettings.applets.refresh.mode!==o.RefreshMode.Disabled&&(d(o.LogLevel.Warning,t>0?"Stopping automatic refreshes after "+t+" consecutive refreshes.":"The card has a refresh section, but automatic refreshes are disabled."),(a.GlobalSettings.applets.refresh.allowManualRefreshesAfterAutomaticRefreshes||a.GlobalSettings.applets.refresh.mode===o.RefreshMode.Manual)&&(d(o.LogLevel.Info,"Showing manual refresh button."),this.showManualRefreshButton(this._card.refresh.action)))}catch(e){d(o.LogLevel.Error,"setCard: "+e)}},e.prototype.internalExecuteAction=function(e,t,i){if(e instanceof c.UniversalAction){if(!this.channelAdapter)throw new Error("internalExecuteAction: No channel adapter set.");var n=this.createActivityRequest(e,t,i);n&&n.retryAsync()}this.onAction&&this.onAction(this,e)},e.prototype.createProgressOverlay=function(e){if(!this._progressOverlay)if(this.onCreateProgressOverlay)this._progressOverlay=this.onCreateProgressOverlay(this,e);else{this._progressOverlay=document.createElement("div"),this._progressOverlay.className="aaf-progress-overlay";var t=document.createElement("div");t.className="aaf-spinner",t.style.width="28px",t.style.height="28px",this._progressOverlay.appendChild(t)}return this._progressOverlay},e.prototype.removeProgressOverlay=function(e){this.onRemoveProgressOverlay&&this.onRemoveProgressOverlay(this,e),void 0!==this._progressOverlay&&(this.renderedElement.removeChild(this._progressOverlay),this._progressOverlay=void 0)},e.prototype.activityRequestSucceeded=function(e,t){this.onActivityRequestSucceeded&&this.onActivityRequestSucceeded(this,e,t)},e.prototype.activityRequestFailed=function(e){return this.onActivityRequestFailed?this.onActivityRequestFailed(this,e):a.GlobalSettings.applets.defaultTimeBetweenRetryAttempts},e.prototype.showAuthCodeInputDialog=function(t){var i=this;if(!this.onShowAuthCodeInputDialog||this.onShowAuthCodeInputDialog(this,t)){var n=this.createMagicCodeInputCard(t.attemptNumber);n.render(),n.onExecuteAction=function(n){if(i.card&&n instanceof c.SubmitAction)switch(n.id){case e._submitMagicCodeActionId:var r=void 0;n.data&&"string"==typeof n.data.magicCode&&(r=n.data.magicCode),r?(i.displayCard(i.card),t.authCode=r,t.retryAsync()):alert("Please enter the magic code you received.");break;case e._cancelMagicCodeAuthActionId:d(o.LogLevel.Warning,"Authentication cancelled by user."),i.displayCard(i.card);break;default:d(o.LogLevel.Error,"Unexpected action taken from magic code input card (id = "+n.id+")"),alert(p.Strings.magicCodeInputCard.somethingWentWrong())}},this.displayCard(n)}},e.prototype.internalSendActivityRequestAsync=function(e){return n(this,void 0,void 0,(function(){var t;return r(this,(function(i){switch(i.label){case 0:if(!this.channelAdapter)throw new Error("internalSendActivityRequestAsync: channelAdapter is not set.");return(t=e.action)instanceof c.ExecuteAction?[4,this.internalSendExecuteRequestAsync(e)]:[3,2];case 1:case 3:return i.sent(),[3,5];case 2:return t instanceof c.DataQuery?[4,this.internalSendDataQueryRequestAsync(e)]:[3,4];case 4:throw new Error("internalSendActivityRequestAsync: Unhandled Action Type");case 5:return[2]}}))}))},e.prototype.internalSendExecuteRequestAsync=function(e){return n(this,void 0,void 0,(function(){var t,i,n,s;return r(this,(function(c){switch(c.label){case 0:if(!this.channelAdapter)throw new Error("internalSendExecuteRequestAsync: channelAdapter is not set.");void 0!==(t=this.createProgressOverlay(e))&&this.renderedElement.appendChild(t),i=!1,n=function(){var t,n,c,u,h,f,m;return r(this,(function(r){switch(r.label){case 0:t=void 0,1===e.attemptNumber?d(o.LogLevel.Info,"Sending activity request to channel (attempt "+e.attemptNumber+")"):d(o.LogLevel.Info,"Re-sending activity request to channel (attempt "+e.attemptNumber+")"),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,s.channelAdapter.sendRequestAsync(e)];case 2:return t=r.sent(),[3,4];case 3:return n=r.sent(),d(o.LogLevel.Error,"Activity request failed: "+n),s.removeProgressOverlay(e),i=!0,[3,4];case 4:if(!t)return[3,10];if(!(t instanceof l.SuccessResponse))return[3,5];if(s.removeProgressOverlay(e),void 0===t.rawContent)throw new Error("internalSendExecuteRequestAsync: Action.Execute result is undefined");c=t.rawContent;try{c=JSON.parse(t.rawContent)}catch(e){}if("string"==typeof c)d(o.LogLevel.Info,"The activity request returned a string after "+e.attemptNumber+" attempt(s)."),s.activityRequestSucceeded(t,c);else{if("object"!=typeof c||"AdaptiveCard"!==c.type)throw new Error("internalSendExecuteRequestAsync: Action.Execute result is of unsupported type ("+typeof t.rawContent+")");d(o.LogLevel.Info,"The activity request returned an Adaptive Card after "+e.attemptNumber+" attempt(s)."),s.internalSetCard(c,e.consecutiveActions),s.activityRequestSucceeded(t,s.card)}return i=!0,[3,10];case 5:return t instanceof l.ErrorResponse?(u=s.activityRequestFailed(t))>=0&&e.attemptNumber<a.GlobalSettings.applets.maximumRetryAttempts?(d(o.LogLevel.Warning,"Activity request failed: "+t.error.message+". Retrying in "+u+"ms"),e.attemptNumber++,[4,new Promise((function(e,t){window.setTimeout((function(){e()}),u)}))]):[3,7]:[3,9];case 6:return r.sent(),[3,8];case 7:d(o.LogLevel.Error,"Activity request failed: "+t.error.message+". Giving up after "+e.attemptNumber+" attempt(s)"),s.removeProgressOverlay(e),i=!0,r.label=8;case 8:return[3,10];case 9:if(t instanceof l.LoginRequestResponse){if(d(o.LogLevel.Info,"The activity request returned a LoginRequestResponse after "+e.attemptNumber+" attempt(s)."),e.attemptNumber<=a.GlobalSettings.applets.maximumRetryAttempts){if(h=!0,t.tokenExchangeResource&&s.onSSOTokenNeeded&&(h=!s.onSSOTokenNeeded(s,e,t.tokenExchangeResource)),h){if(s.removeProgressOverlay(e),void 0===t.signinButton)throw new Error("internalSendExecuteRequestAsync: the login request doesn't contain a valid signin URL.");d(o.LogLevel.Info,"Login required at "+t.signinButton.value),s.onShowSigninPrompt?s.onShowSigninPrompt(s,e,t.signinButton):(s.showAuthCodeInputDialog(e),f=window.screenX+(window.outerWidth-a.GlobalSettings.applets.authPromptWidth)/2,m=window.screenY+(window.outerHeight-a.GlobalSettings.applets.authPromptHeight)/2,window.open(t.signinButton.value,t.signinButton.title?t.signinButton.title:"Sign in","width="+a.GlobalSettings.applets.authPromptWidth+",height="+a.GlobalSettings.applets.authPromptHeight+",left="+f+",top="+m))}}else d(o.LogLevel.Error,"Authentication failed. Giving up after "+e.attemptNumber+" attempt(s)"),alert(p.Strings.magicCodeInputCard.authenticationFailed());return[2,"break"]}throw new Error("Unhandled response type: "+JSON.stringify(t));case 10:return[2]}}))},s=this,c.label=1;case 1:return i?[3,3]:[5,n()];case 2:return"break"===c.sent()?[3,3]:[3,1];case 3:return[2]}}))}))},e.prototype.internalSendDataQueryRequestAsync=function(e){return n(this,void 0,void 0,(function(){var t,i,n,s,a;return r(this,(function(r){switch(r.label){case 0:if(!this.channelAdapter)throw new Error("internalSendDataQueryRequestAsync: channel adapter not set");if(t=e.action.filter,!this._choiceSet)return[3,5];this._choiceSet.showLoadingIndicator(),i=void 0,r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.channelAdapter.sendRequestAsync(e)];case 2:return i=r.sent(),[3,4];case 3:return n=r.sent(),this._choiceSet.showErrorIndicator(t,"Unable to load"),d(o.LogLevel.Error,"Activity request failed: "+n),[3,4];case 4:if(i)if(i instanceof l.SuccessResponse){if(!(s=i.rawContent))throw this._choiceSet.showErrorIndicator(t,"Unable to load"),new Error("internalSendDataQueryRequestAsync: Data.Query result is undefined");a=void 0;try{a=JSON.parse(s)}catch(e){throw this._choiceSet.showErrorIndicator(t,"Unable to load"),new Error("internalSendDataQueryRequestAsync: Cannot parse response object: "+s)}if("object"!=typeof a)throw this._choiceSet.showErrorIndicator(t,"Error loading results"),new Error("internalSendDataQueryRequestAsync: Data.Query result is of unsupported type ("+typeof s+")");this._choiceSet.renderChoices(t,a),this.activityRequestSucceeded(i,s)}else{if(!(i instanceof l.ErrorResponse))throw this._choiceSet.showErrorIndicator(t,"Unable to load"),new Error("Unhandled response type: "+JSON.stringify(i));this._choiceSet.showErrorIndicator(t,i.error.message||"Error loading results"),d(o.LogLevel.Error,"Activity request failed: "+i.error.message+"."),this.activityRequestFailed(i)}else this._choiceSet.removeLoadingIndicator();r.label=5;case 5:return[2]}}))}))},e.prototype.refreshCard=function(){this._card&&this._card.refresh&&this.internalExecuteAction(this._card.refresh.action,l.ActivityRequestTrigger.Manual,0)},e.prototype.setCard=function(e){this.internalSetCard(e,0)},Object.defineProperty(e.prototype,"card",{get:function(){return this._card},enumerable:!1,configurable:!0}),e._submitMagicCodeActionId="submitMagicCode",e._cancelMagicCodeAuthActionId="cancelMagicCodeAuth",e}();t.AdaptiveApplet=f},402:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[i]}})}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),r=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),r(i(653),t),r(i(10),t),r(i(764),t),r(i(755),t),r(i(475),t),r(i(233),t),r(i(699),t),r(i(432),t),r(i(817),t),r(i(651),t),r(i(343),t),r(i(74),t),r(i(213),t),r(i(920),t),r(i(684),t)},651:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),o=this&&this.__decorate||function(e,t,i,n){var r,o=arguments.length,s=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,i,n);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,i,s):r(t,i))||s);return o>3&&s&&Object.defineProperty(t,i,s),s},s=this&&this.__awaiter||function(e,t,i,n){return new(i||(i=Promise))((function(r,o){function s(e){try{l(n.next(e))}catch(e){o(e)}}function a(e){try{l(n.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(s,a)}l((n=n.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!((r=(r=s.trys).length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},l=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundImage=t.ContainerBase=t.StylableCardElementContainer=t.ContainerStyleProperty=t.ActionSet=t.ShowCardAction=t.HttpAction=t.HttpHeader=t.ToggleVisibilityAction=t.OpenUrlAction=t.DataQuery=t.ExecuteAction=t.UniversalAction=t.SubmitAction=t.SubmitActionBase=t.Action=t.TimeInput=t.TimeProperty=t.DateInput=t.NumberInput=t.FilteredChoiceSet=t.ChoiceSetInput=t.ChoiceSetInputDataQuery=t.Choice=t.ToggleInput=t.TextInput=t.Input=t.Media=t.YouTubePlayer=t.DailymotionPlayer=t.VimeoPlayer=t.IFrameMediaMediaPlayer=t.CustomMediaPlayer=t.HTML5MediaPlayer=t.MediaPlayer=t.MediaSource=t.CaptionSource=t.ContentSource=t.ImageSet=t.CardElementContainer=t.Image=t.FactSet=t.Fact=t.RichTextBlock=t.TextRun=t.TextBlock=t.BaseTextBlock=t.ActionProperty=t.CardElement=t.renderSeparation=void 0,t.SerializationContext=t.AdaptiveCard=t.Authentication=t.TokenExchangeResource=t.AuthCardButton=t.RefreshDefinition=t.RefreshActionProperty=t.ContainerWithActions=t.ColumnSet=t.Column=t.Container=void 0;var p=i(10),c=i(764),u=i(755),d=i(699),h=i(639),f=i(817),m=i(475),g=i(432),y=i(653),v=i(441);function b(e){var t,i,n="undefined"==typeof window?"":null!==(i=null===(t=window.trustedTypes)||void 0===t?void 0:t.emptyHTML)&&void 0!==i?i:"";e.innerHTML=n}function C(e,t,i){if(t.spacing>0||t.lineThickness&&t.lineThickness>0){var n=document.createElement("div");n.className=e.makeCssClassName("ac-"+(i===p.Orientation.Horizontal?"horizontal":"vertical")+"-separator"),n.setAttribute("aria-hidden","true");var r=t.lineColor?u.stringToCssColor(t.lineColor):"";return i===p.Orientation.Horizontal?t.lineThickness?(n.style.paddingTop=t.spacing/2+"px",n.style.marginBottom=t.spacing/2+"px",n.style.borderBottom=t.lineThickness+"px solid "+r):n.style.height=t.spacing+"px":t.lineThickness?(n.style.paddingLeft=t.spacing/2+"px",n.style.marginRight=t.spacing/2+"px",n.style.borderRight=t.lineThickness+"px solid "+r):n.style.width=t.spacing+"px",n.style.overflow="hidden",n.style.flex="0 0 auto",n}}t.renderSeparation=C;var w=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._truncatedDueToOverflow=!1,t}return r(t,e),Object.defineProperty(t.prototype,"lang",{get:function(){return this.getValue(t.langProperty)||(this.parent?this.parent.lang:void 0)},set:function(e){this.setValue(t.langProperty,e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isVisible",{get:function(){return this.getValue(t.isVisibleProperty)},set:function(e){c.GlobalSettings.useAdvancedCardBottomTruncation&&!e&&this.undoOverflowTruncation(),this.isVisible!==e&&(this.setValue(t.isVisibleProperty,e),this.updateRenderedElementVisibility(),this._renderedElement&&Ie(this)),this._renderedElement&&this._renderedElement.setAttribute("aria-expanded",e.toString())},enumerable:!1,configurable:!0}),t.prototype.internalRenderSeparator=function(){var e=C(this.hostConfig,{spacing:this.hostConfig.getEffectiveSpacing(this.spacing),lineThickness:this.separator?this.hostConfig.separator.lineThickness:void 0,lineColor:this.separator?this.hostConfig.separator.lineColor:void 0},this.separatorOrientation);if(c.GlobalSettings.alwaysBleedSeparators&&e&&this.separatorOrientation===p.Orientation.Horizontal){var t=this.getParentContainer();if(t&&t.getEffectivePadding()){var i=this.hostConfig.paddingDefinitionToSpacingDefinition(t.getEffectivePadding());e.style.marginLeft="-"+i.left+"px",e.style.marginRight="-"+i.right+"px"}}return e},t.prototype.updateRenderedElementVisibility=function(){var e=this.isDesignMode()||this.isVisible?this._defaultRenderedElementDisplayMode:"none";this._renderedElement&&(e?this._renderedElement.style.display=e:this._renderedElement.style.removeProperty("display")),this._separatorElement&&(this.parent&&this.parent.isFirstElement(this)?this._separatorElement.style.display="none":e?this._separatorElement.style.display=e:this._separatorElement.style.removeProperty("display"))},t.prototype.hideElementDueToOverflow=function(){this._renderedElement&&this.isVisible&&(this._renderedElement.style.visibility="hidden",this.isVisible=!1,Ie(this,!1))},t.prototype.showElementHiddenDueToOverflow=function(){this._renderedElement&&!this.isVisible&&(this._renderedElement.style.removeProperty("visibility"),this.isVisible=!0,Ie(this,!1))},t.prototype.handleOverflow=function(e){if(this.isVisible||this.isHiddenDueToOverflow()){var t=this.truncateOverflow(e);this._truncatedDueToOverflow=t||this._truncatedDueToOverflow,t?t&&!this.isVisible&&this.showElementHiddenDueToOverflow():this.hideElementDueToOverflow()}},t.prototype.resetOverflow=function(){var e=!1;return this._truncatedDueToOverflow&&(this.undoOverflowTruncation(),this._truncatedDueToOverflow=!1,e=!0),this.isHiddenDueToOverflow()&&this.showElementHiddenDueToOverflow(),e},t.prototype.getDefaultSerializationContext=function(){return new $e},t.prototype.createPlaceholderElement=function(){var e=this.getEffectiveStyleDefinition(),t=u.stringToCssColor(e.foregroundColors.default.subtle),i=document.createElement("div");return i.style.border="1px dashed "+t,i.style.padding="4px",i.style.minHeight="32px",i.style.fontSize="10px",t&&(i.style.color=t),i.innerText=y.Strings.defaults.emptyElementText(this.getJsonTypeName()),i},t.prototype.adjustRenderedElementSize=function(e){"auto"===this.height?e.style.flex="0 0 auto":e.style.flex="1 1 auto"},t.prototype.updateInputsVisualState=function(e){var t=this.getAllInputs(),i=e?G.MouseEnterOnCard:G.MouseLeaveOnCard;t.forEach((function(e){return e.updateVisualState(i)}))},t.prototype.isDisplayed=function(){return void 0!==this._renderedElement&&this.isVisible&&this._renderedElement.offsetHeight>0},t.prototype.overrideInternalRender=function(){return this.internalRender()},t.prototype.applyPadding=function(){if(this.separatorElement&&this.separatorOrientation===p.Orientation.Horizontal)if(c.GlobalSettings.alwaysBleedSeparators&&!this.isBleeding()){var e=new c.PaddingDefinition;this.getImmediateSurroundingPadding(e);var t=this.hostConfig.paddingDefinitionToSpacingDefinition(e);this.separatorElement.style.marginLeft="-"+t.left+"px",this.separatorElement.style.marginRight="-"+t.right+"px"}else this.separatorElement.style.marginRight="0",this.separatorElement.style.marginLeft="0"},t.prototype.truncateOverflow=function(e){return!1},t.prototype.undoOverflowTruncation=function(){},t.prototype.getDefaultPadding=function(){return new c.PaddingDefinition},t.prototype.getHasBackground=function(e){return void 0===e&&(e=!1),!1},t.prototype.getHasBorder=function(){return!1},t.prototype.getPadding=function(){return this._padding},t.prototype.setPadding=function(e){this._padding=e},t.prototype.shouldSerialize=function(e){return void 0!==e.elementRegistry.findByName(this.getJsonTypeName())},Object.defineProperty(t.prototype,"useDefaultSizing",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"separatorOrientation",{get:function(){return p.Orientation.Horizontal},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"defaultStyle",{get:function(){return p.ContainerStyle.Default},enumerable:!1,configurable:!0}),t.prototype.parse=function(t,i){e.prototype.parse.call(this,t,i||new $e)},t.prototype.asString=function(){return""},t.prototype.isBleeding=function(){return!1},t.prototype.getEffectiveStyle=function(){return this.parent?this.parent.getEffectiveStyle():this.defaultStyle},t.prototype.getEffectiveStyleDefinition=function(){return this.hostConfig.containerStyles.getStyleByName(this.getEffectiveStyle())},t.prototype.getEffectiveTextStyleDefinition=function(){return this.parent?this.parent.getEffectiveTextStyleDefinition():this.hostConfig.textStyles.default},t.prototype.getForbiddenActionTypes=function(){return[]},t.prototype.getImmediateSurroundingPadding=function(e,t,i,n,r){if(void 0===t&&(t=!0),void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0),this.parent){var o=t&&this.parent.isTopElement(this),s=i&&this.parent.isRightMostElement(this),a=n&&this.parent.isBottomElement(this),l=r&&this.parent.isLeftMostElement(this),c=this.parent.getEffectivePadding();c&&(o&&c.top!==p.Spacing.None&&(e.top=c.top,o=!1),s&&c.right!==p.Spacing.None&&(e.right=c.right,s=!1),a&&c.bottom!==p.Spacing.None&&(e.bottom=c.bottom,a=!1),l&&c.left!==p.Spacing.None&&(e.left=c.left,l=!1)),(o||s||a||l)&&this.parent.getImmediateSurroundingPadding(e,o,s,a,l)}},t.prototype.getActionCount=function(){return 0},t.prototype.getActionAt=function(e){throw new Error(y.Strings.errors.indexOutOfRange(e))},t.prototype.indexOfAction=function(e){for(var t=0;t<this.getActionCount();t++)if(this.getActionAt(t)===e)return t;return-1},t.prototype.remove=function(){return!!(this.parent&&this.parent instanceof V)&&this.parent.removeItem(this)},t.prototype.render=function(){return this._renderedElement=this.overrideInternalRender(),this._separatorElement=this.internalRenderSeparator(),this._renderedElement?(this.id&&(this._renderedElement.id=this.id),this.customCssSelector&&this._renderedElement.classList.add(this.customCssSelector),this._renderedElement.style.boxSizing="border-box",this._defaultRenderedElementDisplayMode=this._renderedElement.style.display?this._renderedElement.style.display:void 0,this.adjustRenderedElementSize(this._renderedElement),this.updateLayout(!1)):this.isDesignMode()&&(this._renderedElement=this.createPlaceholderElement()),this._renderedElement},t.prototype.updateLayout=function(e){void 0===e&&(e=!0),this.updateRenderedElementVisibility(),this.applyPadding()},t.prototype.updateActionsEnabledState=function(){for(var e=0,t=this.getRootElement().getAllActions();e<t.length;e++)t[e].updateEnabledState()},t.prototype.indexOf=function(e){return-1},t.prototype.isDesignMode=function(){var e=this.getRootElement();return e instanceof Ne&&e.designMode},t.prototype.isFirstElement=function(e){return!0},t.prototype.isLastElement=function(e){return!0},t.prototype.isAtTheVeryLeft=function(){return!this.parent||this.parent.isLeftMostElement(this)&&this.parent.isAtTheVeryLeft()},t.prototype.isAtTheVeryRight=function(){return!this.parent||this.parent.isRightMostElement(this)&&this.parent.isAtTheVeryRight()},t.prototype.isAtTheVeryTop=function(){return!this.parent||this.parent.isFirstElement(this)&&this.parent.isAtTheVeryTop()},t.prototype.isAtTheVeryBottom=function(){return!this.parent||this.parent.isLastElement(this)&&this.parent.isAtTheVeryBottom()},t.prototype.isBleedingAtTop=function(){return!1},t.prototype.isBleedingAtBottom=function(){return!1},t.prototype.isLeftMostElement=function(e){return!0},t.prototype.isRightMostElement=function(e){return!0},t.prototype.isTopElement=function(e){return this.isFirstElement(e)},t.prototype.isBottomElement=function(e){return this.isLastElement(e)},t.prototype.isHiddenDueToOverflow=function(){return void 0!==this._renderedElement&&"hidden"===this._renderedElement.style.visibility},t.prototype.getRootElement=function(){return this.getRootObject()},t.prototype.getParentContainer=function(){for(var e=this.parent;e;){if(e instanceof Ee)return e;e=e.parent}},t.prototype.getAllInputs=function(e){return void 0===e&&(e=!0),[]},t.prototype.getAllActions=function(){for(var e=[],t=0;t<this.getActionCount();t++){var i=this.getActionAt(t);i&&e.push(i)}return e},t.prototype.getResourceInformation=function(){return[]},t.prototype.getElementById=function(e){return this.id===e?this:void 0},t.prototype.getActionById=function(e){},t.prototype.getElementByIdFromAction=function(e){for(var t=void 0,i=0;i<this.getActionCount();i++){var n=this.getActionAt(i);if(n instanceof me&&(t=n.card.getElementById(e)))break}return t},t.prototype.getEffectivePadding=function(){return this.getPadding()||this.getDefaultPadding()},t.prototype.getEffectiveHorizontalAlignment=function(){return void 0!==this.horizontalAlignment?this.horizontalAlignment:this.parent?this.parent.getEffectiveHorizontalAlignment():p.HorizontalAlignment.Left},Object.defineProperty(t.prototype,"hostConfig",{get:function(){return this._hostConfig?this._hostConfig:this.parent?this.parent.hostConfig:d.defaultHostConfig},set:function(e){this._hostConfig=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"index",{get:function(){return this.parent?this.parent.indexOf(this):0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isInteractive",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isInline",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hasVisibleSeparator",{get:function(){return!(!this.parent||!this.separatorElement)&&!this.parent.isFirstElement(this)&&(this.isVisible||this.isDesignMode())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"separatorElement",{get:function(){return this._separatorElement},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),t.langProperty=new m.StringProperty(m.Versions.v1_1,"lang",!0,/^[a-z]{2,3}$/gi),t.isVisibleProperty=new m.BoolProperty(m.Versions.v1_2,"isVisible",!0),t.separatorProperty=new m.BoolProperty(m.Versions.v1_0,"separator",!1),t.heightProperty=new m.ValueSetProperty(m.Versions.v1_1,"height",[{value:"auto"},{value:"stretch"}],"auto"),t.horizontalAlignmentProperty=new m.EnumProperty(m.Versions.v1_0,"horizontalAlignment",p.HorizontalAlignment),t.spacingProperty=new m.EnumProperty(m.Versions.v1_0,"spacing",p.Spacing,p.Spacing.Default),o([(0,m.property)(t.horizontalAlignmentProperty)],t.prototype,"horizontalAlignment",void 0),o([(0,m.property)(t.spacingProperty)],t.prototype,"spacing",void 0),o([(0,m.property)(t.separatorProperty)],t.prototype,"separator",void 0),o([(0,m.property)(t.heightProperty)],t.prototype,"height",void 0),o([(0,m.property)(t.langProperty)],t.prototype,"lang",null),o([(0,m.property)(t.isVisibleProperty)],t.prototype,"isVisible",null),t}(f.CardObject);t.CardElement=w;var S=function(e){function t(t,i,n){void 0===n&&(n=[]);var r=e.call(this,t,i,void 0)||this;return r.targetVersion=t,r.name=i,r.forbiddenActionTypes=n,r}return r(t,e),t.prototype.parse=function(e,t,i){var n=e;return i.parseAction(n,t[this.name],this.forbiddenActionTypes,n.isDesignMode())},t.prototype.toJSON=function(e,t,i,n){n.serializeValue(t,this.name,i?i.toJSON(n):void 0,void 0,!0)},t}(m.PropertyDefinition);t.ActionProperty=S;var E=function(e){function t(t){var i=e.call(this)||this;return i.ariaHidden=!1,t&&(i.text=t),i}return r(t,e),t.prototype.populateSchema=function(i){e.prototype.populateSchema.call(this,i),i.remove(t.selectActionProperty)},Object.defineProperty(t.prototype,"text",{get:function(){return this.getValue(t.textProperty)},set:function(e){this.setText(e)},enumerable:!1,configurable:!0}),t.prototype.getFontSize=function(e){switch(this.effectiveSize){case p.TextSize.Small:return e.fontSizes.small;case p.TextSize.Medium:return e.fontSizes.medium;case p.TextSize.Large:return e.fontSizes.large;case p.TextSize.ExtraLarge:return e.fontSizes.extraLarge;default:return e.fontSizes.default}},t.prototype.getColorDefinition=function(e,t){switch(t){case p.TextColor.Accent:return e.accent;case p.TextColor.Dark:return e.dark;case p.TextColor.Light:return e.light;case p.TextColor.Good:return e.good;case p.TextColor.Warning:return e.warning;case p.TextColor.Attention:return e.attention;default:return e.default}},t.prototype.setText=function(e){this.setValue(t.textProperty,e)},t.prototype.init=function(e){this.size=e.size,this.weight=e.weight,this.color=e.color,this.isSubtle=e.isSubtle},t.prototype.asString=function(){return this.text},t.prototype.applyStylesTo=function(e){var t,i=this.hostConfig.getFontTypeDefinition(this.effectiveFontType);switch(i.fontFamily&&(e.style.fontFamily=i.fontFamily),this.effectiveSize){case p.TextSize.Small:t=i.fontSizes.small;break;case p.TextSize.Medium:t=i.fontSizes.medium;break;case p.TextSize.Large:t=i.fontSizes.large;break;case p.TextSize.ExtraLarge:t=i.fontSizes.extraLarge;break;default:t=i.fontSizes.default}e.style.fontSize=t+"px";var n,r=this.getColorDefinition(this.getEffectiveStyleDefinition().foregroundColors,this.effectiveColor),o=u.stringToCssColor(this.effectiveIsSubtle?r.subtle:r.default);switch(o&&(e.style.color=o),this.effectiveWeight){case p.TextWeight.Lighter:n=i.fontWeights.lighter;break;case p.TextWeight.Bolder:n=i.fontWeights.bolder;break;default:n=i.fontWeights.default}e.style.fontWeight=n.toString(),this.ariaHidden&&e.setAttribute("aria-hidden","true")},t.prototype.getAllActions=function(){var t=e.prototype.getAllActions.call(this);return this.selectAction&&t.push(this.selectAction),t},Object.defineProperty(t.prototype,"effectiveColor",{get:function(){return void 0!==this.color?this.color:this.getEffectiveTextStyleDefinition().color},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"effectiveFontType",{get:function(){return void 0!==this.fontType?this.fontType:this.getEffectiveTextStyleDefinition().fontType},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"effectiveIsSubtle",{get:function(){return void 0!==this.isSubtle?this.isSubtle:this.getEffectiveTextStyleDefinition().isSubtle},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"effectiveSize",{get:function(){return void 0!==this.size?this.size:this.getEffectiveTextStyleDefinition().size},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"effectiveWeight",{get:function(){return void 0!==this.weight?this.weight:this.getEffectiveTextStyleDefinition().weight},enumerable:!1,configurable:!0}),t.textProperty=new m.StringProperty(m.Versions.v1_0,"text",!0),t.sizeProperty=new m.EnumProperty(m.Versions.v1_0,"size",p.TextSize),t.weightProperty=new m.EnumProperty(m.Versions.v1_0,"weight",p.TextWeight),t.colorProperty=new m.EnumProperty(m.Versions.v1_0,"color",p.TextColor),t.isSubtleProperty=new m.BoolProperty(m.Versions.v1_0,"isSubtle"),t.fontTypeProperty=new m.EnumProperty(m.Versions.v1_2,"fontType",p.FontType),t.selectActionProperty=new S(m.Versions.v1_1,"selectAction",["Action.ShowCard"]),o([(0,m.property)(t.sizeProperty)],t.prototype,"size",void 0),o([(0,m.property)(t.weightProperty)],t.prototype,"weight",void 0),o([(0,m.property)(t.colorProperty)],t.prototype,"color",void 0),o([(0,m.property)(t.fontTypeProperty)],t.prototype,"fontType",void 0),o([(0,m.property)(t.isSubtleProperty)],t.prototype,"isSubtle",void 0),o([(0,m.property)(t.textProperty)],t.prototype,"text",null),o([(0,m.property)(t.selectActionProperty)],t.prototype,"selectAction",void 0),t}(w);t.BaseTextBlock=E;var x=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.wrap=!1,t._treatAsPlainText=!0,t.useMarkdown=!0,t}var i,n;return r(t,e),t.prototype.restoreOriginalContent=function(){var e,i;if(void 0!==this.renderedElement){this.maxLines&&this.maxLines>0&&(this.renderedElement.style.maxHeight=this._computedLineHeight*this.maxLines+"px");var n=null!==(i=null===(e=t._ttRoundtripPolicy)||void 0===e?void 0:e.createHTML(this._originalInnerHtml))&&void 0!==i?i:this._originalInnerHtml;this.renderedElement.innerHTML=n}},t.prototype.truncateIfSupported=function(e){if(void 0!==this.renderedElement){var t=this.renderedElement.children,i=!t.length;if(i||1===t.length&&"p"===t[0].tagName.toLowerCase()&&!t[0].children.length){var n=i?this.renderedElement:t[0];return u.truncateText(n,e,this._computedLineHeight),!0}}return!1},t.prototype.setText=function(t){e.prototype.setText.call(this,t),this._processedText=void 0},t.prototype.internalRender=function(){var e,i,n=this;if(this._processedText=void 0,this.text){var r=this.preProcessPropertyValue(E.textProperty),o=this.hostConfig,s=void 0;if(this.forElementId){var a=document.createElement("label");a.htmlFor=this.forElementId,s=a}else s=document.createElement("div");if(s.classList.add(o.makeCssClassName("ac-textBlock")),s.style.overflow="hidden",this.applyStylesTo(s),"heading"===this.style){s.setAttribute("role","heading");var l=this.hostConfig.textBlock.headingLevel;void 0!==l&&l>0&&s.setAttribute("aria-level",l.toString())}if(this.selectAction&&o.supportsInteractivity&&(s.onclick=function(e){n.selectAction&&n.selectAction.isEffectivelyEnabled()&&(e.preventDefault(),e.cancelBubble=!0,n.selectAction.execute())},this.selectAction.setupElementForAccessibility(s),this.selectAction.isEffectivelyEnabled()&&s.classList.add(o.makeCssClassName("ac-selectable"))),!this._processedText){this._treatAsPlainText=!0;var p=h.formatText(this.lang,r);if(this.useMarkdown&&p){c.GlobalSettings.allowMarkForTextHighlighting&&(p=p.replace(/<mark>/g,"===").replace(/<\/mark>/g,"/==/"));var d=Ne.applyMarkdown(p);if(d.didProcess&&d.outputHtml){if(this._processedText=d.outputHtml,this._treatAsPlainText=!1,c.GlobalSettings.allowMarkForTextHighlighting&&this._processedText){var f="",m=this.getEffectiveStyleDefinition();m.highlightBackgroundColor&&(f+="background-color: "+m.highlightBackgroundColor+";"),m.highlightForegroundColor&&(f+="color: "+m.highlightForegroundColor+";"),f&&(f='style="'+f+'"'),this._processedText=this._processedText.replace(/===/g,"<mark "+f+">").replace(/\/==\//g,"</mark>")}}else this._processedText=p,this._treatAsPlainText=!0}else this._processedText=p,this._treatAsPlainText=!0}if(this._processedText||(this._processedText=""),this._treatAsPlainText)s.innerText=this._processedText;else{var g=null!==(i=null===(e=t._ttMarkdownPolicy)||void 0===e?void 0:e.createHTML(this._processedText))&&void 0!==i?i:this._processedText;s.innerHTML=g}if(s.firstElementChild instanceof HTMLElement){var y=s.firstElementChild;y.style.marginTop="0px",y.style.width="100%",this.wrap||(y.style.overflow="hidden",y.style.textOverflow="ellipsis")}s.lastElementChild instanceof HTMLElement&&(s.lastElementChild.style.marginBottom="0px");for(var v=s.getElementsByTagName("a"),b=function(e){e.classList.add(o.makeCssClassName("ac-anchor")),e.target="_blank",e.onclick=function(t){Ae(n,e,t)&&(t.preventDefault(),t.cancelBubble=!0)},e.oncontextmenu=function(t){return!Ae(n,e,t)||(t.preventDefault(),t.cancelBubble=!0,!1)}},C=0,w=Array.from(v);C<w.length;C++)b(w[C]);return this.wrap?(s.style.wordWrap="break-word",this.maxLines&&this.maxLines>0&&(s.style.overflow="hidden",u.isInternetExplorer()||!c.GlobalSettings.useWebkitLineClamp?s.style.maxHeight=this._computedLineHeight*this.maxLines+"px":(s.style.removeProperty("line-height"),s.style.display="-webkit-box",s.style.webkitBoxOrient="vertical",s.style.webkitLineClamp=this.maxLines.toString()))):(s.style.whiteSpace="nowrap",s.style.textOverflow="ellipsis"),(c.GlobalSettings.useAdvancedTextBlockTruncation||c.GlobalSettings.useAdvancedCardBottomTruncation)&&(this._originalInnerHtml=s.innerHTML),s}},t.prototype.truncateOverflow=function(e){return e>=this._computedLineHeight&&this.truncateIfSupported(e)},t.prototype.undoOverflowTruncation=function(){if(this.restoreOriginalContent(),c.GlobalSettings.useAdvancedTextBlockTruncation&&this.maxLines){var e=this._computedLineHeight*this.maxLines;this.truncateIfSupported(e)}},t.prototype.applyStylesTo=function(t){switch(e.prototype.applyStylesTo.call(this,t),this.getEffectiveHorizontalAlignment()){case p.HorizontalAlignment.Center:t.style.textAlign="center";break;case p.HorizontalAlignment.Right:t.style.textAlign="end";break;default:t.style.textAlign="start"}var i=this.hostConfig.lineHeights;if(i)switch(this.effectiveSize){case p.TextSize.Small:this._computedLineHeight=i.small;break;case p.TextSize.Medium:this._computedLineHeight=i.medium;break;case p.TextSize.Large:this._computedLineHeight=i.large;break;case p.TextSize.ExtraLarge:this._computedLineHeight=i.extraLarge;break;default:this._computedLineHeight=i.default}else this._computedLineHeight=1.33*this.getFontSize(this.hostConfig.getFontTypeDefinition(this.effectiveFontType));t.style.lineHeight=this._computedLineHeight+"px"},t.prototype.getJsonTypeName=function(){return"TextBlock"},t.prototype.getEffectiveTextStyleDefinition=function(){return this.style?this.hostConfig.textStyles.getStyleByName(this.style):e.prototype.getEffectiveTextStyleDefinition.call(this)},t.prototype.updateLayout=function(t){void 0===t&&(t=!1),e.prototype.updateLayout.call(this,t),c.GlobalSettings.useAdvancedTextBlockTruncation&&this.maxLines&&this.isDisplayed()&&(this.restoreOriginalContent(),this.truncateIfSupported(this._computedLineHeight*this.maxLines))},t.wrapProperty=new m.BoolProperty(m.Versions.v1_0,"wrap",!1),t.maxLinesProperty=new m.NumProperty(m.Versions.v1_0,"maxLines"),t.styleProperty=new m.ValueSetProperty(m.Versions.v1_5,"style",[{value:"default"},{value:"columnHeader"},{value:"heading"}]),t._ttMarkdownPolicy="undefined"==typeof window||null===(i=window.trustedTypes)||void 0===i?void 0:i.createPolicy("adaptivecards#markdownPassthroughPolicy",{createHTML:function(e){return e}}),t._ttRoundtripPolicy="undefined"==typeof window||null===(n=window.trustedTypes)||void 0===n?void 0:n.createPolicy("adaptivecards#restoreContentsPolicy",{createHTML:function(e){return e}}),o([(0,m.property)(t.wrapProperty)],t.prototype,"wrap",void 0),o([(0,m.property)(t.maxLinesProperty)],t.prototype,"maxLines",void 0),o([(0,m.property)(t.styleProperty)],t.prototype,"style",void 0),t}(E);t.TextBlock=x;var P=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.italic=!1,t.strikethrough=!1,t.highlight=!1,t.underline=!1,t}return r(t,e),t.prototype.populateSchema=function(t){e.prototype.populateSchema.call(this,t),t.add(E.selectActionProperty)},t.prototype.internalRender=function(){var e=this;if(this.text){var t=this.preProcessPropertyValue(E.textProperty),i=this.hostConfig,n=h.formatText(this.lang,t);n||(n="");var r=document.createElement("span");if(r.classList.add(i.makeCssClassName("ac-textRun")),this.applyStylesTo(r),this.selectAction&&i.supportsInteractivity){var o=document.createElement("a");o.classList.add(i.makeCssClassName("ac-anchor"));var s=this.selectAction.getHref();o.href=s||"",o.target="_blank",o.onclick=function(t){e.selectAction&&e.selectAction.isEffectivelyEnabled()&&(t.preventDefault(),t.cancelBubble=!0,e.selectAction.execute())},this.selectAction.setupElementForAccessibility(o),o.innerText=n,r.appendChild(o)}else r.innerText=n;return r}},t.prototype.applyStylesTo=function(t){if(e.prototype.applyStylesTo.call(this,t),this.italic&&(t.style.fontStyle="italic"),this.strikethrough&&(t.style.textDecoration="line-through"),this.highlight){var i=this.getColorDefinition(this.getEffectiveStyleDefinition().foregroundColors,this.effectiveColor),n=u.stringToCssColor(this.effectiveIsSubtle?i.highlightColors.subtle:i.highlightColors.default);n&&(t.style.backgroundColor=n)}this.underline&&(t.style.textDecoration="underline")},t.prototype.getJsonTypeName=function(){return"TextRun"},Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isInline",{get:function(){return!0},enumerable:!1,configurable:!0}),t.italicProperty=new m.BoolProperty(m.Versions.v1_2,"italic",!1),t.strikethroughProperty=new m.BoolProperty(m.Versions.v1_2,"strikethrough",!1),t.highlightProperty=new m.BoolProperty(m.Versions.v1_2,"highlight",!1),t.underlineProperty=new m.BoolProperty(m.Versions.v1_3,"underline",!1),o([(0,m.property)(t.italicProperty)],t.prototype,"italic",void 0),o([(0,m.property)(t.strikethroughProperty)],t.prototype,"strikethrough",void 0),o([(0,m.property)(t.highlightProperty)],t.prototype,"highlight",void 0),o([(0,m.property)(t.underlineProperty)],t.prototype,"underline",void 0),t}(E);t.TextRun=P;var _=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._inlines=[],t}return r(t,e),t.prototype.internalAddInline=function(e,t){if(void 0===t&&(t=!1),!e.isInline)throw new Error(y.Strings.errors.elementCannotBeUsedAsInline());if(void 0!==e.parent&&!t&&e.parent!==this)throw new Error(y.Strings.errors.inlineAlreadyParented());e.setParent(this),this._inlines.push(e)},t.prototype.internalParse=function(t,i){if(e.prototype.internalParse.call(this,t,i),this._inlines=[],Array.isArray(t.inlines))for(var n=0,r=t.inlines;n<r.length;n++){var o=r[n],s=void 0;if("string"==typeof o){var a=new P;a.text=o,s=a}else s=i.parseElement(this,o,[],!1);s&&this.internalAddInline(s,!0)}},t.prototype.internalToJSON=function(t,i){if(e.prototype.internalToJSON.call(this,t,i),this._inlines.length>0){for(var n=[],r=0,o=this._inlines;r<o.length;r++){var s=o[r];n.push(s.toJSON(i))}i.serializeValue(t,"inlines",n)}},t.prototype.internalRender=function(){if(this._inlines.length>0){var e=void 0;if(this.forElementId){var t=document.createElement("label");t.htmlFor=this.forElementId,e=t}else e=document.createElement("div");switch(e.className=this.hostConfig.makeCssClassName("ac-richTextBlock"),this.getEffectiveHorizontalAlignment()){case p.HorizontalAlignment.Center:e.style.textAlign="center";break;case p.HorizontalAlignment.Right:e.style.textAlign="end";break;default:e.style.textAlign="start"}for(var i=0,n=0,r=this._inlines;n<r.length;n++){var o=r[n].render();o&&(e.appendChild(o),i++)}if(i>0)return e}},t.prototype.asString=function(){for(var e="",t=0,i=this._inlines;t<i.length;t++)e+=i[t].asString();return e},t.prototype.getJsonTypeName=function(){return"RichTextBlock"},t.prototype.getInlineCount=function(){return this._inlines.length},t.prototype.getInlineAt=function(e){if(e>=0&&e<this._inlines.length)return this._inlines[e];throw new Error(y.Strings.errors.indexOutOfRange(e))},t.prototype.addInline=function(e){"string"==typeof e?this.internalAddInline(new P(e)):this.internalAddInline(e)},t.prototype.removeInline=function(e){var t=this._inlines.indexOf(e);return t>=0&&(this._inlines[t].setParent(void 0),this._inlines.splice(t,1),!0)},t}(w);t.RichTextBlock=_;var A=function(e){function t(t,i){var n=e.call(this)||this;return n.name=t,n.value=i,n}return r(t,e),t.prototype.getSchemaKey=function(){return"Fact"},t.titleProperty=new m.StringProperty(m.Versions.v1_0,"title"),t.valueProperty=new m.StringProperty(m.Versions.v1_0,"value"),o([(0,m.property)(t.titleProperty)],t.prototype,"name",void 0),o([(0,m.property)(t.valueProperty)],t.prototype,"value",void 0),t}(m.SerializableObject);t.Fact=A;var T=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),Object.defineProperty(t.prototype,"useDefaultSizing",{get:function(){return!1},enumerable:!1,configurable:!0}),t.prototype.internalRender=function(){var e=void 0,t=this.hostConfig;if(this.facts.length>0){(e=document.createElement("table")).style.borderWidth="0px",e.style.borderSpacing="0px",e.style.borderStyle="none",e.style.borderCollapse="collapse",e.style.display="block",e.style.overflow="hidden",e.classList.add(t.makeCssClassName("ac-factset")),e.setAttribute("role","presentation");for(var i=0;i<this.facts.length;i++){var n=document.createElement("tr");i>0&&(n.style.marginTop=t.factSet.spacing+"px");var r=document.createElement("td");r.style.padding="0",r.classList.add(t.makeCssClassName("ac-fact-title")),t.factSet.title.maxWidth&&(r.style.maxWidth=t.factSet.title.maxWidth+"px"),r.style.verticalAlign="top";var o=new x;o.setParent(this),o.text=!this.facts[i].name&&this.isDesignMode()?"Title":this.facts[i].name,o.size=t.factSet.title.size,o.color=t.factSet.title.color,o.isSubtle=t.factSet.title.isSubtle,o.weight=t.factSet.title.weight,o.wrap=t.factSet.title.wrap,o.spacing=p.Spacing.None,u.appendChild(r,o.render()),u.appendChild(n,r),(r=document.createElement("td")).style.width="10px",u.appendChild(n,r),(r=document.createElement("td")).style.padding="0",r.style.verticalAlign="top",r.classList.add(t.makeCssClassName("ac-fact-value")),(o=new x).setParent(this),o.text=this.facts[i].value,o.size=t.factSet.value.size,o.color=t.factSet.value.color,o.isSubtle=t.factSet.value.isSubtle,o.weight=t.factSet.value.weight,o.wrap=t.factSet.value.wrap,o.spacing=p.Spacing.None,u.appendChild(r,o.render()),u.appendChild(n,r),u.appendChild(e,n)}}return e},t.prototype.getJsonTypeName=function(){return"FactSet"},t.factsProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_0,"facts",A),o([(0,m.property)(t.factsProperty)],t.prototype,"facts",void 0),t}(w);t.FactSet=T;var I=function(e){function t(t,i,n,r){var o=e.call(this,t,i)||this;return o.targetVersion=t,o.name=i,o.internalName=n,o.fallbackProperty=r,o}return r(t,e),t.prototype.getInternalName=function(){return this.internalName},t.prototype.parse=function(e,t,i){var n=void 0,r=t[this.name];if(void 0===r)return this.defaultValue;var o=!1;if("string"==typeof r){try{var s=c.SizeAndUnit.parse(r,!0);s.unit===p.SizeUnit.Pixel&&(n=s.physicalSize,o=!0)}catch(e){}!o&&this.fallbackProperty&&(o=this.fallbackProperty.isValidValue(r,i))}return o||i.logParseEvent(e,p.ValidationEvent.InvalidPropertyValue,y.Strings.errors.invalidPropertyValue(r,this.name)),n},t.prototype.toJSON=function(e,t,i,n){n.serializeValue(t,this.name,"number"!=typeof i||isNaN(i)?void 0:i+"px")},t}(m.PropertyDefinition),O=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.size=p.Size.Auto,t.style=p.ImageStyle.Default,t}return r(t,e),t.prototype.populateSchema=function(t){e.prototype.populateSchema.call(this,t),t.remove(w.heightProperty)},t.prototype.applySize=function(e){if(this.pixelWidth||this.pixelHeight)this.pixelWidth&&(e.style.width=this.pixelWidth+"px"),this.pixelHeight&&(e.style.height=this.pixelHeight+"px");else if(this.maxHeight){switch(this.size){case p.Size.Small:e.style.height=this.hostConfig.imageSizes.small+"px";break;case p.Size.Large:e.style.height=this.hostConfig.imageSizes.large+"px";break;default:e.style.height=this.hostConfig.imageSizes.medium+"px"}e.style.maxHeight=this.maxHeight+"px"}else{switch(this.size){case p.Size.Stretch:e.style.width="100%";break;case p.Size.Auto:e.style.maxWidth="100%";break;case p.Size.Small:e.style.width=this.hostConfig.imageSizes.small+"px";break;case p.Size.Large:e.style.width=this.hostConfig.imageSizes.large+"px";break;case p.Size.Medium:e.style.width=this.hostConfig.imageSizes.medium+"px"}e.style.maxHeight="100%"}},Object.defineProperty(t.prototype,"useDefaultSizing",{get:function(){return!1},enumerable:!1,configurable:!0}),t.prototype.internalRender=function(){var e=this,i=void 0;if(this.url){(i=document.createElement("div")).style.display="flex",i.style.alignItems="flex-start";var n=this.hostConfig;switch(this.getEffectiveHorizontalAlignment()){case p.HorizontalAlignment.Center:i.style.justifyContent="center";break;case p.HorizontalAlignment.Right:i.style.justifyContent="flex-end";break;default:i.style.justifyContent="flex-start"}var r=document.createElement("img");this.renderedImageElement=r,r.onload=function(t){_e(e)},r.onerror=function(t){if(e.renderedElement){var i=e.getRootElement();if(b(e.renderedElement),i&&i.designMode){var n=document.createElement("div");n.style.display="flex",n.style.alignItems="center",n.style.justifyContent="center",n.style.backgroundColor="#EEEEEE",n.style.color="black",n.innerText=":-(",n.style.padding="10px",e.applySize(n),e.renderedElement.appendChild(n)}}_e(e)},r.style.minWidth="0",r.classList.add(n.makeCssClassName("ac-image")),this.selectAction&&n.supportsInteractivity&&(r.onkeypress=function(t){e.selectAction&&e.selectAction.isEffectivelyEnabled()&&("Enter"===t.code||"Space"===t.code)&&(t.preventDefault(),t.cancelBubble=!0,e.selectAction.execute())},r.onclick=function(t){e.selectAction&&e.selectAction.isEffectivelyEnabled()&&(t.preventDefault(),t.cancelBubble=!0,e.selectAction.execute())},this.selectAction.setupElementForAccessibility(r),this.selectAction.isEffectivelyEnabled()&&r.classList.add(n.makeCssClassName("ac-selectable"))),this.applySize(r),this.style===p.ImageStyle.Person&&(r.style.borderRadius="50%",r.style.backgroundPosition="50% 50%",r.style.backgroundRepeat="no-repeat");var o=u.stringToCssColor(this.backgroundColor);o&&(r.style.backgroundColor=o),this.setImageSource(r);var s=this.preProcessPropertyValue(t.altTextProperty);s&&(r.alt=s),i.appendChild(r)}return i},t.prototype.getJsonTypeName=function(){return"Image"},t.prototype.getAllActions=function(){var t=e.prototype.getAllActions.call(this);return this.selectAction&&t.push(this.selectAction),t},t.prototype.getActionById=function(t){var i=e.prototype.getActionById.call(this,t);return!i&&this.selectAction&&(i=this.selectAction.getActionById(t)),i},t.prototype.getResourceInformation=function(){return this.url?[{url:this.url,mimeType:"image"}]:[]},t.prototype.setImageSource=function(e){var i=new M(this.forceLoad,this.url);i.configureImage(this),e.src=this.preProcessPropertyValue(t.urlProperty),i.resetImage(this)},t.urlProperty=new m.StringProperty(m.Versions.v1_0,"url"),t.altTextProperty=new m.StringProperty(m.Versions.v1_0,"altText"),t.backgroundColorProperty=new m.StringProperty(m.Versions.v1_1,"backgroundColor"),t.styleProperty=new m.EnumProperty(m.Versions.v1_0,"style",p.ImageStyle,p.ImageStyle.Default),t.sizeProperty=new m.EnumProperty(m.Versions.v1_0,"size",p.Size,p.Size.Auto),t.pixelWidthProperty=new I(m.Versions.v1_1,"width","pixelWidth"),t.pixelHeightProperty=new I(m.Versions.v1_1,"height","pixelHeight",w.heightProperty),t.selectActionProperty=new S(m.Versions.v1_1,"selectAction",["Action.ShowCard"]),t.shouldForceLoadProperty=new m.BoolProperty(m.Versions.v1_6,"forceLoad",!1),o([(0,m.property)(t.urlProperty)],t.prototype,"url",void 0),o([(0,m.property)(t.altTextProperty)],t.prototype,"altText",void 0),o([(0,m.property)(t.backgroundColorProperty)],t.prototype,"backgroundColor",void 0),o([(0,m.property)(t.sizeProperty)],t.prototype,"size",void 0),o([(0,m.property)(t.styleProperty)],t.prototype,"style",void 0),o([(0,m.property)(t.pixelWidthProperty)],t.prototype,"pixelWidth",void 0),o([(0,m.property)(t.pixelHeightProperty)],t.prototype,"pixelHeight",void 0),o([(0,m.property)(t.selectActionProperty)],t.prototype,"selectAction",void 0),o([(0,m.property)(t.shouldForceLoadProperty)],t.prototype,"forceLoad",void 0),t}(w);t.Image=O;var M=function(){function e(e,t){this.doForceLoad=e,this.url=t,t&&t.length&&e&&(this.uniqueHash="?"+Date.now(),this.urlWithForceLoadOption=t+this.uniqueHash)}return e.prototype.configureImage=function(e){this.urlWithForceLoadOption&&this.urlWithForceLoadOption.length&&(e.url=this.urlWithForceLoadOption)},e.prototype.resetImage=function(e){e.url=this.url},e}(),V=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.allowVerticalOverflow=!1,t}return r(t,e),t.prototype.populateSchema=function(i){e.prototype.populateSchema.call(this,i),this.isSelectable||i.remove(t.selectActionProperty)},t.prototype.isElementAllowed=function(e){return this.hostConfig.supportsInteractivity||!e.isInteractive},t.prototype.applyPadding=function(){if(e.prototype.applyPadding.call(this),this.renderedElement){var t=new c.SpacingDefinition;this.getEffectivePadding()&&(t=this.hostConfig.paddingDefinitionToSpacingDefinition(this.getEffectivePadding())),this.renderedElement.style.paddingTop=t.top+"px",this.renderedElement.style.paddingRight=t.right+"px",this.renderedElement.style.paddingBottom=t.bottom+"px",this.renderedElement.style.paddingLeft=t.left+"px",this.renderedElement.style.marginRight="0",this.renderedElement.style.marginLeft="0"}},Object.defineProperty(t.prototype,"isSelectable",{get:function(){return!1},enumerable:!1,configurable:!0}),t.prototype.forbiddenChildElements=function(){return[]},t.prototype.releaseDOMResources=function(){e.prototype.releaseDOMResources.call(this);for(var t=0;t<this.getItemCount();t++)this.getItemAt(t).releaseDOMResources()},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t);for(var i=0;i<this.getItemCount();i++){var n=this.getItemAt(i);!this.hostConfig.supportsInteractivity&&n.isInteractive&&t.addFailure(this,p.ValidationEvent.InteractivityNotAllowed,y.Strings.errors.interactivityNotAllowed()),this.isElementAllowed(n)||t.addFailure(this,p.ValidationEvent.InteractivityNotAllowed,y.Strings.errors.elementTypeNotAllowed(n.getJsonTypeName())),n.internalValidateProperties(t)}this._selectAction&&this._selectAction.internalValidateProperties(t)},t.prototype.render=function(){var t=this,i=e.prototype.render.call(this);if(i){var n=this.hostConfig;this.allowVerticalOverflow&&(i.style.overflowX="hidden",i.style.overflowY="auto"),i&&this.isSelectable&&this._selectAction&&n.supportsInteractivity&&(i.onclick=function(e){t._selectAction&&t._selectAction.isEffectivelyEnabled()&&(e.preventDefault(),e.cancelBubble=!0,t._selectAction.execute())},i.onkeypress=function(e){t._selectAction&&t._selectAction.isEffectivelyEnabled()&&("Enter"===e.code||"Space"===e.code)&&(e.preventDefault(),e.cancelBubble=!0,t._selectAction.execute())},this._selectAction.setupElementForAccessibility(i),this._selectAction.isEffectivelyEnabled()&&i.classList.add(n.makeCssClassName("ac-selectable")))}return i},t.prototype.updateLayout=function(t){if(void 0===t&&(t=!0),e.prototype.updateLayout.call(this,t),t)for(var i=0;i<this.getItemCount();i++)this.getItemAt(i).updateLayout()},t.prototype.getAllInputs=function(e){void 0===e&&(e=!0);for(var t=[],i=0;i<this.getItemCount();i++)t.push.apply(t,this.getItemAt(i).getAllInputs(e));return t},t.prototype.getAllActions=function(){for(var t=e.prototype.getAllActions.call(this),i=0;i<this.getItemCount();i++)t.push.apply(t,this.getItemAt(i).getAllActions());return this._selectAction&&t.push(this._selectAction),t},t.prototype.getResourceInformation=function(){for(var e=[],t=0;t<this.getItemCount();t++)e.push.apply(e,this.getItemAt(t).getResourceInformation());return e},t.prototype.getElementById=function(t){var i=e.prototype.getElementById.call(this,t);if(!i)for(var n=0;n<this.getItemCount()&&!(i=this.getItemAt(n).getElementById(t));n++);return i},t.prototype.findDOMNodeOwner=function(t){for(var i,n=void 0,r=0;r<this.getItemCount();r++)if(n=this.getItemAt(r).findDOMNodeOwner(t))return n;for(r=0;r<this.getActionCount();r++)if(n=null===(i=this.getActionAt(r))||void 0===i?void 0:i.findDOMNodeOwner(t))return n;return e.prototype.findDOMNodeOwner.call(this,t)},t.selectActionProperty=new S(m.Versions.v1_1,"selectAction",["Action.ShowCard"]),o([(0,m.property)(t.selectActionProperty)],t.prototype,"_selectAction",void 0),t}(w);t.CardElementContainer=V;var k=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._images=[],t.imageSize=p.ImageSize.Medium,t.presentationStyle=p.ImageSetPresentationStyle.Default,t.pixelOffset=0,t}return r(t,e),t.prototype.internalRender=function(){var e=void 0;if(this._images.length>0){(e=document.createElement("div")).style.display="flex",e.style.flexWrap="wrap";for(var t=0,i=this._images;t<i.length;t++){var n=i[t];switch(this.imageSize){case p.ImageSize.Small:n.size=p.Size.Small;break;case p.ImageSize.Large:n.size=p.Size.Large;break;default:n.size=p.Size.Medium}n.maxHeight=this.hostConfig.imageSet.maxImageHeight;var r=n.render();r&&(r.style.display="inline-flex",r.style.margin="0px",this.presentationStyle==p.ImageSetPresentationStyle.Default&&(r.style.marginRight="10px"),u.appendChild(e,r))}this.presentationStyle==p.ImageSetPresentationStyle.Stacked&&this.applyStackedPresentationStyle()}return e},t.prototype.applyStackedPresentationStyle=function(){if(this._images[0].renderedImageElement){var e=z.parseNumericPixelDimension(this._images[0].renderedImageElement.style.height),t=this.getEffectiveBackgroundColor();e&&new z(this.pixelOffset,e,t).configureImagesArrayAsStackedLayout(this._images)}},t.prototype.getEffectiveBackgroundColor=function(){var e=this.getParentContainer(),t=null==e?void 0:e.getEffectiveStyle(),i=this.hostConfig.containerStyles.getStyleByName(t,this.hostConfig.containerStyles.getStyleByName(this.defaultStyle));return u.stringToCssColor(i.backgroundColor)},t.prototype.getItemCount=function(){return this._images.length},t.prototype.getItemAt=function(e){return this._images[e]},t.prototype.getFirstVisibleRenderedItem=function(){return this._images&&this._images.length>0?this._images[0]:void 0},t.prototype.getLastVisibleRenderedItem=function(){return this._images&&this._images.length>0?this._images[this._images.length-1]:void 0},t.prototype.removeItem=function(e){if(e instanceof O){var t=this._images.indexOf(e);if(t>=0)return this._images.splice(t,1),e.setParent(void 0),this.updateLayout(),!0}return!1},t.prototype.getJsonTypeName=function(){return"ImageSet"},t.prototype.addImage=function(e){if(e.parent)throw new Error("This image already belongs to another ImageSet");this._images.push(e),e.setParent(this)},t.prototype.indexOf=function(e){return e instanceof O?this._images.indexOf(e):-1},t.imagesProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_0,"images",O,(function(e,t){t.setParent(e)})),t.imageSizeProperty=new m.EnumProperty(m.Versions.v1_0,"imageSize",p.ImageSize,p.ImageSize.Medium),t.imagePresentationStyle=new m.EnumProperty(m.Versions.v1_6,"style",p.ImageSetPresentationStyle,p.ImageSetPresentationStyle.Default),t.pixelOffset=new m.NumProperty(m.Versions.v1_6,"offset",0,void 0),o([(0,m.property)(t.imagesProperty)],t.prototype,"_images",void 0),o([(0,m.property)(t.imageSizeProperty)],t.prototype,"imageSize",void 0),o([(0,m.property)(t.imagePresentationStyle)],t.prototype,"presentationStyle",void 0),o([(0,m.property)(t.pixelOffset)],t.prototype,"pixelOffset",void 0),t}(V);t.ImageSet=k;var z=function(){function e(e,t,i){this.sign45=.7071,this.maxImageCounts=2,this.offset=0,this.normalizationConstant=0,this.border=5,this.dimension=0,this.dimension=t,this.normalizationConstant=2*(t*this.sign45-.5*t),this.offset=this.sign45*(Math.max(e,-t)-this.normalizationConstant),this.style=i||""}return e.prototype.moveImageRight=function(e){e.style.marginLeft=this.offset+"px"},e.prototype.moveImageUp=function(e){e.style.marginBottom=this.offset+this.dimension+"px"},e.prototype.moveImageDown=function(e){e.style.marginTop=this.offset+this.dimension+"px"},e.prototype.makeImageRound=function(e){e.style.borderRadius="50%",e.style.backgroundPosition="50% 50%",e.style.backgroundRepeat="no-repeat"},e.prototype.applyBorder=function(e){e.style.height=this.dimension+2*this.border+"px",e.style.border=this.border+"px solid "+this.style},e.prototype.configureImageForBottomLeft=function(e){this.moveImageDown(e),this.makeImageRound(e),this.applyBorder(e),e.style.zIndex="2"},e.prototype.configureImageForTopRight=function(e){this.moveImageUp(e),this.moveImageRight(e),this.makeImageRound(e),e.style.zIndex="1"},e.prototype.configureImagesArrayAsStackedLayout=function(e){1==e.length?e[0].renderedImageElement&&this.makeImageRound(e[0].renderedImageElement):e.length<=this.maxImageCounts&&e[0].renderedImageElement&&e[1].renderedImageElement&&(this.configureImageForBottomLeft(e[0].renderedImageElement),this.configureImageForTopRight(e[1].renderedImageElement))},e.parseNumericPixelDimension=function(e){if("px"==(null==e?void 0:e.substring(e.length-2)))return parseInt(e.substring(0,e.length-2))},e}(),L=function(e){function t(t,i){var n=e.call(this)||this;return n.url=t,n.mimeType=i,n}return r(t,e),t.prototype.isValid=function(){return!(!this.mimeType||!this.url)},t.mimeTypeProperty=new m.StringProperty(m.Versions.v1_1,"mimeType"),t.urlProperty=new m.StringProperty(m.Versions.v1_1,"url"),o([(0,m.property)(t.mimeTypeProperty)],t.prototype,"mimeType",void 0),o([(0,m.property)(t.urlProperty)],t.prototype,"url",void 0),t}(m.SerializableObject);t.ContentSource=L;var D=function(e){function t(t,i,n){var r=e.call(this,t,i)||this;return r.label=n,r}return r(t,e),t.prototype.getSchemaKey=function(){return"CaptionSource"},t.prototype.render=function(){var e=void 0;return this.isValid()&&((e=document.createElement("track")).src=this.url,e.kind="captions",e.label=this.label),e},t.labelProperty=new m.StringProperty(m.Versions.v1_6,"label"),o([(0,m.property)(t.labelProperty)],t.prototype,"label",void 0),t}(L);t.CaptionSource=D;var N=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getSchemaKey=function(){return"MediaSource"},t.prototype.render=function(){var e=void 0;return this.isValid()&&((e=document.createElement("source")).src=this.url,e.type=this.mimeType),e},t}(L);t.MediaSource=N;var R=function(){function e(){}return e.prototype.play=function(){},Object.defineProperty(e.prototype,"posterUrl",{get:function(){return this._posterUrl},set:function(e){this._posterUrl=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"selectedMediaType",{get:function(){},enumerable:!1,configurable:!0}),e}();t.MediaPlayer=R;var $=function(e){function t(t){var i=e.call(this)||this;return i.owner=t,i._selectedSources=[],i._captionSources=[],i.processSources(),i}return r(t,e),t.prototype.processSources=function(){var e;this._selectedSources=[],this._captionSources=[],this._selectedMediaType=void 0;for(var i=0,n=this.owner.sources;i<n.length;i++){var r=n[i],o=r.mimeType?r.mimeType.split("/"):[];if(2===o.length){if(!this._selectedMediaType){var s=t.supportedMediaTypes.indexOf(o[0]);s>=0&&(this._selectedMediaType=t.supportedMediaTypes[s])}o[0]===this._selectedMediaType&&this._selectedSources.push(r)}}(e=this._captionSources).push.apply(e,this.owner.captionSources)},t.prototype.canPlay=function(){return this._selectedSources.length>0},t.prototype.fetchVideoDetails=function(){return s(this,void 0,void 0,(function(){return a(this,(function(e){return[2]}))}))},t.prototype.render=function(){"video"===this._selectedMediaType?this._mediaElement=document.createElement("video"):this._mediaElement=document.createElement("audio"),this._mediaElement.setAttribute("aria-label",this.owner.altText?this.owner.altText:y.Strings.defaults.mediaPlayerAriaLabel()),this._mediaElement.setAttribute("webkit-playsinline",""),this._mediaElement.setAttribute("playsinline",""),this._mediaElement.setAttribute("crossorigin",""),this._mediaElement.autoplay=!0,this._mediaElement.controls=!0,u.isMobileOS()&&(this._mediaElement.muted=!0),this._mediaElement.preload="none",this._mediaElement.style.width="100%";for(var e=0,t=this.owner.sources;e<t.length;e++){var i=t[e].render();u.appendChild(this._mediaElement,i)}for(var n=0,r=this.owner.captionSources;n<r.length;n++){var o=r[n];if("vtt"==o.mimeType){var s=o.render();u.appendChild(this._mediaElement,s)}}return this._mediaElement},t.prototype.play=function(){this._mediaElement&&this._mediaElement.play()},Object.defineProperty(t.prototype,"selectedMediaType",{get:function(){return this._selectedMediaType},enumerable:!1,configurable:!0}),t.supportedMediaTypes=["audio","video"],t}(R);t.HTML5MediaPlayer=$;var B=function(e){function t(t){return e.call(this)||this}return r(t,e),t}(R);t.CustomMediaPlayer=B;var H=function(e){function t(t,i){var n=e.call(this,t)||this;return n.iFrameTitle=i,t.length>=2&&(n._videoId=t[1]),n}return r(t,e),t.prototype.canPlay=function(){return void 0!==this._videoId},t.prototype.render=function(){var e=document.createElement("div");e.style.position="relative",e.style.width="100%",e.style.height="0",e.style.paddingBottom="56.25%";var t=document.createElement("iframe");return t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.src=this.getEmbedVideoUrl(),t.frameBorder="0",this.iFrameTitle&&(t.title=this.iFrameTitle),t.allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",t.allowFullscreen=!0,e.appendChild(t),e},Object.defineProperty(t.prototype,"videoId",{get:function(){return this._videoId},enumerable:!1,configurable:!0}),t}(B);t.IFrameMediaMediaPlayer=H;var j=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.fetchVideoDetails=function(){return s(this,void 0,void 0,(function(){var e,t,i;return a(this,(function(n){switch(n.label){case 0:return e="https://vimeo.com/api/oembed.json?url="+this.getEmbedVideoUrl(),[4,fetch(e)];case 1:return(t=n.sent()).ok?[4,t.json()]:[3,3];case 2:i=n.sent(),this.posterUrl=i.thumbnail_url,n.label=3;case 3:return[2]}}))}))},t.prototype.getEmbedVideoUrl=function(){return"https://player.vimeo.com/video/"+this.videoId+"?autoplay=1"},t}(H);t.VimeoPlayer=j;var F=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.fetchVideoDetails=function(){return s(this,void 0,void 0,(function(){var e,t,i;return a(this,(function(n){switch(n.label){case 0:return e="https://api.dailymotion.com/video/"+this.videoId+"?fields=thumbnail_720_url",[4,fetch(e)];case 1:return(t=n.sent()).ok?[4,t.json()]:[3,3];case 2:i=n.sent(),this.posterUrl=i.thumbnail_720_url,n.label=3;case 3:return[2]}}))}))},t.prototype.getEmbedVideoUrl=function(){return"https://www.dailymotion.com/embed/video/"+this.videoId+"?autoplay=1"},t}(H);t.DailymotionPlayer=F;var W=function(e){function t(t,i){var n=e.call(this,t,i)||this;return n.iFrameTitle=i,t.length>=3&&void 0!==t[2]&&(n._startTimeIndex=parseInt(t[2])),n}return r(t,e),t.prototype.fetchVideoDetails=function(){return s(this,void 0,void 0,(function(){return a(this,(function(e){return this.posterUrl=this.videoId?"https://img.youtube.com/vi/"+this.videoId+"/maxresdefault.jpg":void 0,[2]}))}))},t.prototype.getEmbedVideoUrl=function(){var e="https://www.youtube.com/embed/"+this.videoId+"?autoplay=1";return void 0!==this._startTimeIndex&&(e+="&start="+this._startTimeIndex),e},t}(H);t.YouTubePlayer=W;var G,q=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.sources=[],t.captionSources=[],t}return r(t,e),t.prototype.createMediaPlayer=function(){for(var e=0,i=t.customMediaPlayers;e<i.length;e++)for(var n=i[e],r=0,o=this.sources;r<o.length;r++){var s=o[r];if(s.url)for(var a=0,l=n.urlPatterns;a<l.length;a++){var p=l[a].exec(s.url);if(null!==p)return n.createMediaPlayer(p)}}return new $(this)},t.prototype.handlePlayButtonInvoke=function(e){if(this.hostConfig.media.allowInlinePlayback){if(e.preventDefault(),e.cancelBubble=!0,this.renderedElement){var i=this._mediaPlayer.render();b(this.renderedElement),this.renderedElement.appendChild(i),this._mediaPlayer.play(),i.focus()}}else t.onPlay&&(e.preventDefault(),e.cancelBubble=!0,t.onPlay(this))},t.prototype.displayPoster=function(){return s(this,void 0,void 0,(function(){var e,t,i,n,r,o,s=this;return a(this,(function(a){return this.renderedElement&&((e=document.createElement("div")).className=this.hostConfig.makeCssClassName("ac-media-poster"),e.setAttribute("role","contentinfo"),e.setAttribute("aria-label",this.altText?this.altText:y.Strings.defaults.mediaPlayerAriaLabel()),e.style.position="relative",e.style.display="flex",(t=this.poster?this.poster:this._mediaPlayer.posterUrl)||(t=this.hostConfig.media.defaultPoster),t?((i=document.createElement("img")).style.width="100%",i.style.height="100%",i.setAttribute("role","presentation"),i.onerror=function(t){i.parentNode&&i.parentNode.removeChild(i),e.classList.add("empty"),e.style.minHeight="150px"},i.src=t,e.appendChild(i)):(e.classList.add("empty"),e.style.minHeight="150px"),this.hostConfig.supportsInteractivity&&this._mediaPlayer.canPlay()&&((n=document.createElement("div")).tabIndex=0,n.setAttribute("role","button"),n.setAttribute("aria-label",y.Strings.defaults.mediaPlayerPlayMedia()),n.className=this.hostConfig.makeCssClassName("ac-media-playButton"),n.style.display="flex",n.style.alignItems="center",n.style.justifyContent="center",n.onclick=function(e){s.handlePlayButtonInvoke(e)},n.onkeypress=function(e){"Enter"!==e.code&&"Space"!==e.code||s.handlePlayButtonInvoke(e)},(r=document.createElement("div")).className=this.hostConfig.makeCssClassName("ac-media-playButton-arrow"),r.style.width="12px",r.style.height="15px",r.style.borderTopWidth="7.5px",r.style.borderBottomWidth="7.5px",r.style.borderLeftWidth="12px",r.style.borderRightWidth="0",r.style.borderStyle="solid",r.style.borderTopColor="transparent",r.style.borderRightColor="transparent",r.style.borderBottomColor="transparent",r.style.transform="translate(1.2px,0px)",n.appendChild(r),(o=document.createElement("div")).style.position="absolute",o.style.left="0",o.style.top="0",o.style.width="100%",o.style.height="100%",o.style.display="flex",o.style.justifyContent="center",o.style.alignItems="center",o.appendChild(n),e.appendChild(o)),b(this.renderedElement),this.renderedElement.appendChild(e)),[2]}))}))},t.prototype.internalRender=function(){var e=document.createElement("div");return e.className=this.hostConfig.makeCssClassName("ac-media"),e},t.prototype.render=function(){var t=this,i=e.prototype.render.call(this);return i&&(this._mediaPlayer=this.createMediaPlayer(),this._mediaPlayer.fetchVideoDetails().then((function(){return t.displayPoster()}))),i},t.prototype.releaseDOMResources=function(){e.prototype.releaseDOMResources.call(this),this.displayPoster()},t.prototype.getJsonTypeName=function(){return"Media"},t.prototype.getResourceInformation=function(){var e=[];if(this._mediaPlayer){var t=this.poster?this.poster:this.hostConfig.media.defaultPoster;t&&e.push({url:t,mimeType:"image"})}for(var i=0,n=this.sources;i<n.length;i++){var r=n[i];r.isValid()&&e.push({url:r.url,mimeType:r.mimeType})}for(var o=0,s=this.captionSources;o<s.length;o++){var a=s[o];a.isValid()&&e.push({url:a.url,mimeType:a.mimeType})}return e},Object.defineProperty(t.prototype,"selectedMediaType",{get:function(){return this._mediaPlayer.selectedMediaType},enumerable:!1,configurable:!0}),t.customMediaPlayers=[{urlPatterns:[/^(?:https?:\/\/)?(?:www.)?youtube.com\/watch\?(?=.*v=([\w\d-_]+))(?=(?:.*t=(\d+))?).*/gi,/^(?:https?:\/\/)?youtu.be\/([\w\d-_]+)(?:\?t=(\d+))?/gi],createMediaPlayer:function(e){return new W(e,y.Strings.defaults.youTubeVideoPlayer())}},{urlPatterns:[/^(?:https?:\/\/)?vimeo.com\/([\w\d-_]+).*/gi],createMediaPlayer:function(e){return new j(e,y.Strings.defaults.vimeoVideoPlayer())}},{urlPatterns:[/^(?:https?:\/\/)?(?:www.)?dailymotion.com\/video\/([\w\d-_]+).*/gi],createMediaPlayer:function(e){return new F(e,y.Strings.defaults.dailymotionVideoPlayer())}}],t.sourcesProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_1,"sources",N),t.captionSourcesProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_6,"captionSources",D),t.posterProperty=new m.StringProperty(m.Versions.v1_1,"poster"),t.altTextProperty=new m.StringProperty(m.Versions.v1_1,"altText"),o([(0,m.property)(t.sourcesProperty)],t.prototype,"sources",void 0),o([(0,m.property)(t.captionSourcesProperty)],t.prototype,"captionSources",void 0),o([(0,m.property)(t.posterProperty)],t.prototype,"poster",void 0),o([(0,m.property)(t.altTextProperty)],t.prototype,"altText",void 0),t}(w);t.Media=q,function(e){e[e.InitialRender=0]="InitialRender",e[e.MouseEnterOnCard=1]="MouseEnterOnCard",e[e.MouseLeaveOnCard=2]="MouseLeaveOnCard",e[e.FocusLeave=3]="FocusLeave"}(G||(G={}));var U=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.inputStyle=p.InputStyle.Default,t}return r(t,e),t.prototype.getAllLabelIds=function(){var e=[];return this.labelledBy&&e.push(this.labelledBy),this._renderedLabelElement&&e.push(this._renderedLabelElement.id),this._renderedErrorMessageElement&&e.push(this._renderedErrorMessageElement.id),e},t.prototype.updateInputControlAriaLabelledBy=function(){if(this._renderedInputControlElement){var e=this.getAllLabelIds();e.length>0?this._renderedInputControlElement.setAttribute("aria-labelledby",e.join(" ")):this._renderedInputControlElement.removeAttribute("aria-labelledby")}},Object.defineProperty(t.prototype,"isNullable",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"renderedInputControlElement",{get:function(){return this._renderedInputControlElement},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"inputControlContainerElement",{get:function(){return this._inputControlContainerElement},enumerable:!1,configurable:!0}),t.prototype.overrideInternalRender=function(){var e=this,t=this.hostConfig;this._outerContainerElement=document.createElement("div"),this._outerContainerElement.style.display="flex",this.labelPosition===p.InputLabelPosition.Inline?this._outerContainerElement.style.flexDirection="row":this._outerContainerElement.style.flexDirection="column";var i=u.generateUniqueId();if(this.label){var n=new _;n.setParent(this),n.forElementId=i;var r=new P(this.label);if(n.addInline(r),this.isRequired){r.init(t.inputs.label.requiredInputs);var o=new P(t.inputs.label.requiredInputs.suffix);o.color=t.inputs.label.requiredInputs.suffixColor,o.ariaHidden=!0,n.addInline(o)}else r.init(t.inputs.label.optionalInputs);this._renderedLabelElement=n.render(),this._renderedLabelElement&&(this._renderedLabelElement.id=u.generateUniqueId(),this.labelPosition===p.InputLabelPosition.Inline?this._renderedLabelElement.style.alignSelf="center":this._renderedLabelElement.style.marginBottom=t.getEffectiveSpacing(t.inputs.label.inputSpacing)+"px",this._outerContainerElement.appendChild(this._renderedLabelElement))}if(this._inputControlContainerElement=document.createElement("div"),this._inputControlContainerElement.className=t.makeCssClassName("ac-input-container"),this._inputControlContainerElement.style.display="flex","stretch"===this.height&&(this._inputControlContainerElement.style.alignItems="stretch",this._inputControlContainerElement.style.flex="1 1 auto"),this._renderedInputControlElement=this.internalRender(),this._renderedInputControlElement){if(this._renderedInputControlElement.id=i,this._renderedInputControlElement.style.minWidth="0px",this.isNullable&&this.isRequired&&(this._renderedInputControlElement.setAttribute("aria-required","true"),this._renderedInputControlElement.classList.add(t.makeCssClassName("ac-input-required"))),this._inputControlContainerElement.appendChild(this._renderedInputControlElement),this._outerContainerElement.appendChild(this._inputControlContainerElement),this._renderedLabelElement&&this.labelPosition===p.InputLabelPosition.Inline)if(this.labelWidth){if(this.labelWidth.unit==p.SizeUnit.Weight){var s=this.labelWidth.physicalSize;this._renderedLabelElement.style.width=s.toString()+"%",this._inputControlContainerElement.style.width=(100-s).toString()+"%"}else if(this.labelWidth.unit==p.SizeUnit.Pixel){var a=this.labelWidth.physicalSize;this._renderedLabelElement.style.width=a.toString()+"px"}}else{var l=t.inputs.label.width;this._renderedLabelElement.style.width=l.toString()+"%",this._inputControlContainerElement.style.width=100-l+"%"}return this.updateVisualState(G.InitialRender),this._renderedInputControlElement&&(this._renderedInputControlElement.onblur=function(t){e.updateVisualState(G.FocusLeave)}),this.updateInputControlAriaLabelledBy(),this._outerContainerElement}this.resetDirtyState()},t.prototype.valueChanged=function(){var e,t,i;this.getRootElement().updateActionsEnabledState(),this.isValid()&&this.resetValidationFailureCue(),this.onValueChanged&&this.onValueChanged(this),(i=(t=(e=this).getRootElement())&&t.onInputValueChanged?t.onInputValueChanged:Ne.onInputValueChanged)&&i(e)},t.prototype.resetValidationFailureCue=function(){this.renderedInputControlElement&&(this instanceof Q&&this.isDynamicTypeahead()?this.removeValidationFailureCue():this.renderedInputControlElement.classList.remove(this.hostConfig.makeCssClassName("ac-input-validation-failed")),this.updateInputControlAriaLabelledBy(),this._renderedErrorMessageElement&&(this._outerContainerElement.removeChild(this._renderedErrorMessageElement),this._renderedErrorMessageElement=void 0))},t.prototype.showValidationErrorMessage=function(){if(this.renderedElement&&this.errorMessage&&c.GlobalSettings.displayInputValidationErrors){var e=new x;e.setParent(this),e.text=this.errorMessage,e.wrap=!0,e.init(this.hostConfig.inputs.errorMessage),this._renderedErrorMessageElement=e.render(),this._renderedErrorMessageElement&&(this._renderedErrorMessageElement.id=u.generateUniqueId(),this._outerContainerElement.appendChild(this._renderedErrorMessageElement),this.updateInputControlAriaLabelledBy())}},Object.defineProperty(t.prototype,"allowRevealOnHoverStyle",{get:function(){return this.hostConfig.inputs&&this.hostConfig.inputs.allowRevealOnHoverStyle},enumerable:!1,configurable:!0}),t.prototype.shouldHideInputAdornersForRevealOnHover=function(e,t){var i=e===document.activeElement,n=this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onhover"),r=e.classList.contains(n);return t===G.InitialRender||t===G.FocusLeave&&!r||t===G.MouseLeaveOnCard&&!i},t.prototype.updateVisualState=function(e){this.allowRevealOnHoverStyle&&this._renderedInputControlElement&&this.inputStyle===p.InputStyle.RevealOnHover&&(e===G.InitialRender?this._renderedInputControlElement.classList.add(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onrender")):e===G.MouseEnterOnCard?this._renderedInputControlElement.classList.add(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onhover")):e===G.MouseLeaveOnCard&&this._renderedInputControlElement.classList.remove(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onhover")))},t.prototype.focus=function(){this._renderedInputControlElement&&this._renderedInputControlElement.focus()},t.prototype.isValid=function(){return!0},t.prototype.isDirty=function(){return this.value!==this._oldValue},t.prototype.resetDirtyState=function(){this._oldValue=this.value},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),this.id||t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.inputsMustHaveUniqueId()),this.isRequired&&(this.label||t.addFailure(this,p.ValidationEvent.RequiredInputsShouldHaveLabel,"Required inputs should have a label"),this.errorMessage||t.addFailure(this,p.ValidationEvent.RequiredInputsShouldHaveErrorMessage,"Required inputs should have an error message"))},t.prototype.validateValue=function(){this.resetValidationFailureCue();var e=this.isRequired?this.isSet()&&this.isValid():this.isValid();return!e&&this.renderedInputControlElement&&(this instanceof Q&&this.isDynamicTypeahead()?this.showValidationFailureCue():this.renderedInputControlElement.classList.add(this.hostConfig.makeCssClassName("ac-input-validation-failed")),this.showValidationErrorMessage()),e},t.prototype.getAllInputs=function(e){return void 0===e&&(e=!0),[this]},t.prototype.render=function(){var t=e.prototype.render.call(this);return this.resetDirtyState(),t},Object.defineProperty(t.prototype,"isInteractive",{get:function(){return!0},enumerable:!1,configurable:!0}),t.labelProperty=new m.StringProperty(m.Versions.v1_3,"label",!0),t.isRequiredProperty=new m.BoolProperty(m.Versions.v1_3,"isRequired",!1),t.errorMessageProperty=new m.StringProperty(m.Versions.v1_3,"errorMessage",!0),t.inputStyleProperty=new m.EnumProperty(m.Versions.v1_6,"inputStyle",p.InputStyle,p.InputStyle.Default,[{value:p.InputStyle.RevealOnHover},{value:p.InputStyle.Default}]),t.labelWidthProperty=new m.CustomProperty(m.Versions.v1_6,"labelWidth",(function(e,t,i,n){var r=t.defaultValue,o=i[t.name],s=!1;if("number"!=typeof o||isNaN(o))if("string"==typeof o)try{r=c.SizeAndUnit.parse(o)}catch(e){s=!0}else s=!0;else((r=new c.SizeAndUnit(o,p.SizeUnit.Weight)).physicalSize<0||r.physicalSize>100)&&(s=!0);return s&&(n.logParseEvent(e,p.ValidationEvent.InvalidPropertyValue,y.Strings.errors.invalidInputLabelWidth()),r=void 0),r}),(function(e,t,i,n,r){n instanceof c.SizeAndUnit&&(n.unit===p.SizeUnit.Pixel?r.serializeValue(i,"labelWidth",n.physicalSize+"px"):r.serializeNumber(i,"labelWidth",n.physicalSize))}),void 0),t.labelPositionProperty=new m.EnumProperty(m.Versions.v1_6,"labelPosition",p.InputLabelPosition,p.InputLabelPosition.Above,[{value:p.InputLabelPosition.Inline},{value:p.InputLabelPosition.Above}]),o([(0,m.property)(t.labelProperty)],t.prototype,"label",void 0),o([(0,m.property)(t.isRequiredProperty)],t.prototype,"isRequired",void 0),o([(0,m.property)(t.errorMessageProperty)],t.prototype,"errorMessage",void 0),o([(0,m.property)(t.inputStyleProperty)],t.prototype,"inputStyle",void 0),o([(0,m.property)(t.labelWidthProperty)],t.prototype,"labelWidth",void 0),o([(0,m.property)(t.labelPositionProperty)],t.prototype,"labelPosition",void 0),t}(w);t.Input=U;var J=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isMultiline=!1,t.style=p.InputTextStyle.Text,t}return r(t,e),t.prototype.setupInput=function(e){var t=this;e.style.flex="1 1 auto",e.tabIndex=this.isDesignMode()?-1:0,this.placeholder&&(e.placeholder=this.placeholder,e.setAttribute("aria-label",this.placeholder)),this.defaultValue&&(e.value=this.defaultValue),this.maxLength&&this.maxLength>0&&(e.maxLength=this.maxLength),e.oninput=function(){t.valueChanged()},e.onkeypress=function(e){e.ctrlKey&&"Enter"===e.code&&t.inlineAction&&t.inlineAction.isEffectivelyEnabled()&&t.inlineAction.execute()}},t.prototype.internalRender=function(){var e;return this.isMultiline&&this.style!==p.InputTextStyle.Password?((e=document.createElement("textarea")).className=this.hostConfig.makeCssClassName("ac-input","ac-textInput","ac-multiline"),"stretch"===this.height&&(e.style.height="initial")):((e=document.createElement("input")).className=this.hostConfig.makeCssClassName("ac-input","ac-textInput"),e.type=p.InputTextStyle[this.style].toLowerCase()),this.setupInput(e),e},t.prototype.overrideInternalRender=function(){var t=this,i=e.prototype.overrideInternalRender.call(this);if(this.inlineAction){var n=document.createElement("button");if(n.className=this.hostConfig.makeCssClassName(this.inlineAction.isEffectivelyEnabled()?"ac-inlineActionButton":"ac-inlineActionButton-disabled"),n.onclick=function(e){t.inlineAction&&t.inlineAction.isEffectivelyEnabled()&&(e.preventDefault(),e.cancelBubble=!0,t.inlineAction.execute())},this.inlineAction.iconUrl){n.classList.add("iconOnly");var r=document.createElement("img");r.style.height="100%",r.setAttribute("role","presentation"),r.style.display="none",r.onload=function(){r.style.removeProperty("display")},r.onerror=function(){n.removeChild(r),n.classList.remove("iconOnly"),n.classList.add("textOnly"),n.textContent=t.inlineAction&&t.inlineAction.title?t.inlineAction.title:y.Strings.defaults.inlineActionTitle()},r.src=this.inlineAction.iconUrl,n.appendChild(r),n.title=this.inlineAction.title?this.inlineAction.title:y.Strings.defaults.inlineActionTitle()}else n.classList.add("textOnly"),n.textContent=this.inlineAction.title?this.inlineAction.title:y.Strings.defaults.inlineActionTitle();this.inlineAction.setupElementForAccessibility(n,!0),n.style.marginLeft="8px",this.inputControlContainerElement.appendChild(n)}return i},t.prototype.updateVisualState=function(t){this.allowRevealOnHoverStyle&&(this.inlineAction||this.isMultiline||e.prototype.updateVisualState.call(this,t))},t.prototype.getJsonTypeName=function(){return"Input.Text"},t.prototype.getAllActions=function(){var t=e.prototype.getAllActions.call(this);return this.inlineAction&&t.push(this.inlineAction),t},t.prototype.getActionById=function(t){var i=e.prototype.getActionById.call(this,t);return!i&&this.inlineAction&&(i=this.inlineAction.getActionById(t)),i},t.prototype.isSet=function(){return!!this.value},t.prototype.isValid=function(){return!this.value||!this.regex||new RegExp(this.regex,"g").test(this.value)},Object.defineProperty(t.prototype,"value",{get:function(){return this.renderedInputControlElement?(this.isMultiline,this.renderedInputControlElement.value):void 0},enumerable:!1,configurable:!0}),t.valueProperty=new m.StringProperty(m.Versions.v1_0,"value"),t.maxLengthProperty=new m.NumProperty(m.Versions.v1_0,"maxLength"),t.isMultilineProperty=new m.BoolProperty(m.Versions.v1_0,"isMultiline",!1),t.placeholderProperty=new m.StringProperty(m.Versions.v1_0,"placeholder"),t.styleProperty=new m.EnumProperty(m.Versions.v1_0,"style",p.InputTextStyle,p.InputTextStyle.Text,[{value:p.InputTextStyle.Text},{value:p.InputTextStyle.Tel},{value:p.InputTextStyle.Url},{value:p.InputTextStyle.Email},{value:p.InputTextStyle.Password,targetVersion:m.Versions.v1_5}]),t.inlineActionProperty=new S(m.Versions.v1_0,"inlineAction",["Action.ShowCard"]),t.regexProperty=new m.StringProperty(m.Versions.v1_3,"regex",!0),o([(0,m.property)(t.valueProperty)],t.prototype,"defaultValue",void 0),o([(0,m.property)(t.maxLengthProperty)],t.prototype,"maxLength",void 0),o([(0,m.property)(t.isMultilineProperty)],t.prototype,"isMultiline",void 0),o([(0,m.property)(t.placeholderProperty)],t.prototype,"placeholder",void 0),o([(0,m.property)(t.styleProperty)],t.prototype,"style",void 0),o([(0,m.property)(t.inlineActionProperty)],t.prototype,"inlineAction",void 0),o([(0,m.property)(t.regexProperty)],t.prototype,"regex",void 0),t}(U);t.TextInput=J;var X=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.valueOn="true",t.valueOff="false",t.wrap=!1,t}return r(t,e),t.prototype.updateInputControlAriaLabelledBy=function(){if(this._checkboxInputElement){var e=this.getAllLabelIds().join(" ");this._checkboxInputLabelElement&&this._checkboxInputLabelElement.id&&(e+=" "+this._checkboxInputLabelElement.id),e?this._checkboxInputElement.setAttribute("aria-labelledby",e):this._checkboxInputElement.removeAttribute("aria-labelledby")}},t.prototype.internalRender=function(){var e=this,t=document.createElement("div");if(t.className=this.hostConfig.makeCssClassName("ac-input","ac-toggleInput"),t.style.width="100%",t.style.display="flex",t.style.alignItems="center",this._checkboxInputElement=document.createElement("input"),this._checkboxInputElement.id=u.generateUniqueId(),this._checkboxInputElement.type="checkbox",this._checkboxInputElement.style.display="inline-block",this._checkboxInputElement.style.verticalAlign="middle",this._checkboxInputElement.style.margin="0",this._checkboxInputElement.style.flex="0 0 auto",this.title&&this._checkboxInputElement.setAttribute("aria-label",this.title),this.isRequired&&this._checkboxInputElement.setAttribute("aria-required","true"),this._checkboxInputElement.tabIndex=this.isDesignMode()?-1:0,this.defaultValue===this.valueOn&&(this._checkboxInputElement.checked=!0),this._oldCheckboxValue=this._checkboxInputElement.checked,this._checkboxInputElement.onchange=function(){e.valueChanged()},u.appendChild(t,this._checkboxInputElement),this.title||this.isDesignMode()){var i=new x;if(i.setParent(this),i.forElementId=this._checkboxInputElement.id,i.hostConfig=this.hostConfig,i.text=this.title?this.title:this.getJsonTypeName(),i.useMarkdown=c.GlobalSettings.useMarkdownInRadioButtonAndCheckbox,i.wrap=this.wrap,this._checkboxInputLabelElement=i.render(),this._checkboxInputLabelElement){this._checkboxInputLabelElement.id=u.generateUniqueId(),this._checkboxInputLabelElement.style.display="inline-block",this._checkboxInputLabelElement.style.flex="1 1 auto",this._checkboxInputLabelElement.style.marginLeft="6px",this._checkboxInputLabelElement.style.verticalAlign="middle";var n=document.createElement("div");n.style.width="6px",u.appendChild(t,n),u.appendChild(t,this._checkboxInputLabelElement)}}return t},Object.defineProperty(t.prototype,"isNullable",{get:function(){return!1},enumerable:!1,configurable:!0}),t.prototype.updateVisualState=function(e){},t.prototype.getJsonTypeName=function(){return"Input.Toggle"},t.prototype.focus=function(){this._checkboxInputElement&&this._checkboxInputElement.focus()},t.prototype.isSet=function(){return this.isRequired?this.value===this.valueOn:!!this.value},t.prototype.isDirty=function(){return!!this._checkboxInputElement&&this._checkboxInputElement.checked!==this._oldCheckboxValue},Object.defineProperty(t.prototype,"value",{get:function(){return this._checkboxInputElement?this._checkboxInputElement.checked?this.valueOn:this.valueOff:void 0},enumerable:!1,configurable:!0}),t.valueProperty=new m.StringProperty(m.Versions.v1_0,"value"),t.titleProperty=new m.StringProperty(m.Versions.v1_0,"title"),t.valueOnProperty=new m.StringProperty(m.Versions.v1_0,"valueOn",!0,void 0,"true",(function(e){return"true"})),t.valueOffProperty=new m.StringProperty(m.Versions.v1_0,"valueOff",!0,void 0,"false",(function(e){return"false"})),t.wrapProperty=new m.BoolProperty(m.Versions.v1_2,"wrap",!1),o([(0,m.property)(t.valueProperty)],t.prototype,"defaultValue",void 0),o([(0,m.property)(t.titleProperty)],t.prototype,"title",void 0),o([(0,m.property)(t.valueOnProperty)],t.prototype,"valueOn",void 0),o([(0,m.property)(t.valueOffProperty)],t.prototype,"valueOff",void 0),o([(0,m.property)(t.wrapProperty)],t.prototype,"wrap",void 0),t}(U);t.ToggleInput=X;var Y=function(e){function t(t,i){var n=e.call(this)||this;return n.title=t,n.value=i,n}return r(t,e),t.prototype.getSchemaKey=function(){return"Choice"},t.titleProperty=new m.StringProperty(m.Versions.v1_0,"title"),t.valueProperty=new m.StringProperty(m.Versions.v1_0,"value"),o([(0,m.property)(t.titleProperty)],t.prototype,"title",void 0),o([(0,m.property)(t.valueProperty)],t.prototype,"value",void 0),t}(m.SerializableObject);t.Choice=Y;var K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getSchemaKey=function(){return"choices.data"},t.typeProperty=new m.StringProperty(m.Versions.v1_6,"type",!0,new RegExp("^Data.Query$")),t.datasetProperty=new m.StringProperty(m.Versions.v1_6,"dataset"),t.countProperty=new m.NumProperty(m.Versions.v1_6,"count"),t.skipProperty=new m.NumProperty(m.Versions.v1_6,"skip"),o([(0,m.property)(t.typeProperty)],t.prototype,"type",void 0),o([(0,m.property)(t.datasetProperty)],t.prototype,"dataset",void 0),o([(0,m.property)(t.countProperty)],t.prototype,"count",void 0),o([(0,m.property)(t.skipProperty)],t.prototype,"skip",void 0),t}(m.SerializableObject);t.ChoiceSetInputDataQuery=K;var Q=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isMultiSelect=!1,t.wrap=!1,t.choices=[],t}return r(t,e),Object.defineProperty(t.prototype,"isCompact",{get:function(){return!this.style||"compact"===this.style},set:function(e){this.style=e?void 0:"expanded"},enumerable:!1,configurable:!0}),t.getUniqueCategoryName=function(){var e="__ac-category"+t._uniqueCategoryCounter;return t._uniqueCategoryCounter++,e},t.prototype.isDynamicTypeahead=function(){return this.hostConfig.inputs.allowDynamicallyFilteredChoiceSet&&!!this.choicesData&&!!this.choicesData.dataset&&"Data.Query"===this.choicesData.type},t.prototype.getFilterForDynamicSearch=function(){var e;return null===(e=this._textInput)||void 0===e?void 0:e.value},t.prototype.getDropdownElement=function(){var e;return null===(e=this._filteredChoiceSet)||void 0===e?void 0:e.dropdown},t.prototype.renderChoices=function(e,t){var i;null===(i=this._filteredChoiceSet)||void 0===i||i.processResponse(e,t)},t.prototype.showLoadingIndicator=function(){var e;null===(e=this._filteredChoiceSet)||void 0===e||e.showLoadingIndicator()},t.prototype.removeLoadingIndicator=function(){var e;null===(e=this._filteredChoiceSet)||void 0===e||e.removeLoadingIndicator()},t.prototype.showErrorIndicator=function(e,t){var i;null===(i=this._filteredChoiceSet)||void 0===i||i.showErrorIndicator(e,t)},t.prototype.showValidationFailureCue=function(){var e;null===(e=this._textInput)||void 0===e||e.classList.add(this.hostConfig.makeCssClassName("ac-input-validation-failed"))},t.prototype.removeValidationFailureCue=function(){var e;null===(e=this._textInput)||void 0===e||e.classList.remove(this.hostConfig.makeCssClassName("ac-input-validation-failed"))},t.prototype.createPlaceholderOptionWhenValueDoesNotExist=function(){if(!this.value){var e=document.createElement("option");return e.selected=!0,e.disabled=!0,e.hidden=!0,e.value="",this.placeholder&&(e.text=this.placeholder),e}},t.prototype.internalApplyAriaCurrent=function(){if(this._selectElement){var e=this._selectElement.options;if(e)for(var t=0,i=Array.from(e);t<i.length;t++){var n=i[t];n.selected?n.setAttribute("aria-current","true"):n.removeAttribute("aria-current")}}},t.prototype.renderCompoundInput=function(e,t,i){var n=this,r=document.createElement("div");r.className=this.hostConfig.makeCssClassName("ac-input",e),r.style.width="100%",r.tabIndex=this.isDesignMode()?-1:0,this._toggleInputs=[],this._labels=[];for(var o=0,s=this.choices;o<s.length;o++){var a=s[o],l=document.createElement("input");l.id=u.generateUniqueId(),l.type=t,l.style.margin="0",l.style.display="inline-block",l.style.verticalAlign="middle",l.style.flex="0 0 auto",l.name=this.id?this.id:this._uniqueCategoryName,this.isRequired&&l.setAttribute("aria-required","true"),l.tabIndex=this.isDesignMode()?-1:0,a.value&&(l.value=a.value),a.title&&l.setAttribute("aria-label",a.title),i&&a.value&&i.indexOf(a.value)>=0&&(l.checked=!0),l.onchange=function(){n.valueChanged()},this._toggleInputs.push(l);var p=document.createElement("div");p.style.display="flex",p.style.alignItems="center",u.appendChild(p,l);var d=new x;d.setParent(this),d.forElementId=l.id,d.hostConfig=this.hostConfig,d.text=a.title?a.title:"Choice "+this._toggleInputs.length,d.useMarkdown=c.GlobalSettings.useMarkdownInRadioButtonAndCheckbox,d.wrap=this.wrap;var h=d.render();if(this._labels.push(h),h){h.id=u.generateUniqueId(),h.style.display="inline-block",h.style.flex="1 1 auto",h.style.marginLeft="6px",h.style.verticalAlign="middle";var f=document.createElement("div");f.style.width="6px",u.appendChild(p,f),u.appendChild(p,h)}u.appendChild(r,p)}return r},t.prototype.updateInputControlAriaLabelledBy=function(){if((this.isMultiSelect||"expanded"===this.style)&&this._toggleInputs&&this._labels)for(var t=this.getAllLabelIds(),i=0;i<this._toggleInputs.length;i++){var n=t.join(" "),r=this._labels[i];r&&r.id&&(n+=" "+r.id),n?this._toggleInputs[i].setAttribute("aria-labelledby",n):this._toggleInputs[i].removeAttribute("aria-labelledby")}else e.prototype.updateInputControlAriaLabelledBy.call(this)},t.prototype.internalRender=function(){var e=this;if(this._uniqueCategoryName=t.getUniqueCategoryName(),this.isDynamicTypeahead()){var i=new Z(t._uniqueCategoryCounter,this.choices,this.hostConfig);if(i.render(),i.textInput){this._textInput=i.textInput,this.defaultValue&&(this._textInput.value=this.defaultValue),this.placeholder&&!this._textInput.value&&(this._textInput.placeholder=this.placeholder,this._textInput.setAttribute("aria-label",this.placeholder)),this._textInput.tabIndex=this.isDesignMode()?-1:0;var n=u.debounce((function(){i.processChoices(),e.valueChanged(),e._textInput&&(e.value?(e._textInput.removeAttribute("placeholder"),e._textInput.removeAttribute("aria-label")):e.placeholder&&(e._textInput.placeholder=e.placeholder,e._textInput.setAttribute("aria-label",e.placeholder)))}),this.hostConfig.inputs.debounceTimeInMilliSeconds);this._textInput.onclick=n,this._textInput.oninput=n}return i.parent=this,this._filteredChoiceSet=i,i.renderedElement}if(this.isMultiSelect)return this.renderCompoundInput("ac-choiceSetInput-multiSelect","checkbox",this.defaultValue?this.defaultValue.split(this.hostConfig.choiceSetInputValueSeparator):void 0);if("expanded"===this.style)return this.renderCompoundInput("ac-choiceSetInput-expanded","radio",this.defaultValue?[this.defaultValue]:void 0);if("filtered"===this.style){var r=document.createElement("div");r.style.width="100%",this._textInput=document.createElement("input"),this._textInput.className=this.hostConfig.makeCssClassName("ac-input","ac-multichoiceInput","ac-choiceSetInput-filtered"),this._textInput.type="text",this._textInput.style.width="100%",this._textInput.oninput=function(){e.valueChanged(),e._textInput&&(e.value?(e._textInput.removeAttribute("placeholder"),e._textInput.removeAttribute("aria-label")):e.placeholder&&(e._textInput.placeholder=e.placeholder,e._textInput.setAttribute("aria-label",e.placeholder)))},this.defaultValue&&(this._textInput.value=this.defaultValue),this.placeholder&&!this._textInput.value&&(this._textInput.placeholder=this.placeholder,this._textInput.setAttribute("aria-label",this.placeholder)),this._textInput.tabIndex=this.isDesignMode()?-1:0;var o=document.createElement("datalist");o.id=u.generateUniqueId();for(var s=0,a=this.choices;s<a.length;s++){var l=a[s],p=document.createElement("option");l.title&&(p.value=l.title,p.setAttribute("aria-label",l.title)),p.tabIndex=this.isDesignMode()?-1:0,o.appendChild(p)}return this._textInput.setAttribute("list",o.id),r.append(this._textInput,o),r}this._selectElement=document.createElement("select"),this._selectElement.className=this.hostConfig.makeCssClassName("ac-input","ac-multichoiceInput","ac-choiceSetInput-compact"),this._selectElement.style.width="100%",this._selectElement.tabIndex=this.isDesignMode()?-1:0;var c=this.createPlaceholderOptionWhenValueDoesNotExist();u.appendChild(this._selectElement,c);for(var d=0,h=this.choices;d<h.length;d++)l=h[d],(p=document.createElement("option")).value=l.value,l.title&&(p.text=l.title,p.setAttribute("aria-label",l.title)),p.tabIndex=this.isDesignMode()?-1:0,l.value===this.defaultValue&&(p.selected=!0),u.appendChild(this._selectElement,p);return this._selectElement.onchange=function(){e.internalApplyAriaCurrent(),e.valueChanged()},this.internalApplyAriaCurrent(),this._selectElement},t.prototype.updateVisualState=function(t){this.allowRevealOnHoverStyle&&!this.isMultiSelect&&this.isCompact&&(e.prototype.updateVisualState.call(this,t),this._selectElement&&this.inputStyle===p.InputStyle.RevealOnHover&&(this.shouldHideInputAdornersForRevealOnHover(this._selectElement,t)?(this._selectElement.style.appearance="none",this._selectElement.classList.remove(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onfocus"))):(this._selectElement.style.appearance="auto",this._selectElement.classList.add(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onfocus")))))},t.prototype.getJsonTypeName=function(){return"Input.ChoiceSet"},t.prototype.focus=function(){this._toggleInputs&&(this.isMultiSelect||"expanded"===this.style)?this._toggleInputs.length>0&&this._toggleInputs[0].focus():this._textInput?this._textInput.focus():e.prototype.focus.call(this)},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),0===this.choices.length&&t.addFailure(this,p.ValidationEvent.CollectionCantBeEmpty,y.Strings.errors.choiceSetMustHaveAtLeastOneChoice());for(var i=0,n=this.choices;i<n.length;i++){var r=n[i];r.title&&r.value||t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.choiceSetChoicesMustHaveTitleAndValue())}},t.prototype.isSet=function(){return!!this.value},t.prototype.isValid=function(){if(this._textInput){if(""===this.value||this.value===this.placeholder)return!0;for(var t=0,i=this.choices;t<i.length;t++){var n=i[t];if(this.value===n.value)return!0}if(this.dynamicChoices)for(var r=0,o=this.dynamicChoices;r<o.length;r++)if(n=o[r],this.value===n)return!0;return!1}return e.prototype.isValid.call(this)},Object.defineProperty(t.prototype,"value",{get:function(){if(this.isMultiSelect){if(!this._toggleInputs||0===this._toggleInputs.length)return;for(var e="",t=0,i=this._toggleInputs;t<i.length;t++)(l=i[t]).checked&&(""!==e&&(e+=this.hostConfig.choiceSetInputValueSeparator),e+=l.value);return e||void 0}if(this._selectElement)return this._selectElement.selectedIndex>0?this._selectElement.value:void 0;if(this._textInput){for(var n=0,r=this.choices;n<r.length;n++){var o=r[n];if(o.title&&this._textInput.value===o.title)return o.value}return this._textInput.value}if(this._toggleInputs&&this._toggleInputs.length>0)for(var s=0,a=this._toggleInputs;s<a.length;s++){var l;if((l=a[s]).checked)return l.value}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dynamicChoices",{get:function(){var e;return null===(e=this._filteredChoiceSet)||void 0===e?void 0:e.dynamicChoices},enumerable:!1,configurable:!0}),t.valueProperty=new m.StringProperty(m.Versions.v1_0,"value"),t.choicesProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_0,"choices",Y),t.choicesDataProperty=new m.SerializableObjectProperty(m.Versions.v1_6,"choices.data",K),t.styleProperty=new m.ValueSetProperty(m.Versions.v1_0,"style",[{value:"compact"},{value:"expanded"},{value:"filtered",targetVersion:m.Versions.v1_5}],"compact"),t.isMultiSelectProperty=new m.BoolProperty(m.Versions.v1_0,"isMultiSelect",!1),t.placeholderProperty=new m.StringProperty(m.Versions.v1_0,"placeholder"),t.wrapProperty=new m.BoolProperty(m.Versions.v1_2,"wrap",!1),t._uniqueCategoryCounter=0,o([(0,m.property)(t.valueProperty)],t.prototype,"defaultValue",void 0),o([(0,m.property)(t.styleProperty)],t.prototype,"style",void 0),o([(0,m.property)(t.isMultiSelectProperty)],t.prototype,"isMultiSelect",void 0),o([(0,m.property)(t.placeholderProperty)],t.prototype,"placeholder",void 0),o([(0,m.property)(t.wrapProperty)],t.prototype,"wrap",void 0),o([(0,m.property)(t.choicesProperty)],t.prototype,"choices",void 0),o([(0,m.property)(t.choicesDataProperty)],t.prototype,"choicesData",void 0),t}(U);t.ChoiceSetInput=Q;var Z=function(){function e(e,t,i){this._choiceSetId=e,this._choices=t,this._dynamicChoices=[],this._visibleChoiceCount=0,this._highlightedChoiceId=-1,this._hostConfig=i}return e.prototype.render=function(){var e=this,t=document.createElement("div");t.className=this.hostConfig.makeCssClassName("ac-input","ac-choiceSetInput-filtered-container"),this._textInput=document.createElement("input"),this._textInput.className=this.hostConfig.makeCssClassName("ac-input","ac-choiceSetInput-filtered-textbox"),this._textInput.type="text",this._textInput.onkeydown=function(t){if("ArrowDown"===t.key)t.preventDefault(),e.highlightChoice(e._highlightedChoiceId+1);else if("ArrowUp"===t.key)t.preventDefault(),e.highlightChoice(e._highlightedChoiceId-1);else if("Enter"===t.key){var i=document.getElementById("ac-choiceSetInput-"+e._choiceSetId+"-choice-"+e._highlightedChoiceId);null==i||i.click()}},this._dropdown=document.createElement("div"),this._dropdown.className=this.hostConfig.makeCssClassName("ac-input","ac-choiceSetInput-filtered-dropdown"),t.append(this._textInput,this._dropdown),this._renderedElement=t},e.prototype.createChoice=function(e,t,i){var n=this,r=document.createElement("span");return r.className=this.hostConfig.makeCssClassName("ac-input","ac-choiceSetInput-choice"),r.id="ac-choiceSetInput-"+this._choiceSetId+"-choice-"+i,r.innerHTML=e.replace(t,"<b>"+t+"</b>"),r.tabIndex=-1,r.onclick=function(){r.classList.remove(n.hostConfig.makeCssClassName("ac-choiceSetInput-choice-highlighted")),n._highlightedChoiceId=-1,n._textInput&&(n._textInput.value=r.innerText,n._textInput.focus()),n._dropdown&&n._dropdown.classList.remove(n.hostConfig.makeCssClassName("ac-choiceSetInput-filtered-dropdown-open"))},r.onmousemove=function(){n._highlightedChoiceId!==i&&n.highlightChoice(i,!1)},r},e.prototype.highlightChoice=function(e,t){if(void 0===t&&(t=!0),this._visibleChoiceCount>0){var i=document.getElementById("ac-choiceSetInput-"+this._choiceSetId+"-choice-"+this._highlightedChoiceId),n=document.getElementById("ac-choiceSetInput-"+this._choiceSetId+"-choice-"+e);n?(null==i||i.classList.remove(this.hostConfig.makeCssClassName("ac-choiceSetInput-choice-highlighted")),n.classList.add(this.hostConfig.makeCssClassName("ac-choiceSetInput-choice-highlighted")),t&&n.scrollIntoView(),this._highlightedChoiceId=e):i&&0!==this._highlightedChoiceId?this.highlightChoice(0):this.highlightChoice(this._visibleChoiceCount-1)}},e.prototype.filterChoices=function(){for(var e,t,i=(null===(e=this._textInput)||void 0===e?void 0:e.value.toLowerCase().trim())||"",n=0,r=l(l([],this._choices,!0),this._dynamicChoices,!0);n<r.length;n++){var o=r[n];if(o.title){var s=o.title.toLowerCase().indexOf(i);if(-1!==s){var a=o.title.substring(s,s+i.length),p=this.createChoice(o.title,a,this._visibleChoiceCount++);null===(t=this._dropdown)||void 0===t||t.appendChild(p)}}}},e.prototype.getStatusIndicator=function(e){if(e){if(!this._errorIndicator){var t=document.createElement("div");t.className=this.hostConfig.makeCssClassName("ac-input","ac-choiceSetInput-statusIndicator","ac-choiceSetInput-errorIndicator"),this._errorIndicator=t}return this._errorIndicator.innerText=e,this._errorIndicator}if(!this._loadingIndicator){var i=document.createElement("div");i.className=this.hostConfig.makeCssClassName("ac-input","ac-choiceSetInput-statusIndicator"),this._loadingIndicator=i}return this._loadingIndicator.innerText=0===this._visibleChoiceCount?"Loading...":"Loading more...",this._loadingIndicator},e.prototype.resetDropdown=function(){this._dropdown&&(u.clearElementChildren(this._dropdown),this._visibleChoiceCount=0,this._highlightedChoiceId=-1)},e.prototype.showDropdown=function(){var e;(null===(e=this._dropdown)||void 0===e?void 0:e.hasChildNodes())&&this._dropdown.classList.add(this.hostConfig.makeCssClassName("ac-choiceSetInput-filtered-dropdown-open"))},e.prototype.processChoices=function(){this.resetDropdown(),this.filterChoices(),this.showDropdown()},e.prototype.processResponse=function(e,t){var i;e===(null===(i=this._textInput)||void 0===i?void 0:i.value)&&(this.resetDropdown(),this._dynamicChoices=t,this.filterChoices(),0===this._visibleChoiceCount&&this.showErrorIndicator(e,"No results found"))},e.prototype.showLoadingIndicator=function(){var e,t=this.getStatusIndicator();null===(e=this._dropdown)||void 0===e||e.appendChild(t),this.showDropdown()},e.prototype.removeLoadingIndicator=function(){var e,t;this._loadingIndicator&&(null===(e=this._dropdown)||void 0===e?void 0:e.contains(this._loadingIndicator))&&(null===(t=this._dropdown)||void 0===t||t.removeChild(this._loadingIndicator))},e.prototype.showErrorIndicator=function(e,t){var i,n;if(e===(null===(i=this._textInput)||void 0===i?void 0:i.value)){this.processChoices();var r=this.getStatusIndicator(t);null===(n=this._dropdown)||void 0===n||n.appendChild(r),r.scrollIntoView()}},Object.defineProperty(e.prototype,"dynamicChoices",{get:function(){var e;return null===(e=this._dynamicChoices)||void 0===e?void 0:e.map((function(e){return e.title}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hostConfig",{get:function(){return this._hostConfig?this._hostConfig:this.parent?this.parent.hostConfig:d.defaultHostConfig},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"parent",{get:function(){return this._parent},set:function(e){this._parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"renderedElement",{get:function(){return this._renderedElement},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"textInput",{get:function(){return this._textInput},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dropdown",{get:function(){return this._dropdown},enumerable:!1,configurable:!0}),e}();t.FilteredChoiceSet=Z;var ee=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.internalRender=function(){var e=this;return this._numberInputElement=document.createElement("input"),this._numberInputElement.setAttribute("type","number"),void 0!==this.min&&this._numberInputElement.setAttribute("min",this.min.toString()),void 0!==this.max&&this._numberInputElement.setAttribute("max",this.max.toString()),this._numberInputElement.className=this.hostConfig.makeCssClassName("ac-input","ac-numberInput"),this._numberInputElement.style.width="100%",this._numberInputElement.tabIndex=this.isDesignMode()?-1:0,void 0!==this.defaultValue&&(this._numberInputElement.valueAsNumber=this.defaultValue),this.placeholder&&(this._numberInputElement.placeholder=this.placeholder,this._numberInputElement.setAttribute("aria-label",this.placeholder)),this._numberInputElement.oninput=function(){e.valueChanged()},this._numberInputElement},t.prototype.getJsonTypeName=function(){return"Input.Number"},t.prototype.isSet=function(){return void 0!==this.value&&!isNaN(this.value)},t.prototype.isValid=function(){if(void 0===this.value)return!this.isRequired;var e=!0;return void 0!==this.min&&(e=e&&this.value>=this.min),void 0!==this.max&&(e=e&&this.value<=this.max),e},Object.defineProperty(t.prototype,"value",{get:function(){return this._numberInputElement?this._numberInputElement.valueAsNumber:void 0},set:function(e){e&&this._numberInputElement&&(this._numberInputElement.value=e.toString())},enumerable:!1,configurable:!0}),t.valueProperty=new m.NumProperty(m.Versions.v1_0,"value"),t.placeholderProperty=new m.StringProperty(m.Versions.v1_0,"placeholder"),t.minProperty=new m.NumProperty(m.Versions.v1_0,"min"),t.maxProperty=new m.NumProperty(m.Versions.v1_0,"max"),o([(0,m.property)(t.valueProperty)],t.prototype,"defaultValue",void 0),o([(0,m.property)(t.minProperty)],t.prototype,"min",void 0),o([(0,m.property)(t.maxProperty)],t.prototype,"max",void 0),o([(0,m.property)(t.placeholderProperty)],t.prototype,"placeholder",void 0),t}(U);t.NumberInput=ee;var te=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.internalRender=function(){var e=this;return this._dateInputElement=document.createElement("input"),this._dateInputElement.setAttribute("type","date"),this.min&&this._dateInputElement.setAttribute("min",this.min),this.max&&this._dateInputElement.setAttribute("max",this.max),this.placeholder&&(this._dateInputElement.placeholder=this.placeholder,this._dateInputElement.setAttribute("aria-label",this.placeholder)),this._dateInputElement.tabIndex=this.isDesignMode()?-1:0,this._dateInputElement.className=this.hostConfig.makeCssClassName("ac-input","ac-dateInput"),this._dateInputElement.style.width="100%",this._dateInputElement.oninput=function(){e.valueChanged()},this.defaultValue&&(this._dateInputElement.value=this.defaultValue),this._dateInputElement},t.prototype.updateVisualState=function(t){if(this.allowRevealOnHoverStyle&&(e.prototype.updateVisualState.call(this,t),this._dateInputElement&&this.inputStyle===p.InputStyle.RevealOnHover)){var i=this.shouldHideInputAdornersForRevealOnHover(this._dateInputElement,t);i?this._dateInputElement.classList.remove(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onfocus")):this._dateInputElement.classList.add(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onfocus")),Oe(this._dateInputElement,i)}},t.prototype.getJsonTypeName=function(){return"Input.Date"},t.prototype.isSet=function(){return!!this.value},t.prototype.isValid=function(){if(!this.value)return!this.isRequired;var e=new Date(this.value),t=!0;if(this.min){var i=new Date(this.min);t=t&&e>=i}if(this.max){var n=new Date(this.max);t=t&&e<=n}return t},Object.defineProperty(t.prototype,"value",{get:function(){return this._dateInputElement?this._dateInputElement.value:void 0},enumerable:!1,configurable:!0}),t.valueProperty=new m.StringProperty(m.Versions.v1_0,"value"),t.placeholderProperty=new m.StringProperty(m.Versions.v1_0,"placeholder"),t.minProperty=new m.StringProperty(m.Versions.v1_0,"min"),t.maxProperty=new m.StringProperty(m.Versions.v1_0,"max"),o([(0,m.property)(t.valueProperty)],t.prototype,"defaultValue",void 0),o([(0,m.property)(t.minProperty)],t.prototype,"min",void 0),o([(0,m.property)(t.maxProperty)],t.prototype,"max",void 0),o([(0,m.property)(t.placeholderProperty)],t.prototype,"placeholder",void 0),t}(U);t.DateInput=te;var ie=function(e){function t(t,i){var n=e.call(this,t,i,(function(e,t,i,n){var r=i[t.name];if("string"==typeof r&&r&&/^[0-9]{2}:[0-9]{2}$/.test(r))return r}),(function(e,t,i,n,r){r.serializeValue(i,t.name,n)}))||this;return n.targetVersion=t,n.name=i,n}return r(t,e),t}(m.CustomProperty);t.TimeProperty=ie;var ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.convertTimeStringToDate=function(e){return new Date("1973-09-04T"+e+":00Z")},t.prototype.internalRender=function(){var e=this;return this._timeInputElement=document.createElement("input"),this._timeInputElement.setAttribute("type","time"),this.min&&this._timeInputElement.setAttribute("min",this.min),this.max&&this._timeInputElement.setAttribute("max",this.max),this._timeInputElement.className=this.hostConfig.makeCssClassName("ac-input","ac-timeInput"),this._timeInputElement.style.width="100%",this._timeInputElement.oninput=function(){e.valueChanged()},this.placeholder&&(this._timeInputElement.placeholder=this.placeholder,this._timeInputElement.setAttribute("aria-label",this.placeholder)),this._timeInputElement.tabIndex=this.isDesignMode()?-1:0,this.defaultValue&&(this._timeInputElement.value=this.defaultValue),this._timeInputElement},t.prototype.updateVisualState=function(t){if(this.allowRevealOnHoverStyle&&(e.prototype.updateVisualState.call(this,t),this._timeInputElement&&this.inputStyle===p.InputStyle.RevealOnHover)){var i=this.shouldHideInputAdornersForRevealOnHover(this._timeInputElement,t);i?this._timeInputElement.classList.remove(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onfocus")):this._timeInputElement.classList.add(this.hostConfig.makeCssClassName("ac-inputStyle-revealOnHover-onfocus")),Oe(this._timeInputElement,i)}},t.prototype.getJsonTypeName=function(){return"Input.Time"},t.prototype.isSet=function(){return!!this.value},t.prototype.isValid=function(){if(!this.value)return!this.isRequired;var e=t.convertTimeStringToDate(this.value),i=!0;if(this.min){var n=t.convertTimeStringToDate(this.min);i=i&&e>=n}if(this.max){var r=t.convertTimeStringToDate(this.max);i=i&&e<=r}return i},Object.defineProperty(t.prototype,"value",{get:function(){return this._timeInputElement?this._timeInputElement.value:void 0},enumerable:!1,configurable:!0}),t.valueProperty=new ie(m.Versions.v1_0,"value"),t.placeholderProperty=new m.StringProperty(m.Versions.v1_0,"placeholder"),t.minProperty=new ie(m.Versions.v1_0,"min"),t.maxProperty=new ie(m.Versions.v1_0,"max"),o([(0,m.property)(t.valueProperty)],t.prototype,"defaultValue",void 0),o([(0,m.property)(t.minProperty)],t.prototype,"min",void 0),o([(0,m.property)(t.maxProperty)],t.prototype,"max",void 0),o([(0,m.property)(t.placeholderProperty)],t.prototype,"placeholder",void 0),t}(U);t.TimeInput=ne;var re=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.style=p.ActionStyle.Default,t.mode=p.ActionMode.Primary,t._state=0,t._isFocusable=!0,t}return r(t,e),t.prototype.renderButtonContent=function(){if(this.renderedElement){var e=this.hostConfig,t=document.createElement("div");if(t.style.overflow="hidden",t.style.textOverflow="ellipsis",e.actions.iconPlacement===p.ActionIconPlacement.AboveTitle||e.actions.allowTitleToWrap||(t.style.whiteSpace="nowrap"),this.title&&(t.innerText=this.title),this.iconUrl){var i=document.createElement("img");i.src=this.iconUrl,i.style.width=e.actions.iconSize+"px",i.style.height=e.actions.iconSize+"px",i.style.flex="0 0 auto",e.actions.iconPlacement===p.ActionIconPlacement.AboveTitle?(this.renderedElement.classList.add("iconAbove"),this.renderedElement.style.flexDirection="column",this.title&&(i.style.marginBottom="6px")):(this.renderedElement.classList.add("iconLeft"),i.style.maxHeight="100%",this.title&&(i.style.marginRight="6px")),this.renderedElement.appendChild(i),this.renderedElement.appendChild(t)}else this.renderedElement.classList.add("noIcon"),this.renderedElement.appendChild(t)}},t.prototype.getParentContainer=function(){return this.parent instanceof Ee?this.parent:this.parent?this.parent.getParentContainer():void 0},t.prototype.isDesignMode=function(){var e=this.getRootObject();return e instanceof w&&e.isDesignMode()},t.prototype.updateCssClasses=function(){var e,t;if(this.parent&&this.renderedElement){var i=this.parent.hostConfig;this.renderedElement.className=i.makeCssClassName(this.isEffectivelyEnabled()?"ac-pushButton":"ac-pushButton-disabled");var n=this.getParentContainer();if(n){var r=n.getEffectiveStyle();r&&this.renderedElement.classList.add("style-"+r)}switch(this.renderedElement.tabIndex=!this.isDesignMode()&&this.isFocusable?0:-1,this._state){case 0:break;case 1:this.renderedElement.classList.add(i.makeCssClassName("expanded"));break;case 2:this.renderedElement.classList.add(i.makeCssClassName("subdued"))}this.style&&this.isEffectivelyEnabled()&&(this.style===p.ActionStyle.Positive?(e=this.renderedElement.classList).add.apply(e,i.makeCssClassNames("primary","style-positive")):(t=this.renderedElement.classList).add.apply(t,i.makeCssClassNames("style-"+this.style.toLowerCase())))}},t.prototype.getDefaultSerializationContext=function(){return new $e},t.prototype.internalGetReferencedInputs=function(){return{}},t.prototype.internalPrepareForExecution=function(e){},t.prototype.internalValidateInputs=function(e){var t=[];if(e)for(var i=0,n=Object.keys(e);i<n.length;i++){var r=e[n[i]];r.validateValue()||t.push(r)}return t},t.prototype.shouldSerialize=function(e){return void 0!==e.actionRegistry.findByName(this.getJsonTypeName())},t.prototype.raiseExecuteActionEvent=function(){var e,t,i;this.onExecute&&this.onExecute(this),i=(t=(e=this).parent?e.parent.getRootElement():void 0)&&t.onExecuteAction?t.onExecuteAction:Ne.onExecuteAction,e.prepareForExecution()&&i&&i(e)},t.prototype.internalAfterExecute=function(){var e=this.getRootObject();e instanceof w&&e.updateActionsEnabledState()},t.prototype.getHref=function(){return""},t.prototype.getAriaRole=function(){var e=this.getAriaRoleFromEnum();return null!=e?e:"button"},t.prototype.getAriaRoleFromEnum=function(){switch(this.role){case p.ActionRole.Button:return"button";case p.ActionRole.Link:return"link";case p.ActionRole.Menu:return"menu";case p.ActionRole.MenuItem:return"menuitem";case p.ActionRole.Tab:return"tab";default:return}},t.prototype.setupElementForAccessibility=function(e,t){void 0===t&&(t=!1),e.tabIndex=this.isEffectivelyEnabled()&&!this.isDesignMode()?0:-1,e.setAttribute("role",this.getAriaRole()),e instanceof HTMLButtonElement&&(e.disabled=!this.isEffectivelyEnabled()),this.isEffectivelyEnabled()?(e.removeAttribute("aria-disabled"),e.classList.add(this.hostConfig.makeCssClassName("ac-selectable"))):e.setAttribute("aria-disabled","true"),this.title?(e.setAttribute("aria-label",this.title),e.title=this.title):(e.removeAttribute("aria-label"),e.removeAttribute("title")),this.tooltip&&(t&&!this.title&&e.setAttribute("aria-label",this.tooltip),e.title=this.tooltip)},t.prototype.parse=function(t,i){return e.prototype.parse.call(this,t,i||new $e)},t.prototype.render=function(){var e=this,t=document.createElement("button");t.type="button",t.style.display="flex",t.style.alignItems="center",t.style.justifyContent="center",t.onclick=function(t){e.isEffectivelyEnabled()&&(t.preventDefault(),t.cancelBubble=!0,e.execute())},this._renderedElement=t,this.renderButtonContent(),this.updateCssClasses(),this.setupElementForAccessibility(t)},t.prototype.execute=function(){this._actionCollection&&this._actionCollection.actionExecuted(this),this.raiseExecuteActionEvent(),this.internalAfterExecute()},t.prototype.prepareForExecution=function(){var e=this.getReferencedInputs(),t=this.internalValidateInputs(e);return t.length>0?(t[0].focus(),!1):(this.internalPrepareForExecution(e),!0)},t.prototype.remove=function(){return!!this._actionCollection&&this._actionCollection.removeAction(this)},t.prototype.getAllInputs=function(e){return void 0===e&&(e=!0),[]},t.prototype.getAllActions=function(){return[this]},t.prototype.getResourceInformation=function(){return this.iconUrl?[{url:this.iconUrl,mimeType:"image"}]:[]},t.prototype.getActionById=function(e){return this.id===e?this:void 0},t.prototype.getReferencedInputs=function(){return this.internalGetReferencedInputs()},t.prototype.validateInputs=function(){return this.internalValidateInputs(this.getReferencedInputs())},t.prototype.updateEnabledState=function(){},t.prototype.isEffectivelyEnabled=function(){return this.isEnabled},Object.defineProperty(t.prototype,"isPrimary",{get:function(){return this.style===p.ActionStyle.Positive},set:function(e){e?this.style=p.ActionStyle.Positive:this.style===p.ActionStyle.Positive&&(this.style=p.ActionStyle.Default)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hostConfig",{get:function(){return this.parent?this.parent.hostConfig:d.defaultHostConfig},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},set:function(e){this._state!==e&&(this._state=e,this.updateCssClasses())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isFocusable",{get:function(){return this._isFocusable},set:function(e){this._isFocusable!==e&&(this._isFocusable=e,this.updateCssClasses())},enumerable:!1,configurable:!0}),t.titleProperty=new m.StringProperty(m.Versions.v1_0,"title"),t.iconUrlProperty=new m.StringProperty(m.Versions.v1_1,"iconUrl"),t.styleProperty=new m.ValueSetProperty(m.Versions.v1_2,"style",[{value:p.ActionStyle.Default},{value:p.ActionStyle.Positive},{value:p.ActionStyle.Destructive}],p.ActionStyle.Default),t.modeProperty=new m.ValueSetProperty(m.Versions.v1_5,"mode",[{value:p.ActionMode.Primary},{value:p.ActionMode.Secondary}],p.ActionMode.Primary),t.tooltipProperty=new m.StringProperty(m.Versions.v1_5,"tooltip"),t.isEnabledProperty=new m.BoolProperty(m.Versions.v1_5,"isEnabled",!0),t.roleProperty=new m.EnumProperty(m.Versions.v1_6,"role",p.ActionRole),o([(0,m.property)(t.titleProperty)],t.prototype,"title",void 0),o([(0,m.property)(t.iconUrlProperty)],t.prototype,"iconUrl",void 0),o([(0,m.property)(t.styleProperty)],t.prototype,"style",void 0),o([(0,m.property)(t.modeProperty)],t.prototype,"mode",void 0),o([(0,m.property)(t.tooltipProperty)],t.prototype,"tooltip",void 0),o([(0,m.property)(t.isEnabledProperty)],t.prototype,"isEnabled",void 0),o([(0,m.property)(t.roleProperty)],t.prototype,"role",void 0),t}(f.CardObject);t.Action=re;var oe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.disabledUnlessAssociatedInputsChange=!1,t._isPrepared=!1,t._areReferencedInputsDirty=!1,t}return r(t,e),t.prototype.internalGetReferencedInputs=function(){var e={};if("none"!==this.associatedInputs){for(var t=this.parent,i=[];t;)i.push.apply(i,t.getAllInputs(!1)),t=t.parent;for(var n=0,r=i;n<r.length;n++){var o=r[n];o.id&&(e[o.id]=o)}}return e},t.prototype.internalPrepareForExecution=function(e){if(this._originalData?this._processedData=JSON.parse(JSON.stringify(this._originalData)):this._processedData={},this._processedData&&e)for(var t=0,i=Object.keys(e);t<i.length;t++){var n=e[i[t]];n.id&&n.isSet()&&(this._processedData[n.id]="string"==typeof n.value?n.value:n.value.toString())}this._isPrepared=!0},t.prototype.internalAfterExecute=function(){c.GlobalSettings.resetInputsDirtyStateAfterActionExecution&&this.resetReferencedInputsDirtyState()},t.prototype.resetReferencedInputsDirtyState=function(){var e=this.getReferencedInputs();if(this._areReferencedInputsDirty=!1,e)for(var t=0,i=Object.keys(e);t<i.length;t++)e[i[t]].resetDirtyState()},t.prototype.updateEnabledState=function(){this._areReferencedInputsDirty=!1;var e=this.getReferencedInputs();if(e)for(var t=0,i=Object.keys(e);t<i.length;t++)if(e[i[t]].isDirty()){this._areReferencedInputsDirty=!0;break}this.updateCssClasses(),this._renderedElement&&this.setupElementForAccessibility(this._renderedElement)},t.prototype.isEffectivelyEnabled=function(){var t=e.prototype.isEffectivelyEnabled.call(this);return this.disabledUnlessAssociatedInputsChange?t&&this._areReferencedInputsDirty:t},Object.defineProperty(t.prototype,"data",{get:function(){return this._isPrepared?this._processedData:this._originalData},set:function(e){this._originalData=e,this._isPrepared=!1},enumerable:!1,configurable:!0}),t.dataProperty=new m.PropertyDefinition(m.Versions.v1_0,"data"),t.associatedInputsProperty=new m.CustomProperty(m.Versions.v1_3,"associatedInputs",(function(e,t,i,n){var r=i[t.name];if(void 0!==r&&"string"==typeof r)return"none"===r.toLowerCase()?"none":"auto"}),(function(e,t,i,n,r){r.serializeValue(i,t.name,n)})),t.disabledUnlessAssociatedInputsChangeProperty=new m.BoolProperty(m.Versions.v1_6,"disabledUnlessAssociatedInputsChange",!1),o([(0,m.property)(t.dataProperty)],t.prototype,"_originalData",void 0),o([(0,m.property)(t.associatedInputsProperty)],t.prototype,"associatedInputs",void 0),o([(0,m.property)(t.disabledUnlessAssociatedInputsChangeProperty)],t.prototype,"disabledUnlessAssociatedInputsChange",void 0),t}(re);t.SubmitActionBase=oe;var se=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getJsonTypeName=function(){return t.JsonTypeName},t.JsonTypeName="Action.Submit",t}(oe);t.SubmitAction=se;var ae=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(oe);t.UniversalAction=ae;var le=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getJsonTypeName=function(){return t.JsonTypeName},t.JsonTypeName="Action.Execute",t.verbProperty=new m.StringProperty(m.Versions.v1_4,"verb"),o([(0,m.property)(t.verbProperty)],t.prototype,"verb",void 0),t}(ae);t.ExecuteAction=le;var pe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getJsonTypeName=function(){return t.JsonTypeName},Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),t.JsonTypeName="Data.Query",t.datasetProperty=new m.StringProperty(m.Versions.v1_6,"dataset"),t.filterProperty=new m.StringProperty(m.Versions.v1_6,"filter"),t.countProperty=new m.NumProperty(m.Versions.v1_6,"count"),t.skipProperty=new m.NumProperty(m.Versions.v1_6,"skip"),o([(0,m.property)(t.datasetProperty)],t.prototype,"dataset",void 0),o([(0,m.property)(t.filterProperty)],t.prototype,"filter",void 0),o([(0,m.property)(t.countProperty)],t.prototype,"count",void 0),o([(0,m.property)(t.skipProperty)],t.prototype,"skip",void 0),t}(ae);t.DataQuery=pe;var ce=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getJsonTypeName=function(){return t.JsonTypeName},t.prototype.getAriaRole=function(){var e=this.getAriaRoleFromEnum();return null!=e?e:"link"},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),this.url||t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.propertyMustBeSet("url"))},t.prototype.getHref=function(){return this.url},t.urlProperty=new m.StringProperty(m.Versions.v1_0,"url"),t.JsonTypeName="Action.OpenUrl",o([(0,m.property)(t.urlProperty)],t.prototype,"url",void 0),t}(re);t.OpenUrlAction=ce;var ue=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.targetElements={},t}return r(t,e),t.prototype.updateAriaControlsAttribute=function(){if(this.targetElements){var e=Object.keys(this.targetElements);this._renderedElement&&(e.length>0?this._renderedElement.setAttribute("aria-controls",e.join(" ")):this._renderedElement.removeAttribute("aria-controls"))}},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),this.targetElements||t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.propertyMustBeSet("targetElements"))},t.prototype.getJsonTypeName=function(){return t.JsonTypeName},t.prototype.render=function(){e.prototype.render.call(this),this.updateAriaControlsAttribute()},t.prototype.execute=function(){if(e.prototype.execute.call(this),this.parent)for(var t=0,i=Object.keys(this.targetElements);t<i.length;t++){var n=i[t],r=this.parent.getRootElement().getElementById(n);r&&("boolean"==typeof this.targetElements[n]?r.isVisible=this.targetElements[n]:r.isVisible=!r.isVisible)}},t.prototype.addTargetElement=function(e,t){void 0===t&&(t=void 0),this.targetElements[e]=t,this.updateAriaControlsAttribute()},t.prototype.removeTargetElement=function(e){delete this.targetElements[e],this.updateAriaControlsAttribute()},t.targetElementsProperty=new m.CustomProperty(m.Versions.v1_2,"targetElements",(function(e,t,i,n){var r={};if(Array.isArray(i[t.name]))for(var o=0,s=i[t.name];o<s.length;o++){var a=s[o];if("string"==typeof a)r[a]=void 0;else if("object"==typeof a){var l=a.elementId;"string"==typeof l&&(r[l]=u.parseBool(a.isVisible))}}return r}),(function(e,t,i,n,r){for(var o=[],s=0,a=Object.keys(n);s<a.length;s++){var l=a[s];"boolean"==typeof n[l]?o.push({elementId:l,isVisible:n[l]}):o.push(l)}r.serializeArray(i,t.name,o)}),{},(function(e){return{}})),t.JsonTypeName="Action.ToggleVisibility",o([(0,m.property)(t.targetElementsProperty)],t.prototype,"targetElements",void 0),t}(re);t.ToggleVisibilityAction=ue;var de=function(e){function t(t,i){var n=e.call(this,t,i,void 0,(function(){return new c.StringWithSubstitutions}))||this;return n.targetVersion=t,n.name=i,n}return r(t,e),t.prototype.parse=function(e,t,i){var n=new c.StringWithSubstitutions;return n.set(u.parseString(t[this.name])),n},t.prototype.toJSON=function(e,t,i,n){n.serializeValue(t,this.name,i.getOriginal())},t}(m.PropertyDefinition),he=function(e){function t(t,i){void 0===t&&(t=""),void 0===i&&(i="");var n=e.call(this)||this;return n.name=t,n.value=i,n}return r(t,e),t.prototype.getSchemaKey=function(){return"HttpHeader"},t.prototype.getReferencedInputs=function(e,t){this._value.getReferencedInputs(e,t)},t.prototype.prepareForExecution=function(e){this._value.substituteInputValues(e,c.ContentTypes.applicationXWwwFormUrlencoded)},Object.defineProperty(t.prototype,"value",{get:function(){return this._value.get()},set:function(e){this._value.set(e)},enumerable:!1,configurable:!0}),t.nameProperty=new m.StringProperty(m.Versions.v1_0,"name"),t.valueProperty=new de(m.Versions.v1_0,"value"),o([(0,m.property)(t.nameProperty)],t.prototype,"name",void 0),o([(0,m.property)(t.valueProperty)],t.prototype,"_value",void 0),t}(m.SerializableObject);t.HttpHeader=he;var fe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._ignoreInputValidation=!1,t}return r(t,e),t.prototype.internalGetReferencedInputs=function(){var e=this.parent?this.parent.getRootElement().getAllInputs():[],t={};this._url.getReferencedInputs(e,t);for(var i=0,n=this.headers;i<n.length;i++)n[i].getReferencedInputs(e,t);return this._body.getReferencedInputs(e,t),t},t.prototype.internalPrepareForExecution=function(e){if(e){this._url.substituteInputValues(e,c.ContentTypes.applicationXWwwFormUrlencoded);for(var t=c.ContentTypes.applicationJson,i=0,n=this.headers;i<n.length;i++){var r=n[i];r.prepareForExecution(e),r.name&&"content-type"===r.name.toLowerCase()&&(t=r.value)}this._body.substituteInputValues(e,t)}},t.prototype.getJsonTypeName=function(){return t.JsonTypeName},t.prototype.internalValidateProperties=function(t){if(e.prototype.internalValidateProperties.call(this,t),this.url||t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.propertyMustBeSet("url")),this.headers.length>0)for(var i=0,n=this.headers;i<n.length;i++)n[i].name||t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.actionHttpHeadersMustHaveNameAndValue())},Object.defineProperty(t.prototype,"ignoreInputValidation",{get:function(){return this._ignoreInputValidation},set:function(e){this._ignoreInputValidation=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"url",{get:function(){return this._url.get()},set:function(e){this._url.set(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"body",{get:function(){return this._body.get()},set:function(e){this._body.set(e)},enumerable:!1,configurable:!0}),t.urlProperty=new de(m.Versions.v1_0,"url"),t.bodyProperty=new de(m.Versions.v1_0,"body"),t.methodProperty=new m.StringProperty(m.Versions.v1_0,"method"),t.headersProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_0,"headers",he),t.ignoreInputValidationProperty=new m.BoolProperty(m.Versions.v1_3,"ignoreInputValidation",!1),t.JsonTypeName="Action.Http",o([(0,m.property)(t.urlProperty)],t.prototype,"_url",void 0),o([(0,m.property)(t.bodyProperty)],t.prototype,"_body",void 0),o([(0,m.property)(t.methodProperty)],t.prototype,"method",void 0),o([(0,m.property)(t.headersProperty)],t.prototype,"headers",void 0),o([(0,m.property)(t.ignoreInputValidationProperty)],t.prototype,"_ignoreInputValidation",void 0),t}(re);t.HttpAction=fe;var me=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.card=new Re,t}return r(t,e),t.prototype.updateCssClasses=function(){if(e.prototype.updateCssClasses.call(this),this.renderedElement){var t=this.parent?this.parent.hostConfig:d.defaultHostConfig;this.renderedElement.classList.add(t.makeCssClassName("expandable")),this.renderedElement.setAttribute("aria-expanded",(1===this.state).toString())}},t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i);var n=t.card;n?this.card.parse(n,i):i.logParseEvent(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.showCardMustHaveCard())},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i),this.card&&i.serializeValue(t,"card",this.card.toJSON(i))},t.prototype.raiseExecuteActionEvent=function(){this.hostConfig.actions.showCard.actionMode===p.ShowCardActionMode.Popup&&e.prototype.raiseExecuteActionEvent.call(this)},t.prototype.releaseDOMResources=function(){e.prototype.releaseDOMResources.call(this),this.card.releaseDOMResources()},t.prototype.getJsonTypeName=function(){return t.JsonTypeName},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),this.card.internalValidateProperties(t)},t.prototype.setParent=function(t){e.prototype.setParent.call(this,t),this.card.setParent(t)},t.prototype.getAllInputs=function(e){return void 0===e&&(e=!0),this.card.getAllInputs(e)},t.prototype.getAllActions=function(){var t=e.prototype.getAllActions.call(this);return t.push.apply(t,this.card.getAllActions()),t},t.prototype.getResourceInformation=function(){var t=e.prototype.getResourceInformation.call(this);return t.push.apply(t,this.card.getResourceInformation()),t},t.prototype.getActionById=function(t){var i=e.prototype.getActionById.call(this,t);return i||(i=this.card.getActionById(t)),i},t.JsonTypeName="Action.ShowCard",t}(re);t.ShowCardAction=me;var ge=function(e){function t(t){var i=e.call(this)||this;return i._actions=t,i.title=y.Strings.defaults.overflowButtonText(),i.tooltip=y.Strings.defaults.overflowButtonTooltip(),i}return r(t,e),t.prototype.getActions=function(){return this._actions},t.prototype.getAllActions=function(){var t=e.prototype.getAllActions.call(this);return t.push.apply(t,this._actions),t},t.prototype.getJsonTypeName=function(){return me.JsonTypeName},t.prototype.execute=function(){var e,t,i,n,r,o=this;if(t=this,i=this.renderedElement,(void 0===(r=(n=t.parent?t.parent.getRootElement():void 0)&&n.onDisplayOverflowActionMenu?n.onDisplayOverflowActionMenu:Ne.onDisplayOverflowActionMenu)||!r(t.getActions(),i))&&this.renderedElement){var s=new v.PopupMenu;s.hostConfig=this.hostConfig;for(var a=function(t){var i=new v.MenuItem(t.toString(),null!==(e=l._actions[t].title)&&void 0!==e?e:"");i.isEnabled=l._actions[t].isEnabled,i.onClick=function(){var e=o._actions[t];s.closePopup(!1),e.isEnabled&&e.execute()},s.items.add(i)},l=this,p=0;p<this._actions.length;p++)a(p);s.onClose=function(){var e,t;null===(e=o.renderedElement)||void 0===e||e.focus(),null===(t=o.renderedElement)||void 0===t||t.setAttribute("aria-expanded","false")},this.renderedElement.setAttribute("aria-expanded","true"),s.popup(this.renderedElement),s.selectedIndex=0}},t.prototype.setupElementForAccessibility=function(t,i){void 0===i&&(i=!1),e.prototype.setupElementForAccessibility.call(this,t,i),t.setAttribute("aria-label",y.Strings.defaults.overflowButtonTooltip()),t.setAttribute("aria-expanded","false")},t.JsonTypeName="Action.Overflow",t}(re),ye=function(){function e(e){this._items=[],this._renderedActions=[],this._owner=e}return e.prototype.isActionAllowed=function(e){var t=this._owner.getForbiddenActionTypes();if(t)for(var i=0,n=t;i<n.length;i++){var r=n[i];if(e.constructor===r)return!1}return!0},e.prototype.refreshContainer=function(){if(b(this._actionCardContainer),this._actionCard){this._actionCardContainer.style.marginTop=this.renderedActionCount>0?this._owner.hostConfig.actions.showCard.inlineTopMargin+"px":"0px";var e=this._owner.getEffectivePadding();this._owner.getImmediateSurroundingPadding(e);var t=this._owner.hostConfig.paddingDefinitionToSpacingDefinition(e);this._actionCard&&(this._actionCard.style.paddingLeft=t.left+"px",this._actionCard.style.paddingRight=t.right+"px",this._actionCard.style.marginLeft="-"+t.left+"px",this._actionCard.style.marginRight="-"+t.right+"px",0===t.bottom||this._owner.isDesignMode()||(this._actionCard.style.paddingBottom=t.bottom+"px",this._actionCard.style.marginBottom="-"+t.bottom+"px"),u.appendChild(this._actionCardContainer,this._actionCard))}else this._actionCardContainer.style.marginTop="0px"},e.prototype.layoutChanged=function(){this._owner.getRootElement().updateLayout()},e.prototype.showActionCard=function(e,t,i){void 0===t&&(t=!1),void 0===i&&(i=!0),e.card.suppressStyle=t;var n=e.card.renderedElement&&!this._owner.isDesignMode()?e.card.renderedElement:e.card.render();this._actionCard=n,this._expandedAction=e,this.refreshContainer(),i&&(this.layoutChanged(),Te(e,!0))},e.prototype.collapseExpandedAction=function(){for(var e=0,t=this._renderedActions;e<t.length;e++)t[e].state=0;var i=this._expandedAction;this._expandedAction=void 0,this._actionCard=void 0,this.refreshContainer(),i&&(this.layoutChanged(),Te(i,!1))},e.prototype.expandShowCardAction=function(e,t){for(var i=this,n=!1,r=0,o=this._renderedActions;r<o.length;r++){var s=o[r];this._owner.hostConfig.actions.actionsOrientation==p.Orientation.Horizontal&&n&&(s.isFocusable=!1),s!==e?s.state=2:(s.state=1,n=!0,s.renderedElement&&(s.renderedElement.onblur=function(e){for(var t=0,n=i._renderedActions;t<n.length;t++)n[t].isFocusable=!0}))}this.showActionCard(e,!(this._owner.isAtTheVeryLeft()&&this._owner.isAtTheVeryRight()),t)},e.prototype.releaseDOMResources=function(){for(var e=0,t=this._renderedActions;e<t.length;e++)t[e].releaseDOMResources()},e.prototype.actionExecuted=function(e){e instanceof me?e===this._expandedAction?this.collapseExpandedAction():this._owner.hostConfig.actions.showCard.actionMode===p.ShowCardActionMode.Inline&&this.expandShowCardAction(e,!0):this.collapseExpandedAction()},e.prototype.parse=function(e,t){if(this.clear(),Array.isArray(e))for(var i=0,n=e;i<n.length;i++){var r=n[i],o=[];this._owner instanceof Me&&(o=this._owner.getForbiddenActionNames());var s=t.parseAction(this._owner,r,o,!this._owner.isDesignMode());s&&this.addAction(s)}},e.prototype.toJSON=function(e,t,i){i.serializeArray(e,t,this._items)},e.prototype.getActionAt=function(e){return this._items[e]},e.prototype.getActionCount=function(){return this._items.length},e.prototype.getActionById=function(e){for(var t=void 0,i=0,n=this._items;i<n.length&&!(t=n[i].getActionById(e));i++);return t},e.prototype.validateProperties=function(e){this._owner.hostConfig.actions.maxActions&&this._items.length>this._owner.hostConfig.actions.maxActions&&e.addFailure(this._owner,p.ValidationEvent.TooManyActions,y.Strings.errors.tooManyActions(this._owner.hostConfig.actions.maxActions)),this._items.length>0&&!this._owner.hostConfig.supportsInteractivity&&e.addFailure(this._owner,p.ValidationEvent.InteractivityNotAllowed,y.Strings.errors.interactivityNotAllowed());for(var t=0,i=this._items;t<i.length;t++){var n=i[t];this.isActionAllowed(n)||e.addFailure(this._owner,p.ValidationEvent.ActionTypeNotAllowed,y.Strings.errors.actionTypeNotAllowed(n.getJsonTypeName())),n.internalValidateProperties(e)}},e.prototype.render=function(e){var t=this._owner.hostConfig;if(t.supportsInteractivity){var i=document.createElement("div"),n=t.actions.maxActions?Math.min(t.actions.maxActions,this._items.length):this._items.length;if(this._actionCardContainer=document.createElement("div"),this._renderedActions=[],t.actions.preExpandSingleShowCardAction&&1===n&&this._items[0]instanceof me&&this.isActionAllowed(this._items[0]))this.showActionCard(this._items[0],!0),this._renderedActions.push(this._items[0]);else{var r=document.createElement("div");if(r.className=t.makeCssClassName("ac-actionSet"),r.style.display="flex",e===p.Orientation.Horizontal)if(r.style.flexDirection="row",this._owner.horizontalAlignment&&t.actions.actionAlignment!==p.ActionAlignment.Stretch)switch(this._owner.horizontalAlignment){case p.HorizontalAlignment.Center:r.style.justifyContent="center";break;case p.HorizontalAlignment.Right:r.style.justifyContent="flex-end";break;default:r.style.justifyContent="flex-start"}else switch(t.actions.actionAlignment){case p.ActionAlignment.Center:r.style.justifyContent="center";break;case p.ActionAlignment.Right:r.style.justifyContent="flex-end";break;default:r.style.justifyContent="flex-start"}else if(r.style.flexDirection="column",this._owner.horizontalAlignment&&t.actions.actionAlignment!==p.ActionAlignment.Stretch)switch(this._owner.horizontalAlignment){case p.HorizontalAlignment.Center:r.style.alignItems="center";break;case p.HorizontalAlignment.Right:r.style.alignItems="flex-end";break;default:r.style.alignItems="flex-start"}else switch(t.actions.actionAlignment){case p.ActionAlignment.Center:r.style.alignItems="center";break;case p.ActionAlignment.Right:r.style.alignItems="flex-end";break;case p.ActionAlignment.Stretch:r.style.alignItems="stretch";break;default:r.style.alignItems="flex-start"}var o=this._items.filter(this.isActionAllowed.bind(this)),s=[],a=[];if(this._owner.isDesignMode())s=o;else{o.forEach((function(e){return e.mode===p.ActionMode.Secondary?a.push(e):s.push(e)}));var l=s.splice(t.actions.maxActions);c.GlobalSettings.allowMoreThanMaxActionsInOverflowMenu&&a.push.apply(a,l);var d=!0;if(a.length>0){this._overflowAction||(this._overflowAction=new ge(a),this._overflowAction.setParent(this._owner),this._overflowAction._actionCollection=this);var h=this._owner instanceof Ne&&!this._owner.parent;d=!function(e,t){var i=e.parent?e.parent.getRootElement():void 0,n=i&&i.onRenderOverflowActions?i.onRenderOverflowActions:Ne.onRenderOverflowActions;return void 0!==n&&n(e.getActions(),t)}(this._overflowAction,h)}this._overflowAction&&d&&s.push(this._overflowAction)}for(var f=0;f<s.length;f++){var m=s[f];if(m.render(),m.renderedElement&&(t.actions.actionsOrientation===p.Orientation.Horizontal&&t.actions.actionAlignment===p.ActionAlignment.Stretch?m.renderedElement.style.flex="0 1 100%":m.renderedElement.style.flex="0 1 auto",r.appendChild(m.renderedElement),this._renderedActions.push(m),f<s.length-1&&t.actions.buttonSpacing>0)){var g=document.createElement("div");e===p.Orientation.Horizontal?(g.style.flex="0 0 auto",g.style.width=t.actions.buttonSpacing+"px"):g.style.height=t.actions.buttonSpacing+"px",u.appendChild(r,g)}}var y=document.createElement("div");y.style.overflow="hidden",y.appendChild(r),u.appendChild(i,y)}u.appendChild(i,this._actionCardContainer);for(var v=0,b=this._renderedActions;v<b.length;v++){var C=b[v];if(1===C.state){this.expandShowCardAction(C,!1);break}}return this._renderedActions.length>0?i:void 0}},e.prototype.addAction=function(e){if(!e)throw new Error("The action parameter cannot be null.");if(e.parent&&e.parent!==this._owner||!(this._items.indexOf(e)<0))throw new Error(y.Strings.errors.actionAlreadyParented());this._items.push(e),e.parent||e.setParent(this._owner),e._actionCollection=this},e.prototype.removeAction=function(e){this.expandedAction&&this._expandedAction===e&&this.collapseExpandedAction();var t=this._items.indexOf(e);if(t>=0){this._items.splice(t,1),e.setParent(void 0),e._actionCollection=void 0;for(var i=0;i<this._renderedActions.length;i++)if(this._renderedActions[i]===e){this._renderedActions.splice(i,1);break}return!0}return!1},e.prototype.clear=function(){this._items=[],this._renderedActions=[],this._expandedAction=void 0},e.prototype.getAllInputs=function(e){void 0===e&&(e=!0);var t=[];if(e)for(var i=0,n=this._items;i<n.length;i++){var r=n[i];t.push.apply(t,r.getAllInputs())}return t},e.prototype.getResourceInformation=function(){for(var e=[],t=0,i=this._items;t<i.length;t++){var n=i[t];e.push.apply(e,n.getResourceInformation())}return e},Object.defineProperty(e.prototype,"renderedActionCount",{get:function(){return this._renderedActions.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"expandedAction",{get:function(){return this._expandedAction},enumerable:!1,configurable:!0}),e}(),ve=function(e){function t(){var t=e.call(this)||this;return t._actionCollection=new ye(t),t}return r(t,e),t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this._actionCollection.parse(t.actions,i)},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i),this._actionCollection.toJSON(t,"actions",i)},t.prototype.internalRender=function(){return this._actionCollection.render(void 0!==this.orientation?this.orientation:this.hostConfig.actions.actionsOrientation)},t.prototype.releaseDOMResources=function(){e.prototype.releaseDOMResources.call(this),this._actionCollection.releaseDOMResources()},t.prototype.isBleedingAtBottom=function(){return 0===this._actionCollection.renderedActionCount?e.prototype.isBleedingAtBottom.call(this):1===this._actionCollection.getActionCount()?void 0!==this._actionCollection.expandedAction&&!this.hostConfig.actions.preExpandSingleShowCardAction:void 0!==this._actionCollection.expandedAction},t.prototype.getJsonTypeName=function(){return"ActionSet"},t.prototype.getActionCount=function(){return this._actionCollection.getActionCount()},t.prototype.getActionAt=function(t){return t>=0&&t<this.getActionCount()?this._actionCollection.getActionAt(t):e.prototype.getActionAt.call(this,t)},t.prototype.getActionById=function(t){return this._actionCollection.getActionById(t)||e.prototype.getActionById.call(this,t)},t.prototype.getAllActions=function(){for(var t=e.prototype.getAllActions.call(this),i=0;i<this.getActionCount();i++){var n=this.getActionAt(i);n&&t.push(n)}return t},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),this._actionCollection.validateProperties(t)},t.prototype.addAction=function(e){this._actionCollection.addAction(e)},t.prototype.getAllInputs=function(e){return void 0===e&&(e=!0),e?this._actionCollection.getAllInputs():[]},t.prototype.getResourceInformation=function(){return this._actionCollection.getResourceInformation()},t.prototype.findDOMNodeOwner=function(t){for(var i=void 0,n=0;n<this.getActionCount();n++){var r=this.getActionAt(n);if(r&&(i=r.findDOMNodeOwner(t)))return i}return e.prototype.findDOMNodeOwner.call(this,t)},t.prototype.getElementById=function(t){var i=e.prototype.getElementById.call(this,t);return i||(i=this.getElementByIdFromAction(t)),i},Object.defineProperty(t.prototype,"isInteractive",{get:function(){return!0},enumerable:!1,configurable:!0}),t.orientationProperty=new m.EnumProperty(m.Versions.v1_1,"orientation",p.Orientation),o([(0,m.property)(t.orientationProperty)],t.prototype,"orientation",void 0),t}(w);t.ActionSet=ve;var be=function(e){function t(t,i,n,r){var o=e.call(this,t,i,[{value:p.ContainerStyle.Default},{value:p.ContainerStyle.Emphasis},{targetVersion:m.Versions.v1_2,value:p.ContainerStyle.Accent},{targetVersion:m.Versions.v1_2,value:p.ContainerStyle.Good},{targetVersion:m.Versions.v1_2,value:p.ContainerStyle.Attention},{targetVersion:m.Versions.v1_2,value:p.ContainerStyle.Warning}],n,r)||this;return o.targetVersion=t,o.name=i,o.defaultValue=n,o.onGetInitialValue=r,o}return r(t,e),t}(m.ValueSetProperty);t.ContainerStyleProperty=be;var Ce=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),Object.defineProperty(t.prototype,"style",{get:function(){if(this.allowCustomStyle){var e=this.getValue(t.styleProperty);if(e&&this.hostConfig.containerStyles.getStyleByName(e))return e}},set:function(e){this.setValue(t.styleProperty,e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"allowCustomStyle",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hasExplicitStyle",{get:function(){return void 0!==this.getValue(t.styleProperty)},enumerable:!1,configurable:!0}),t.prototype.applyBorder=function(){},t.prototype.applyBackground=function(){if(this.renderedElement){var e=this.hostConfig.containerStyles.getStyleByName(this.style,this.hostConfig.containerStyles.getStyleByName(this.defaultStyle));if(e.backgroundColor){var t=u.stringToCssColor(e.backgroundColor);t&&(this.renderedElement.style.backgroundColor=t)}}},t.prototype.applyPadding=function(){if(e.prototype.applyPadding.call(this),this.renderedElement){var t=new c.SpacingDefinition;if(this.getEffectivePadding()&&(t=this.hostConfig.paddingDefinitionToSpacingDefinition(this.getEffectivePadding())),this.renderedElement.style.paddingTop=t.top+"px",this.renderedElement.style.paddingRight=t.right+"px",this.renderedElement.style.paddingBottom=t.bottom+"px",this.renderedElement.style.paddingLeft=t.left+"px",this.isBleeding()){var i=new c.PaddingDefinition;this.getImmediateSurroundingPadding(i);var n=this.hostConfig.paddingDefinitionToSpacingDefinition(i);this.renderedElement.style.marginRight="-"+n.right+"px",this.renderedElement.style.marginLeft="-"+n.left+"px",this.isDesignMode()||(this.renderedElement.style.marginTop="-"+n.top+"px",this.renderedElement.style.marginBottom="-"+n.bottom+"px"),this.separatorElement&&this.separatorOrientation===p.Orientation.Horizontal&&(this.separatorElement.style.marginLeft="-"+n.left+"px",this.separatorElement.style.marginRight="-"+n.right+"px")}else this.renderedElement.style.marginRight="0",this.renderedElement.style.marginLeft="0",this.renderedElement.style.marginTop="0",this.renderedElement.style.marginBottom="0",this.separatorElement&&this.separatorOrientation===p.Orientation.Horizontal&&(this.separatorElement.style.marginRight="0",this.separatorElement.style.marginLeft="0")}},t.prototype.getHasBackground=function(e){void 0===e&&(e=!1);for(var i=this.parent;i;){var n;if(n=!e&&i instanceof Ee&&i.backgroundImage.isValid(),i instanceof t&&this.hasExplicitStyle&&(i.getEffectiveStyle()!==this.getEffectiveStyle()||n))return!0;i=i.parent}return!1},t.prototype.getDefaultPadding=function(){return this.getHasBackground()||this.getHasBorder()?new c.PaddingDefinition(p.Spacing.Padding,p.Spacing.Padding,p.Spacing.Padding,p.Spacing.Padding):e.prototype.getDefaultPadding.call(this)},t.prototype.internalValidateProperties=function(i){e.prototype.internalValidateProperties.call(this,i);var n=this.getValue(t.styleProperty);void 0!==n&&(this.hostConfig.containerStyles.getStyleByName(n)||i.addFailure(this,p.ValidationEvent.InvalidPropertyValue,y.Strings.errors.invalidPropertyValue(n,"style")))},t.prototype.render=function(){var t=e.prototype.render.call(this);return t&&this.getHasBackground()&&this.applyBackground(),this.applyBorder(),t},t.prototype.getEffectiveStyle=function(){return this.style||e.prototype.getEffectiveStyle.call(this)},t.styleProperty=new be(m.Versions.v1_0,"style"),o([(0,m.property)(t.styleProperty)],t.prototype,"style",null),t}(V);t.StylableCardElementContainer=Ce;var we=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._bleed=!1,t}return r(t,e),t.prototype.adjustRenderedElementSize=function(t){e.prototype.adjustRenderedElementSize.call(this,t),this.minPixelHeight&&(t.style.minHeight=this.minPixelHeight+"px")},t.prototype.getHasExpandedAction=function(){return!1},t.prototype.getBleed=function(){return this._bleed},t.prototype.setBleed=function(e){this._bleed=e},Object.defineProperty(t.prototype,"renderedActionCount",{get:function(){return 0},enumerable:!1,configurable:!0}),t.prototype.isBleeding=function(){return(this.getHasBackground()||this.hostConfig.alwaysAllowBleed)&&this.getBleed()},t.bleedProperty=new m.BoolProperty(m.Versions.v1_2,"bleed",!1),t.minHeightProperty=new m.PixelSizeProperty(m.Versions.v1_2,"minHeight"),o([(0,m.property)(t.bleedProperty)],t.prototype,"_bleed",void 0),o([(0,m.property)(t.minHeightProperty)],t.prototype,"minPixelHeight",void 0),t}(Ce);t.ContainerBase=we;var Se=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getSchemaKey=function(){return"BackgroundImage"},t.prototype.internalParse=function(t,i){if("string"!=typeof t)return e.prototype.internalParse.call(this,t,i);this.resetDefaultValues(),this.url=t},t.prototype.apply=function(e){if(this.url&&e.renderedElement){switch(e.renderedElement.style.backgroundImage="url('"+e.preProcessPropertyValue(t.urlProperty,this.url)+"')",this.fillMode){case p.FillMode.Repeat:e.renderedElement.style.backgroundRepeat="repeat";break;case p.FillMode.RepeatHorizontally:e.renderedElement.style.backgroundRepeat="repeat-x";break;case p.FillMode.RepeatVertically:e.renderedElement.style.backgroundRepeat="repeat-y";break;case p.FillMode.Cover:default:e.renderedElement.style.backgroundRepeat="no-repeat",e.renderedElement.style.backgroundSize="cover"}switch(this.horizontalAlignment){case p.HorizontalAlignment.Left:break;case p.HorizontalAlignment.Center:e.renderedElement.style.backgroundPositionX="center";break;case p.HorizontalAlignment.Right:e.renderedElement.style.backgroundPositionX="right"}switch(this.verticalAlignment){case p.VerticalAlignment.Top:break;case p.VerticalAlignment.Center:e.renderedElement.style.backgroundPositionY="center";break;case p.VerticalAlignment.Bottom:e.renderedElement.style.backgroundPositionY="bottom"}}},t.prototype.isValid=function(){return!!this.url},t.urlProperty=new m.StringProperty(m.Versions.v1_0,"url"),t.fillModeProperty=new m.EnumProperty(m.Versions.v1_2,"fillMode",p.FillMode,p.FillMode.Cover),t.horizontalAlignmentProperty=new m.EnumProperty(m.Versions.v1_2,"horizontalAlignment",p.HorizontalAlignment,p.HorizontalAlignment.Left),t.verticalAlignmentProperty=new m.EnumProperty(m.Versions.v1_2,"verticalAlignment",p.VerticalAlignment,p.VerticalAlignment.Top),o([(0,m.property)(t.urlProperty)],t.prototype,"url",void 0),o([(0,m.property)(t.fillModeProperty)],t.prototype,"fillMode",void 0),o([(0,m.property)(t.horizontalAlignmentProperty)],t.prototype,"horizontalAlignment",void 0),o([(0,m.property)(t.verticalAlignmentProperty)],t.prototype,"verticalAlignment",void 0),t}(m.SerializableObject);t.BackgroundImage=Se;var Ee=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._items=[],t._renderedItems=[],t}return r(t,e),Object.defineProperty(t.prototype,"backgroundImage",{get:function(){return this.getValue(t.backgroundImageProperty)},enumerable:!1,configurable:!0}),t.prototype.insertItemAt=function(e,t,i){if(e.parent&&!i)throw new Error(y.Strings.errors.elementAlreadyParented());if(!e.isStandalone)throw new Error(y.Strings.errors.elementTypeNotStandalone(e.getJsonTypeName()));t<0||t>=this._items.length?this._items.push(e):this._items.splice(t,0,e),e.setParent(this)},t.prototype.getItemsCollectionPropertyName=function(){return"items"},t.prototype.applyBackground=function(){this.backgroundImage.isValid()&&this.renderedElement&&this.backgroundImage.apply(this),e.prototype.applyBackground.call(this)},t.prototype.applyRTL=function(e){void 0!==this.rtl&&(e.dir=this.rtl?"rtl":"ltr")},t.prototype.internalRender=function(){this._renderedItems=[];var e=this.hostConfig,t=document.createElement("div");switch(this.applyRTL(t),t.classList.add(e.makeCssClassName("ac-container")),t.style.display="flex",t.style.flexDirection="column",c.GlobalSettings.useAdvancedCardBottomTruncation&&(t.style.minHeight="-webkit-min-content"),this.getEffectiveVerticalContentAlignment()){case p.VerticalAlignment.Center:t.style.justifyContent="center";break;case p.VerticalAlignment.Bottom:t.style.justifyContent="flex-end";break;default:t.style.justifyContent="flex-start"}if(this._items.length>0)for(var i=0,n=this._items;i<n.length;i++){var r=n[i],o=this.isElementAllowed(r)?r.render():void 0;o&&(this._renderedItems.length>0&&r.separatorElement&&(r.separatorElement.style.flex="0 0 auto",u.appendChild(t,r.separatorElement)),u.appendChild(t,o),this._renderedItems.push(r))}else if(this.isDesignMode()){var s=this.createPlaceholderElement();s.style.width="100%",s.style.height="100%",t.appendChild(s)}return t},t.prototype.truncateOverflow=function(e){if(this.renderedElement){for(var t=this.renderedElement.offsetTop+e+1,i=function(e){var n=e.renderedElement;if(n)switch(u.getFitStatus(n,t)){case p.ContainerFitStatus.FullyInContainer:e.resetOverflow()&&i(e);break;case p.ContainerFitStatus.Overflowing:var r=t-n.offsetTop;e.handleOverflow(r);break;case p.ContainerFitStatus.FullyOutOfContainer:e.handleOverflow(0)}},n=0,r=this._items;n<r.length;n++){var o=r[n];i(o)}return!0}return!1},t.prototype.undoOverflowTruncation=function(){for(var e=0,t=this._items;e<t.length;e++)t[e].resetOverflow()},t.prototype.getHasBackground=function(t){return void 0===t&&(t=!1),!t&&this.backgroundImage.isValid()||e.prototype.getHasBackground.call(this,t)},t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this.clear(),this.setShouldFallback(!1);var n=t[this.getItemsCollectionPropertyName()];if(Array.isArray(n))for(var r=0,o=n;r<o.length;r++){var s=o[r],a=i.parseElement(this,s,this.forbiddenChildElements(),!this.isDesignMode());a&&this.insertItemAt(a,-1,!0)}},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i);var n=this.getItemsCollectionPropertyName();i.serializeArray(t,n,this._items)},Object.defineProperty(t.prototype,"isSelectable",{get:function(){return!0},enumerable:!1,configurable:!0}),t.prototype.getEffectivePadding=function(){return c.GlobalSettings.removePaddingFromContainersWithBackgroundImage&&!this.getHasBackground(!0)?new c.PaddingDefinition:e.prototype.getEffectivePadding.call(this)},t.prototype.getEffectiveVerticalContentAlignment=function(){if(void 0!==this.verticalContentAlignment)return this.verticalContentAlignment;var e=this.getParentContainer();return e?e.getEffectiveVerticalContentAlignment():p.VerticalAlignment.Top},t.prototype.getItemCount=function(){return this._items.length},t.prototype.getItemAt=function(e){return this._items[e]},t.prototype.getFirstVisibleRenderedItem=function(){if(this.renderedElement&&this._renderedItems&&this._renderedItems.length>0)for(var e=0,t=this._renderedItems;e<t.length;e++){var i=t[e];if(i.isVisible)return i}},t.prototype.getLastVisibleRenderedItem=function(){if(this.renderedElement&&this._renderedItems&&this._renderedItems.length>0)for(var e=this._renderedItems.length-1;e>=0;e--)if(this._renderedItems[e].isVisible)return this._renderedItems[e]},t.prototype.getJsonTypeName=function(){return"Container"},t.prototype.isFirstElement=function(e){for(var t=this.isDesignMode(),i=0,n=this._items;i<n.length;i++){var r=n[i];if(r.isVisible||t)return r===e}return!1},t.prototype.isLastElement=function(e){for(var t=this.isDesignMode(),i=this._items.length-1;i>=0;i--)if(this._items[i].isVisible||t)return this._items[i]===e;return!1},t.prototype.isRtl=function(){if(void 0!==this.rtl)return this.rtl;var e=this.getParentContainer();return!!e&&e.isRtl()},t.prototype.isBleedingAtTop=function(){var e=this.getFirstVisibleRenderedItem();return this.isBleeding()||!!e&&e.isBleedingAtTop()},t.prototype.isBleedingAtBottom=function(){var e=this.getLastVisibleRenderedItem();return this.isBleeding()||!!e&&e.isBleedingAtBottom()&&e.getEffectiveStyle()===this.getEffectiveStyle()},t.prototype.indexOf=function(e){return this._items.indexOf(e)},t.prototype.addItem=function(e){this.insertItemAt(e,-1,!1)},t.prototype.insertItemBefore=function(e,t){this.insertItemAt(e,this._items.indexOf(t),!1)},t.prototype.insertItemAfter=function(e,t){this.insertItemAt(e,this._items.indexOf(t)+1,!1)},t.prototype.removeItem=function(e){var t=this._items.indexOf(e);return t>=0&&(this._items.splice(t,1),e.setParent(void 0),this.updateLayout(),!0)},t.prototype.clear=function(){this._items=[],this._renderedItems=[]},t.prototype.getResourceInformation=function(){var t=e.prototype.getResourceInformation.call(this);return this.backgroundImage.isValid()&&t.push({url:this.backgroundImage.url,mimeType:"image"}),t},t.prototype.getActionById=function(t){var i=e.prototype.getActionById.call(this,t);if(!i&&(this.selectAction&&(i=this.selectAction.getActionById(t)),!i))for(var n=0,r=this._items;n<r.length&&!(i=r[n].getActionById(t));n++);return i},Object.defineProperty(t.prototype,"padding",{get:function(){return this.getPadding()},set:function(e){this.setPadding(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectAction",{get:function(){return this._selectAction},set:function(e){this._selectAction=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"bleed",{get:function(){return this.getBleed()},set:function(e){this.setBleed(e)},enumerable:!1,configurable:!0}),t.backgroundImageProperty=new m.SerializableObjectProperty(m.Versions.v1_0,"backgroundImage",Se),t.verticalContentAlignmentProperty=new m.EnumProperty(m.Versions.v1_1,"verticalContentAlignment",p.VerticalAlignment),t.rtlProperty=new m.BoolProperty(m.Versions.v1_0,"rtl"),o([(0,m.property)(t.backgroundImageProperty)],t.prototype,"backgroundImage",null),o([(0,m.property)(t.verticalContentAlignmentProperty)],t.prototype,"verticalContentAlignment",void 0),o([(0,m.property)(t.rtlProperty)],t.prototype,"rtl",void 0),t}(we);t.Container=Ee;var xe=function(e){function t(t){void 0===t&&(t="stretch");var i=e.call(this)||this;return i.width="stretch",i._computedWeight=0,i.width=t,i}return r(t,e),t.prototype.adjustRenderedElementSize=function(e){this.isDesignMode()?(e.style.minWidth="20px",e.style.minHeight=(this.minPixelHeight?Math.max(this.minPixelHeight,20):20)+"px"):(e.style.minWidth="0",this.minPixelHeight&&(e.style.minHeight=this.minPixelHeight+"px")),"auto"===this.width?e.style.flex="0 1 auto":"stretch"===this.width?e.style.flex="1 1 50px":this.width instanceof c.SizeAndUnit&&(this.width.unit===p.SizeUnit.Pixel?(e.style.flex="0 0 auto",e.style.width=this.width.physicalSize+"px"):e.style.flex="1 1 "+(this._computedWeight>0?this._computedWeight:this.width.physicalSize)+"%")},t.prototype.shouldSerialize=function(e){return!0},Object.defineProperty(t.prototype,"separatorOrientation",{get:function(){return p.Orientation.Vertical},enumerable:!1,configurable:!0}),t.prototype.getJsonTypeName=function(){return"Column"},Object.defineProperty(t.prototype,"hasVisibleSeparator",{get:function(){return!!(this.parent&&this.parent instanceof Pe)&&void 0!==this.separatorElement&&!this.parent.isLeftMostElement(this)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),t.widthProperty=new m.CustomProperty(m.Versions.v1_0,"width",(function(e,t,i,n){var r=t.defaultValue,o=i[t.name],s=!1;if("number"!=typeof o||isNaN(o))if("auto"===o||"stretch"===o)r=o;else if("string"==typeof o)try{(r=c.SizeAndUnit.parse(o)).unit===p.SizeUnit.Pixel&&t.targetVersion.compareTo(n.targetVersion)>0&&(s=!0)}catch(e){s=!0}else s=!0;else r=new c.SizeAndUnit(o,p.SizeUnit.Weight);return s&&(n.logParseEvent(e,p.ValidationEvent.InvalidPropertyValue,y.Strings.errors.invalidColumnWidth(o)),r="auto"),r}),(function(e,t,i,n,r){n instanceof c.SizeAndUnit?n.unit===p.SizeUnit.Pixel?r.serializeValue(i,"width",n.physicalSize+"px"):r.serializeNumber(i,"width",n.physicalSize):r.serializeValue(i,"width",n)}),"stretch"),o([(0,m.property)(t.widthProperty)],t.prototype,"width",void 0),t}(Ee);t.Column=xe;var Pe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._columns=[],t}return r(t,e),t.prototype.createColumnInstance=function(e,t){return t.parseCardObject(this,e,[],!this.isDesignMode(),(function(e){return e&&"Column"!==e?void 0:new xe}),(function(e,i){t.logParseEvent(void 0,p.ValidationEvent.ElementTypeNotAllowed,y.Strings.errors.elementTypeNotAllowed(e))}))},t.prototype.internalRender=function(){if(this._renderedColumns=[],this._columns.length>0){var e=this.hostConfig,t=document.createElement("div");switch(t.className=e.makeCssClassName("ac-columnSet"),t.style.display="flex",c.GlobalSettings.useAdvancedCardBottomTruncation&&(t.style.minHeight="-webkit-min-content"),this.getEffectiveHorizontalAlignment()){case p.HorizontalAlignment.Center:t.style.justifyContent="center";break;case p.HorizontalAlignment.Right:t.style.justifyContent="flex-end";break;default:t.style.justifyContent="flex-start"}for(var i=0,n=0,r=this._columns;n<r.length;n++)(a=r[n]).width instanceof c.SizeAndUnit&&a.width.unit===p.SizeUnit.Weight&&(i+=a.width.physicalSize);for(var o=0,s=this._columns;o<s.length;o++){var a;if((a=s[o]).width instanceof c.SizeAndUnit&&a.width.unit===p.SizeUnit.Weight&&i>0){var l=100/i*a.width.physicalSize;a._computedWeight=l}var d=a.render();d&&(this._renderedColumns.length>0&&a.separatorElement&&(a.separatorElement.style.flex="0 0 auto",u.appendChild(t,a.separatorElement)),u.appendChild(t,d),this._renderedColumns.push(a))}return this._renderedColumns.length>0?t:void 0}},t.prototype.truncateOverflow=function(e){for(var t=0,i=this._columns;t<i.length;t++)i[t].handleOverflow(e);return!0},t.prototype.undoOverflowTruncation=function(){for(var e=0,t=this._columns;e<t.length;e++)t[e].resetOverflow()},Object.defineProperty(t.prototype,"isSelectable",{get:function(){return!0},enumerable:!1,configurable:!0}),t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this._columns=[],this._renderedColumns=[];var n=t.columns;if(Array.isArray(n))for(var r=0,o=n;r<o.length;r++){var s=o[r],a=this.createColumnInstance(s,i);a&&this._columns.push(a)}},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i),i.serializeArray(t,"columns",this._columns)},t.prototype.isFirstElement=function(e){for(var t=0,i=this._columns;t<i.length;t++){var n=i[t];if(n.isVisible)return n===e}return!1},t.prototype.isBleedingAtTop=function(){if(this.isBleeding())return!0;if(this._renderedColumns&&this._renderedColumns.length>0)for(var e=0,t=this._columns;e<t.length;e++)if(t[e].isBleedingAtTop())return!0;return!1},t.prototype.isBleedingAtBottom=function(){if(this.isBleeding())return!0;if(this._renderedColumns&&this._renderedColumns.length>0)for(var e=0,t=this._columns;e<t.length;e++)if(t[e].isBleedingAtBottom())return!0;return!1},t.prototype.getItemCount=function(){return this._columns.length},t.prototype.getFirstVisibleRenderedItem=function(){return this.renderedElement&&this._renderedColumns&&this._renderedColumns.length>0?this._renderedColumns[0]:void 0},t.prototype.getLastVisibleRenderedItem=function(){return this.renderedElement&&this._renderedColumns&&this._renderedColumns.length>0?this._renderedColumns[this._renderedColumns.length-1]:void 0},t.prototype.getColumnAt=function(e){return this._columns[e]},t.prototype.getItemAt=function(e){return this.getColumnAt(e)},t.prototype.getJsonTypeName=function(){return"ColumnSet"},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t);for(var i=0,n=0,r=0,o=this._columns;r<o.length;r++){var s=o[r];"number"==typeof s.width?i++:"stretch"===s.width&&n++}i>0&&n>0&&t.addFailure(this,p.ValidationEvent.Hint,y.Strings.hints.dontUseWeightedAndStrecthedColumnsInSameSet())},t.prototype.addColumn=function(e){if(e.parent)throw new Error(y.Strings.errors.columnAlreadyBelongsToAnotherSet());this._columns.push(e),e.setParent(this)},t.prototype.removeItem=function(e){if(e instanceof xe){var t=this._columns.indexOf(e);if(t>=0)return this._columns.splice(t,1),e.setParent(void 0),this.updateLayout(),!0}return!1},t.prototype.indexOf=function(e){return e instanceof xe?this._columns.indexOf(e):-1},t.prototype.isLeftMostElement=function(e){return 0===this._columns.indexOf(e)},t.prototype.isRightMostElement=function(e){return this._columns.indexOf(e)===this._columns.length-1},t.prototype.isTopElement=function(e){return this._columns.indexOf(e)>=0},t.prototype.isBottomElement=function(e){return this._columns.indexOf(e)>=0},t.prototype.getActionById=function(e){for(var t=void 0,i=0,n=this._columns;i<n.length&&!(t=n[i].getActionById(e));i++);return t},Object.defineProperty(t.prototype,"bleed",{get:function(){return this.getBleed()},set:function(e){this.setBleed(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"padding",{get:function(){return this.getPadding()},set:function(e){this.setPadding(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectAction",{get:function(){return this._selectAction},set:function(e){this._selectAction=e},enumerable:!1,configurable:!0}),t}(we);function _e(e){var t=e.getRootElement(),i=t&&t.onImageLoaded?t.onImageLoaded:Ne.onImageLoaded;i&&i(e)}function Ae(e,t,i){var n=e.getRootElement(),r=n&&n.onAnchorClicked?n.onAnchorClicked:Ne.onAnchorClicked;return void 0!==r&&r(e,t,i)}function Te(e,t){var i=e.parent?e.parent.getRootElement():void 0,n=i&&i.onInlineCardExpanded?i.onInlineCardExpanded:Ne.onInlineCardExpanded;n&&n(e,t)}function Ie(e,t){void 0===t&&(t=!0);var i=e.getRootElement();t&&i.updateLayout();var n=i,r=n&&n.onElementVisibilityChanged?n.onElementVisibilityChanged:Ne.onElementVisibilityChanged;void 0!==r&&r(e)}function Oe(e,t){t?(e.readOnly=!0,e.required=!0):(e.readOnly=!1,e.required=!1)}t.ColumnSet=Pe;var Me=function(e){function t(){var t=e.call(this)||this;return t._actionCollection=new ye(t),t}return r(t,e),t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this.parseActions(t,i)},t.prototype.parseActions=function(e,t){this._actionCollection.parse(e.actions,t)},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i),this._actionCollection.toJSON(t,"actions",i)},t.prototype.internalRender=function(){var t=e.prototype.internalRender.call(this);if(t){var i=this._actionCollection.render(this.hostConfig.actions.actionsOrientation);return i&&(u.appendChild(t,C(this.hostConfig,{spacing:this.hostConfig.getEffectiveSpacing(this.hostConfig.actions.spacing)},p.Orientation.Horizontal)),u.appendChild(t,i)),this.renderIfEmpty||t.children.length>0?t:void 0}},t.prototype.getHasExpandedAction=function(){return 0!==this.renderedActionCount&&(1===this.renderedActionCount?void 0!==this._actionCollection.expandedAction&&!this.hostConfig.actions.preExpandSingleShowCardAction:void 0!==this._actionCollection.expandedAction)},Object.defineProperty(t.prototype,"renderedActionCount",{get:function(){return this._actionCollection.renderedActionCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"renderIfEmpty",{get:function(){return!1},enumerable:!1,configurable:!0}),t.prototype.releaseDOMResources=function(){e.prototype.releaseDOMResources.call(this),this._actionCollection.releaseDOMResources()},t.prototype.getActionCount=function(){return this._actionCollection.getActionCount()},t.prototype.getActionAt=function(t){return t>=0&&t<this.getActionCount()?this._actionCollection.getActionAt(t):e.prototype.getActionAt.call(this,t)},t.prototype.getActionById=function(t){return this._actionCollection.getActionById(t)||e.prototype.getActionById.call(this,t)},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),this._actionCollection&&this._actionCollection.validateProperties(t)},t.prototype.isLastElement=function(t){return e.prototype.isLastElement.call(this,t)&&0===this._actionCollection.getActionCount()},t.prototype.addAction=function(e){this._actionCollection.addAction(e)},t.prototype.clear=function(){e.prototype.clear.call(this),this._actionCollection.clear()},t.prototype.getAllInputs=function(t){void 0===t&&(t=!0);var i=e.prototype.getAllInputs.call(this,t);return t&&i.push.apply(i,this._actionCollection.getAllInputs(t)),i},t.prototype.getResourceInformation=function(){var t=e.prototype.getResourceInformation.call(this);return t.push.apply(t,this._actionCollection.getResourceInformation()),t},t.prototype.isBleedingAtBottom=function(){return 0===this._actionCollection.renderedActionCount?e.prototype.isBleedingAtBottom.call(this):1===this._actionCollection.getActionCount()?void 0!==this._actionCollection.expandedAction&&!this.hostConfig.actions.preExpandSingleShowCardAction:void 0!==this._actionCollection.expandedAction},t.prototype.getForbiddenActionNames=function(){return[]},t.prototype.getElementById=function(t){var i=e.prototype.getElementById.call(this,t);return i||(i=this.getElementByIdFromAction(t)),i},Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),t}(Ee);t.ContainerWithActions=Me;var Ve=function(e){function t(t,i){var n=e.call(this,t,i,void 0)||this;return n.targetVersion=t,n.name=i,n}return r(t,e),t.prototype.parse=function(e,t,i){var n=i.parseAction(e.parent,t[this.name],[],!1);if(void 0!==n){if(n instanceof le)return n;i.logParseEvent(e,p.ValidationEvent.ActionTypeNotAllowed,y.Strings.errors.actionTypeNotAllowed(n.getJsonTypeName()))}i.logParseEvent(e,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.propertyMustBeSet("action"))},t.prototype.toJSON=function(e,t,i,n){n.serializeValue(t,this.name,i?i.toJSON(n):void 0,void 0,!0)},t}(m.PropertyDefinition);t.RefreshActionProperty=Ve;var ke=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),Object.defineProperty(t.prototype,"action",{get:function(){return this.getValue(t.actionProperty)},set:function(e){this.setValue(t.actionProperty,e),e&&e.setParent(this.parent)},enumerable:!1,configurable:!0}),t.prototype.getSchemaKey=function(){return"RefreshDefinition"},t.actionProperty=new Ve(m.Versions.v1_4,"action"),t.userIdsProperty=new m.StringArrayProperty(m.Versions.v1_4,"userIds"),o([(0,m.property)(t.actionProperty)],t.prototype,"action",null),o([(0,m.property)(t.userIdsProperty)],t.prototype,"userIds",void 0),t}(m.SerializableObject);t.RefreshDefinition=ke;var ze=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getSchemaKey=function(){return"AuthCardButton"},t.typeProperty=new m.StringProperty(m.Versions.v1_4,"type"),t.titleProperty=new m.StringProperty(m.Versions.v1_4,"title"),t.imageProperty=new m.StringProperty(m.Versions.v1_4,"image"),t.valueProperty=new m.StringProperty(m.Versions.v1_4,"value"),o([(0,m.property)(t.typeProperty)],t.prototype,"type",void 0),o([(0,m.property)(t.titleProperty)],t.prototype,"title",void 0),o([(0,m.property)(t.imageProperty)],t.prototype,"image",void 0),o([(0,m.property)(t.valueProperty)],t.prototype,"value",void 0),t}(m.SerializableObject);t.AuthCardButton=ze;var Le=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getSchemaKey=function(){return"TokenExchangeResource"},t.idProperty=new m.StringProperty(m.Versions.v1_4,"id"),t.uriProperty=new m.StringProperty(m.Versions.v1_4,"uri"),t.providerIdProperty=new m.StringProperty(m.Versions.v1_4,"providerId"),o([(0,m.property)(t.idProperty)],t.prototype,"id",void 0),o([(0,m.property)(t.uriProperty)],t.prototype,"uri",void 0),o([(0,m.property)(t.providerIdProperty)],t.prototype,"providerId",void 0),t}(m.SerializableObject);t.TokenExchangeResource=Le;var De=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getSchemaKey=function(){return"Authentication"},t.textProperty=new m.StringProperty(m.Versions.v1_4,"text"),t.connectionNameProperty=new m.StringProperty(m.Versions.v1_4,"connectionName"),t.buttonsProperty=new m.SerializableObjectCollectionProperty(m.Versions.v1_4,"buttons",ze),t.tokenExchangeResourceProperty=new m.SerializableObjectProperty(m.Versions.v1_4,"tokenExchangeResource",Le,!0),o([(0,m.property)(t.textProperty)],t.prototype,"text",void 0),o([(0,m.property)(t.connectionNameProperty)],t.prototype,"connectionName",void 0),o([(0,m.property)(t.buttonsProperty)],t.prototype,"buttons",void 0),o([(0,m.property)(t.tokenExchangeResourceProperty)],t.prototype,"tokenExchangeResource",void 0),t}(m.SerializableObject);t.Authentication=De;var Ne=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.designMode=!1,t}return r(t,e),Object.defineProperty(t.prototype,"refresh",{get:function(){return this.getValue(t.refreshProperty)},set:function(e){this.setValue(t.refreshProperty,e),e&&(e.parent=this)},enumerable:!1,configurable:!0}),Object.defineProperty(t,"processMarkdown",{get:function(){throw new Error(y.Strings.errors.processMarkdownEventRemoved())},set:function(e){throw new Error(y.Strings.errors.processMarkdownEventRemoved())},enumerable:!1,configurable:!0}),t.applyMarkdown=function(e){var i={didProcess:!1};if(t.onProcessMarkdown)t.onProcessMarkdown(e,i);else if(window.markdownit){var n=window.markdownit;i.outputHtml=n().render(e),i.didProcess=!0}else t._haveWarnedAboutNoMarkdownProcessing||(console.warn(y.Strings.errors.markdownProcessingNotEnabled),t._haveWarnedAboutNoMarkdownProcessing=!0);return i},t.prototype.isVersionSupported=function(){return!!this.bypassVersionCheck||!(!this.version||!this.version.isValid||this.maxVersion.major<this.version.major||this.maxVersion.major===this.version.major&&this.maxVersion.minor<this.version.minor)},t.prototype.getDefaultSerializationContext=function(){return new $e(this.version)},t.prototype.getItemsCollectionPropertyName=function(){return"body"},t.prototype.internalParse=function(i,n){this._fallbackCard=void 0;var r=n.parseElement(void 0,i.fallback,this.forbiddenChildElements(),!this.isDesignMode());r&&(this._fallbackCard=new t,this._fallbackCard.addItem(r)),e.prototype.internalParse.call(this,i,n)},t.prototype.internalToJSON=function(i,n){this.setValue(t.versionProperty,n.targetVersion),e.prototype.internalToJSON.call(this,i,n)},t.prototype.internalRender=function(){var t=e.prototype.internalRender.call(this);return c.GlobalSettings.useAdvancedCardBottomTruncation&&t&&t.style.removeProperty("minHeight"),t},t.prototype.getHasBackground=function(e){return void 0===e&&(e=!1),!0},t.prototype.getDefaultPadding=function(){return new c.PaddingDefinition(p.Spacing.Padding,p.Spacing.Padding,p.Spacing.Padding,p.Spacing.Padding)},t.prototype.shouldSerialize=function(e){return!0},Object.defineProperty(t.prototype,"renderIfEmpty",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"bypassVersionCheck",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"allowCustomStyle",{get:function(){return this.hostConfig.adaptiveCard&&this.hostConfig.adaptiveCard.allowCustomStyle},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hasBackground",{get:function(){return!0},enumerable:!1,configurable:!0}),t.prototype.getJsonTypeName=function(){return"AdaptiveCard"},t.prototype.internalValidateProperties=function(t){e.prototype.internalValidateProperties.call(this,t),"AdaptiveCard"!==this.getValue(w.typeNameProperty)&&t.addFailure(this,p.ValidationEvent.MissingCardType,y.Strings.errors.invalidCardType()),this.bypassVersionCheck||this.version?this.isVersionSupported()||t.addFailure(this,p.ValidationEvent.UnsupportedCardVersion,y.Strings.errors.unsupportedCardVersion(this.version.toString(),this.maxVersion.toString())):t.addFailure(this,p.ValidationEvent.PropertyCantBeNull,y.Strings.errors.propertyMustBeSet("version"))},t.prototype.render=function(t){var i,n=this;this.shouldFallback()&&this._fallbackCard?(this._fallbackCard.hostConfig=this.hostConfig,i=this._fallbackCard.render()):(i=e.prototype.render.call(this))&&(i.classList.add(this.hostConfig.makeCssClassName("ac-adaptiveCard")),c.GlobalSettings.setTabIndexAtCardRoot&&(i.tabIndex=0),this.speak&&i.setAttribute("aria-label",this.speak),i.onmouseenter=function(e){n.updateInputsVisualState(!0)},i.onmouseleave=function(e){n.updateInputsVisualState(!1)},this.getRootElement().updateActionsEnabledState()),t&&(u.appendChild(t,i),this.updateLayout());var r=this.getAllInputs();return document.onclick=function(e){r.forEach((function(t){var i,r;t instanceof Q&&!(null===(i=t.renderedElement)||void 0===i?void 0:i.contains(e.target))&&(null===(r=t.getDropdownElement())||void 0===r||r.classList.remove(n.hostConfig.makeCssClassName("ac-choiceSetInput-filtered-dropdown-open")))}))},i},t.prototype.updateLayout=function(t){if(void 0===t&&(t=!0),e.prototype.updateLayout.call(this,t),c.GlobalSettings.useAdvancedCardBottomTruncation&&this.isDisplayed()){var i=this.hostConfig.getEffectiveSpacing(p.Spacing.Default);this.handleOverflow(this.renderedElement.offsetHeight-i)}},t.prototype.shouldFallback=function(){return e.prototype.shouldFallback.call(this)||!this.isVersionSupported()},Object.defineProperty(t.prototype,"hasVisibleSeparator",{get:function(){return!1},enumerable:!1,configurable:!0}),t.schemaUrl="http://adaptivecards.io/schemas/adaptive-card.json",t.$schemaProperty=new m.CustomProperty(m.Versions.v1_0,"$schema",(function(e,i,n,r){return t.schemaUrl}),(function(e,i,n,r,o){o.serializeValue(n,i.name,t.schemaUrl)})),t.versionProperty=new m.CustomProperty(m.Versions.v1_0,"version",(function(e,t,i,n){var r=m.Version.parse(i[t.name],n);return void 0===r&&(r=m.Versions.latest,n.logParseEvent(e,p.ValidationEvent.InvalidPropertyValue,y.Strings.errors.invalidCardVersion(r.toString()))),r}),(function(e,t,i,n,r){void 0!==n&&r.serializeValue(i,t.name,n.toString())}),m.Versions.v1_0),t.fallbackTextProperty=new m.StringProperty(m.Versions.v1_0,"fallbackText"),t.speakProperty=new m.StringProperty(m.Versions.v1_0,"speak"),t.refreshProperty=new m.SerializableObjectProperty(m.Versions.v1_4,"refresh",ke,!0),t.authenticationProperty=new m.SerializableObjectProperty(m.Versions.v1_4,"authentication",De,!0),t._haveWarnedAboutNoMarkdownProcessing=!1,o([(0,m.property)(t.versionProperty)],t.prototype,"version",void 0),o([(0,m.property)(t.fallbackTextProperty)],t.prototype,"fallbackText",void 0),o([(0,m.property)(t.speakProperty)],t.prototype,"speak",void 0),o([(0,m.property)(t.refreshProperty)],t.prototype,"refresh",null),o([(0,m.property)(t.authenticationProperty)],t.prototype,"authentication",void 0),t}(Me);t.AdaptiveCard=Ne;var Re=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.suppressStyle=!1,t}return r(t,e),t.prototype.getSchemaKey=function(){return"InlineAdaptiveCard"},t.prototype.populateSchema=function(t){e.prototype.populateSchema.call(this,t),t.remove(Ne.$schemaProperty,Ne.versionProperty)},t.prototype.getDefaultPadding=function(){return new c.PaddingDefinition(this.suppressStyle?p.Spacing.None:p.Spacing.Padding,p.Spacing.Padding,this.suppressStyle?p.Spacing.None:p.Spacing.Padding,p.Spacing.Padding)},Object.defineProperty(t.prototype,"bypassVersionCheck",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"defaultStyle",{get:function(){return this.suppressStyle?p.ContainerStyle.Default:this.hostConfig.actions.showCard.style?this.hostConfig.actions.showCard.style:p.ContainerStyle.Emphasis},enumerable:!1,configurable:!0}),t.prototype.render=function(t){var i=e.prototype.render.call(this,t);return i&&(i.setAttribute("aria-live","polite"),i.removeAttribute("tabindex")),i},t}(Ne),$e=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._forbiddenTypes=new Set,t}return r(t,e),t.prototype.internalParseCardObject=function(e,t,i,n,r,o){var s=this,a=void 0;if(t&&"object"==typeof t){var l=new Set;this._forbiddenTypes.forEach((function(e){l.add(e)})),i.forEach((function(e){s._forbiddenTypes.add(e)}));var d=u.parseString(t.type);if(d&&this._forbiddenTypes.has(d))o(d,p.TypeErrorType.ForbiddenType);else{var h=!1;if((a=r(d))?(a.setParent(e),a.parse(t,this),h=c.GlobalSettings.enableFallback&&n&&a.shouldFallback()):(h=c.GlobalSettings.enableFallback&&n,o(d,p.TypeErrorType.UnknownType)),h){var f=t.fallback;!f&&e&&e.setShouldFallback(!0),"string"==typeof f&&"drop"===f.toLowerCase()?a=void 0:"object"==typeof f&&(a=this.internalParseCardObject(e,f,i,!0,r,o))}}this._forbiddenTypes=l}return a},t.prototype.cardObjectParsed=function(e,t){e instanceof re&&this.onParseAction?this.onParseAction(e,t,this):e instanceof w&&this.onParseElement&&this.onParseElement(e,t,this)},t.prototype.shouldSerialize=function(e){return e instanceof re?void 0!==this.actionRegistry.findByName(e.getJsonTypeName()):!(e instanceof w)||void 0!==this.elementRegistry.findByName(e.getJsonTypeName())},t.prototype.parseCardObject=function(e,t,i,n,r,o){var s=new Set(i),a=this.internalParseCardObject(e,t,s,n,r,o);return void 0!==a&&this.cardObjectParsed(a,t),a},t.prototype.parseElement=function(e,t,i,n){var r=this;return this.parseCardObject(e,t,i,n,(function(e){return r.elementRegistry.createInstance(e,r.targetVersion)}),(function(e,t){t===p.TypeErrorType.UnknownType?r.logParseEvent(void 0,p.ValidationEvent.UnknownElementType,y.Strings.errors.unknownElementType(e)):r.logParseEvent(void 0,p.ValidationEvent.ElementTypeNotAllowed,y.Strings.errors.elementTypeNotAllowed(e))}))},t.prototype.parseAction=function(e,t,i,n){var r=this;return this.parseCardObject(e,t,i,n,(function(e){return r.actionRegistry.createInstance(e,r.targetVersion)}),(function(e,t){t===p.TypeErrorType.UnknownType?r.logParseEvent(void 0,p.ValidationEvent.UnknownActionType,y.Strings.errors.unknownActionType(e)):r.logParseEvent(void 0,p.ValidationEvent.ActionTypeNotAllowed,y.Strings.errors.actionTypeNotAllowed(e))}))},Object.defineProperty(t.prototype,"elementRegistry",{get:function(){var e;return null!==(e=this._elementRegistry)&&void 0!==e?e:g.GlobalRegistry.elements},enumerable:!1,configurable:!0}),t.prototype.setElementRegistry=function(e){this._elementRegistry=e},Object.defineProperty(t.prototype,"actionRegistry",{get:function(){var e;return null!==(e=this._actionRegistry)&&void 0!==e?e:g.GlobalRegistry.actions},enumerable:!1,configurable:!0}),t.prototype.setActionRegistry=function(e){this._actionRegistry=e},t}(m.BaseSerializationContext);t.SerializationContext=$e,g.GlobalRegistry.defaultElements.register("Container",Ee),g.GlobalRegistry.defaultElements.register("TextBlock",x),g.GlobalRegistry.defaultElements.register("RichTextBlock",_,m.Versions.v1_2),g.GlobalRegistry.defaultElements.register("TextRun",P,m.Versions.v1_2),g.GlobalRegistry.defaultElements.register("Image",O),g.GlobalRegistry.defaultElements.register("ImageSet",k),g.GlobalRegistry.defaultElements.register("Media",q,m.Versions.v1_1),g.GlobalRegistry.defaultElements.register("FactSet",T),g.GlobalRegistry.defaultElements.register("ColumnSet",Pe),g.GlobalRegistry.defaultElements.register("ActionSet",ve,m.Versions.v1_2),g.GlobalRegistry.defaultElements.register("Input.Text",J),g.GlobalRegistry.defaultElements.register("Input.Date",te),g.GlobalRegistry.defaultElements.register("Input.Time",ne),g.GlobalRegistry.defaultElements.register("Input.Number",ee),g.GlobalRegistry.defaultElements.register("Input.ChoiceSet",Q),g.GlobalRegistry.defaultElements.register("Input.Toggle",X),g.GlobalRegistry.defaultActions.register(ce.JsonTypeName,ce),g.GlobalRegistry.defaultActions.register(se.JsonTypeName,se),g.GlobalRegistry.defaultActions.register(me.JsonTypeName,me),g.GlobalRegistry.defaultActions.register(ue.JsonTypeName,ue,m.Versions.v1_2),g.GlobalRegistry.defaultActions.register(le.JsonTypeName,le,m.Versions.v1_4)},817:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),o=this&&this.__decorate||function(e,t,i,n){var r,o=arguments.length,s=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,i,n);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,i,s):r(t,i))||s);return o>3&&s&&Object.defineProperty(t,i,s),s};Object.defineProperty(t,"__esModule",{value:!0}),t.CardObject=t.ValidationResults=void 0;var s=i(10),a=i(653),l=i(764),p=i(233),c=i(475),u=function(){function e(){this.allIds={},this.validationEvents=[]}return e.prototype.addFailure=function(e,t,i){this.validationEvents.push({phase:s.ValidationPhase.Validation,source:e,event:t,message:i})},e}();t.ValidationResults=u;var d=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._shouldFallback=!1,t}return r(t,e),t.prototype.getSchemaKey=function(){return this.getJsonTypeName()},Object.defineProperty(t.prototype,"requires",{get:function(){return this.getValue(t.requiresProperty)},enumerable:!1,configurable:!0}),t.prototype.contains=function(e){return!!this._renderedElement&&this._renderedElement.contains(e)},t.prototype.preProcessPropertyValue=function(e,t){var i=void 0===t?this.getValue(e):t;if(l.GlobalSettings.allowPreProcessingPropertyValues){for(var n=this;n&&!n.onPreProcessPropertyValue;)n=n.parent;if(n&&n.onPreProcessPropertyValue)return n.onPreProcessPropertyValue(this,e,i)}return i},t.prototype.setParent=function(e){this._parent=e},t.prototype.setShouldFallback=function(e){this._shouldFallback=e},t.prototype.shouldFallback=function(){return this._shouldFallback||!this.requires.areAllMet(this.hostConfig.hostCapabilities)},t.prototype.getRootObject=function(){for(var e=this;e.parent;)e=e.parent;return e},t.prototype.internalValidateProperties=function(e){this.id&&(e.allIds.hasOwnProperty(this.id)?(1===e.allIds[this.id]&&e.addFailure(this,s.ValidationEvent.DuplicateId,a.Strings.errors.duplicateId(this.id)),e.allIds[this.id]+=1):e.allIds[this.id]=1)},t.prototype.validateProperties=function(){var e=new u;return this.internalValidateProperties(e),e},t.prototype.findDOMNodeOwner=function(e){return this.contains(e)?this:void 0},t.prototype.releaseDOMResources=function(){},Object.defineProperty(t.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"renderedElement",{get:function(){return this._renderedElement},enumerable:!1,configurable:!0}),t.typeNameProperty=new c.StringProperty(c.Versions.v1_0,"type",void 0,void 0,void 0,(function(e){return e.getJsonTypeName()})),t.idProperty=new c.StringProperty(c.Versions.v1_0,"id"),t.requiresProperty=new c.SerializableObjectProperty(c.Versions.v1_2,"requires",p.HostCapabilities,!1,new p.HostCapabilities),o([(0,c.property)(t.idProperty)],t.prototype,"id",void 0),o([(0,c.property)(t.requiresProperty)],t.prototype,"requires",null),t}(c.SerializableObject);t.CardObject=d},343:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),o=this&&this.__decorate||function(e,t,i,n){var r,o=arguments.length,s=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,i,n);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,i,s):r(t,i))||s);return o>3&&s&&Object.defineProperty(t,i,s),s},s=this&&this.__spreadArray||function(e,t,i){if(i||2===arguments.length)for(var n,r=0,o=t.length;r<o;r++)!n&&r in t||(n||(n=Array.prototype.slice.call(t,0,r)),n[r]=t[r]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.CarouselEvent=t.Carousel=t.CarouselPage=void 0;var a=i(651),l=i(10),p=i(475),c=i(432),u=i(10),d=i(653),h=i(20),f=i(755),m=i(764),g=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.populateSchema=function(t){e.prototype.populateSchema.call(this,t),t.remove(a.Container.styleProperty),t.remove(a.Container.bleedProperty),t.remove(a.Container.isVisibleProperty)},t.prototype.internalRender=function(){var t=document.createElement("div");t.className=this.hostConfig.makeCssClassName("swiper-slide"),this.rtl=this.isRtl();var i=e.prototype.internalRender.call(this);return f.appendChild(t,i),t},t.prototype.getForbiddenActionTypes=function(){return[a.ShowCardAction,a.ToggleVisibilityAction]},t.prototype.getForbiddenChildElements=function(){return this.forbiddenChildElements()},t.prototype.forbiddenChildElements=function(){return s([a.ToggleVisibilityAction.JsonTypeName,a.ShowCardAction.JsonTypeName,"Media","ActionSet","Input.Text","Input.Date","Input.Time","Input.Number","Input.ChoiceSet","Input.Toggle","Carousel"],e.prototype.forbiddenChildElements.call(this),!0)},t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this.setShouldFallback(!1)},t.prototype.shouldSerialize=function(e){return!0},t.prototype.getJsonTypeName=function(){return"CarouselPage"},Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"hasVisibleSeparator",{get:function(){return!1},enumerable:!1,configurable:!0}),t}(a.Container);t.CarouselPage=g;var y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.carouselLoop=!0,t.carouselOrientation=l.Orientation.Horizontal,t._pages=[],t._currentIndex=0,t._previousEventType=l.CarouselInteractionEvent.Pagination,t}return r(t,e),t.prototype.populateSchema=function(t){e.prototype.populateSchema.call(this,t),t.remove(a.Container.styleProperty),t.remove(a.Container.bleedProperty),t.remove(a.Container.isVisibleProperty)},Object.defineProperty(t.prototype,"timer",{get:function(){var e=this.getValue(t.timerProperty);return e&&e<this.hostConfig.carousel.minAutoplayDelay&&(console.warn(d.Strings.errors.tooLittleTimeDelay),e=this.hostConfig.carousel.minAutoplayDelay),e},set:function(e){e&&e<this.hostConfig.carousel.minAutoplayDelay?(console.warn(d.Strings.errors.tooLittleTimeDelay),this.setValue(t.timerProperty,this.hostConfig.carousel.minAutoplayDelay)):this.setValue(t.timerProperty,e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"initialPageIndex",{get:function(){return this.getValue(t.initialPageProperty)},set:function(e){this.isValidParsedPageIndex(e)?this.setValue(t.initialPageProperty,e):(console.warn(d.Strings.errors.invalidInitialPageIndex(e)),this.setValue(t.initialPageProperty,0))},enumerable:!1,configurable:!0}),t.prototype.isValidParsedPageIndex=function(e){return!!this._pages&&this.isValidPageIndex(e,this._pages.length)},t.prototype.isValidRenderedPageIndex=function(e){return!!this._renderedPages&&this.isValidPageIndex(e,this._renderedPages.length)},t.prototype.isValidPageIndex=function(e,t){return t>0&&0<=e&&e<t},Object.defineProperty(t.prototype,"previousEventType",{get:function(){return this._previousEventType},set:function(e){this._previousEventType=e},enumerable:!1,configurable:!0}),t.prototype.forbiddenChildElements=function(){return s([a.ToggleVisibilityAction.JsonTypeName,a.ShowCardAction.JsonTypeName,"Media","ActionSet","Input.Text","Input.Date","Input.Time","Input.Number","Input.ChoiceSet","Input.Toggle"],e.prototype.forbiddenChildElements.call(this),!0)},t.prototype.adjustRenderedElementSize=function(t){e.prototype.adjustRenderedElementSize.call(this,t),"stretch"==this.height&&void 0!==this._containerForAdorners&&(this._containerForAdorners.style.height="100%"),this.carouselHeight&&(this._carouselPageContainer.style.height=this.carouselHeight+"px")},t.prototype.getJsonTypeName=function(){return"Carousel"},t.prototype.getItemCount=function(){return this._pages.length},t.prototype.getItemAt=function(e){return this._pages[e]},t.prototype.addPage=function(e){this._pages.push(e),e.setParent(this)},t.prototype.removeItem=function(e){if(e instanceof g){var t=this._pages.indexOf(e);if(t>=0)return this._pages.splice(t,1),e.setParent(void 0),this.updateLayout(),!0}return!1},t.prototype.getFirstVisibleRenderedItem=function(){var e;return this.renderedElement&&(null===(e=this._renderedPages)||void 0===e?void 0:e.length)>0?this._renderedPages[0]:void 0},t.prototype.getLastVisibleRenderedItem=function(){var e;return this.renderedElement&&(null===(e=this._renderedPages)||void 0===e?void 0:e.length)>0?this._renderedPages[this._renderedPages.length-1]:void 0},Object.defineProperty(t.prototype,"currentPageId",{get:function(){var e,t;if(null===(t=null===(e=this._carousel)||void 0===e?void 0:e.slides)||void 0===t?void 0:t.length)return this._carousel.slides[this._carousel.activeIndex].id},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"currentPageIndex",{get:function(){var e;return null===(e=this._carousel)||void 0===e?void 0:e.realIndex},enumerable:!1,configurable:!0}),t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this._pages=[];var n=t.pages;if(Array.isArray(n))for(var r=0,o=n;r<o.length;r++){var s=o[r],a=this.createCarouselPageInstance(s,i);a&&this._pages.push(a)}this.validateParsing(i)},t.prototype.validateParsing=function(e){this.isValidParsedPageIndex(this.initialPageIndex)||e.logParseEvent(this,l.ValidationEvent.InvalidPropertyValue,d.Strings.errors.invalidInitialPageIndex(this.initialPageIndex))},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i),i.serializeArray(t,"pages",this._pages)},t.prototype.internalRender=function(){var e;if(this._renderedPages=[],!(this._pages.length<=0)){this.validateOrientationProperties();var t=document.createElement("div");t.className=this.hostConfig.makeCssClassName("ac-carousel-card-level-container");var i=document.createElement("div");i.className=this.hostConfig.makeCssClassName("swiper","ac-carousel"),this._carouselPageContainer=i;var n=document.createElement("div");n.className=this.hostConfig.makeCssClassName("ac-carousel-container"),this._containerForAdorners=n,t.appendChild(n);var r=document.createElement("div");switch(r.className=this.hostConfig.makeCssClassName("swiper-wrapper","ac-carousel-card-container"),r.style.display="flex",this.getEffectiveVerticalContentAlignment()){case l.VerticalAlignment.Top:r.style.alignItems="flex-start";break;case l.VerticalAlignment.Bottom:r.style.alignItems="flex-end";break;default:r.style.alignItems="center"}m.GlobalSettings.useAdvancedCardBottomTruncation&&(r.style.minHeight="-webkit-min-content");var o=document.createElement("div");o.className=this.hostConfig.makeCssClassName("ac-carousel-navigation");var s=document.createElement("div");s.className=this.hostConfig.makeCssClassName("swiper-button-prev");var a=document.createElement("div");a.className=this.hostConfig.makeCssClassName("swiper-button-next"),this.carouselOrientation===l.Orientation.Horizontal?this.updateCssForHorizontalCarousel(s,a):this.updateCssForVerticalCarousel(o,s,a);var p=document.createElement("div");p.className=this.hostConfig.makeCssClassName("swiper-pagination","ac-carousel-pagination"),o.appendChild(s),f.addCancelSelectActionEventHandler(s),o.appendChild(p),f.addCancelSelectActionEventHandler(p),o.appendChild(a),f.addCancelSelectActionEventHandler(a),this.isDesignMode()&&(s.style.zIndex="20",a.style.zIndex="20",p.style.zIndex="20");var c=Math.min(this._pages.length,this.hostConfig.carousel.maxCarouselPages);if(this._pages.length>this.hostConfig.carousel.maxCarouselPages&&console.warn(d.Strings.errors.tooManyCarouselPages),this._pages.length>0)for(var u=0;u<c;u++){var h=this._pages[u],g=this.isElementAllowed(h)?h.render():void 0;null==g||g.classList.add("ac-carousel-page"),null===(e=null==g?void 0:g.children[0])||void 0===e||e.classList.add("ac-carousel-page-container"),g&&(f.appendChild(r,g),this._renderedPages.push(h))}return i.appendChild(r),i.tabIndex=this.isDesignMode()?-1:0,n.appendChild(i),n.appendChild(o),this.rtl=this.isRtl(),this.applyRTL(p),this.isDesignMode()||(this.isValidRenderedPageIndex(this.initialPageIndex)?this._currentIndex=this.initialPageIndex:(console.warn(d.Strings.errors.invalidInitialPageIndex(this.initialPageIndex)),this._currentIndex=0)),this.initializeCarouselControl(i,a,s,p,this.rtl),this._renderedPages.length>0?t:void 0}},t.prototype.applyRTL=function(t){e.prototype.applyRTL.call(this,this._carouselPageContainer),this.rtl&&t.classList.add(this.hostConfig.makeCssClassName("ac-carousel-pagination-rtl"))},t.prototype.validateOrientationProperties=function(){this.carouselHeight||(this.carouselOrientation=l.Orientation.Horizontal)},t.prototype.updateCssForHorizontalCarousel=function(e,t){e.classList.add(this.hostConfig.makeCssClassName("ac-carousel-left")),t.classList.add(this.hostConfig.makeCssClassName("ac-carousel-right"))},t.prototype.updateCssForVerticalCarousel=function(e,t,i){this._containerForAdorners.classList.add(this.hostConfig.makeCssClassName("ac-carousel-container-vertical")),e.classList.add(this.hostConfig.makeCssClassName("ac-carousel-navigation-vertical")),t.classList.add(this.hostConfig.makeCssClassName("ac-carousel-up")),i.classList.add(this.hostConfig.makeCssClassName("ac-carousel-down"))},t.prototype.initializeCarouselControl=function(e,t,i,n,r){var o,s,a,p=this,c=void 0!==r&&r?i:t,u=void 0!==r&&r?t:i,f=l.Orientation.Horizontal===this.carouselOrientation?u:i,m=l.Orientation.Horizontal===this.carouselOrientation?c:t,g={loop:!this.isDesignMode()&&this.carouselLoop,modules:[h.Navigation,h.Pagination,h.Scrollbar,h.A11y,h.History,h.Keyboard],pagination:{el:n,clickable:!0},navigation:{prevEl:f,nextEl:m},a11y:{enabled:!0},keyboard:{enabled:!1,onlyInViewport:!0},direction:this.carouselOrientation===l.Orientation.Horizontal?"horizontal":"vertical",resizeObserver:!1,initialSlide:this._currentIndex};this.timer&&!this.isDesignMode()&&(null===(o=g.modules)||void 0===o||o.push(h.Autoplay),g.autoplay={delay:this.timer,pauseOnMouseEnter:!0});var y=new h.Swiper(e,g);e.addEventListener("mouseenter",(function(e){var t;null===(t=y.autoplay)||void 0===t||t.stop()})),e.addEventListener("mouseleave",(function(e){var t;null===(t=y.autoplay)||void 0===t||t.start()})),y.on("navigationNext",(function(e){p.raiseCarouselEvent(l.CarouselInteractionEvent.NavigationNext)})),y.on("navigationPrev",(function(e){p.raiseCarouselEvent(l.CarouselInteractionEvent.NavigationPrevious)})),y.on("slideChangeTransitionEnd",(function(e){p.currentIndex=e.realIndex,p.raiseCarouselEvent(l.CarouselInteractionEvent.Pagination)})),y.on("autoplay",(function(){p.raiseCarouselEvent(l.CarouselInteractionEvent.Autoplay)})),y.on("paginationRender",(function(e,t){e.pagination.bullets.forEach((function(t,i){t.addEventListener("keypress",(function(t){"Enter"==t.key&&(t.preventDefault(),e.slideTo(i+1))}))}))})),y.on("destroy",(function(){p.destroyResizeObserver()})),i.title=null!==(s=i.ariaLabel)&&void 0!==s?s:d.Strings.defaults.carouselNavigationPreviousTooltip(),t.title=null!==(a=t.ariaLabel)&&void 0!==a?a:d.Strings.defaults.carouselNavigationNextTooltip(),this._carousel=y,this.createResizeObserver()},t.prototype.createCarouselPageInstance=function(e,t){return t.parseCardObject(this,e,this.forbiddenChildElements(),!this.isDesignMode(),(function(e){return e&&"CarouselPage"!==e?void 0:new g}),(function(e,i){t.logParseEvent(void 0,u.ValidationEvent.ElementTypeNotAllowed,d.Strings.errors.elementTypeNotAllowed(e))}))},t.prototype.slideTo=function(e){var t;null===(t=this._carousel)||void 0===t||t.slideTo(e)},Object.defineProperty(t.prototype,"carouselPageContainer",{get:function(){return this._carouselPageContainer},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"currentIndex",{get:function(){return this._currentIndex},set:function(e){this._currentIndex=e},enumerable:!1,configurable:!0}),t.prototype.createCarouselEvent=function(e){var t;return null!=this.currentPageIndex&&(t=this.getItemAt(this.currentPageIndex).id),new v(e,this.id,t,this.currentPageIndex)},t.prototype.raiseCarouselEvent=function(e){var t=this.parent?this.parent.getRootElement():void 0,i=t&&t.onCarouselEvent?t.onCarouselEvent:a.AdaptiveCard.onCarouselEvent;i&&e==l.CarouselInteractionEvent.Pagination&&i(this.createCarouselEvent(this.previousEventType)),this.previousEventType=e},t.prototype.createResizeObserver=function(){var e,t=this;this.checkIfCarouselInValidStateForResizeEvent()&&(this._observer=new ResizeObserver((function(e){var i,n,r,o,s=null===(i=t._carousel)||void 0===i?void 0:i.width,a=null===(n=t._carousel)||void 0===n?void 0:n.height,l=s,p=a;e.forEach((function(e){var i,n=e.contentBoxSize,r=e.contentRect,o=e.target;o&&o!==(null===(i=t._carousel)||void 0===i?void 0:i.el)||(l=r?r.width:(n[0]||n).inlineSize,p=r?r.height:(n[0]||n).blockSize)})),l===s&&p===a||t.checkIfCarouselInValidStateForResizeEvent()&&(null===(r=t._carousel)||void 0===r||r.emit("beforeResize"),null===(o=t._carousel)||void 0===o||o.emit("resize"))})),this._observer.observe(null===(e=this._carousel)||void 0===e?void 0:e.el))},t.prototype.destroyResizeObserver=function(){var e;this._observer&&this._observer.unobserve&&(null===(e=this._carousel)||void 0===e?void 0:e.el)&&(this._observer.unobserve(this._carousel.el),this._observer=null)},t.prototype.checkIfCarouselInValidStateForResizeEvent=function(){return this._carousel&&!this._carousel.destroyed},t.timerProperty=new p.NumProperty(p.Versions.v1_6,"timer",void 0),t.initialPageProperty=new p.NumProperty(p.Versions.v1_6,"initialPage",0),t.loopProperty=new p.BoolProperty(p.Versions.v1_6,"loop",!0),t.orientationProperty=new p.EnumProperty(p.Versions.v1_6,"orientation",l.Orientation,l.Orientation.Horizontal),t.carouselHeightProperty=new p.PixelSizeProperty(p.Versions.v1_6,"heightInPixels"),o([(0,p.property)(t.timerProperty)],t.prototype,"timer",null),o([(0,p.property)(t.initialPageProperty)],t.prototype,"initialPageIndex",null),o([(0,p.property)(t.loopProperty)],t.prototype,"carouselLoop",void 0),o([(0,p.property)(t.orientationProperty)],t.prototype,"carouselOrientation",void 0),o([(0,p.property)(t.carouselHeightProperty)],t.prototype,"carouselHeight",void 0),t}(a.Container);t.Carousel=y;var v=function(e,t,i,n){this.type=e,this.carouselId=t,this.activeCarouselPageId=i,this.activeCarouselPageIndex=n};t.CarouselEvent=v,c.GlobalRegistry.defaultElements.register("Carousel",y,p.Versions.v1_6)},213:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ChannelAdapter=void 0;t.ChannelAdapter=function(){}},846:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Collection=void 0;var i=function(){function e(){this._items=[]}return e.prototype.get=function(e){return this._items[e]},e.prototype.add=function(e){this._items.push(e),this.onItemAdded&&this.onItemAdded(e)},e.prototype.remove=function(e){var t=this._items.indexOf(e);t>=0&&(this._items=this._items.splice(t,1),this.onItemRemoved&&this.onItemRemoved(e))},e.prototype.indexOf=function(e){return this._items.indexOf(e)},Object.defineProperty(e.prototype,"length",{get:function(){return this._items.length},enumerable:!1,configurable:!0}),e}();t.Collection=i},994:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Constants=void 0;var i=function(){function e(){}return e.keys={tab:"Tab",enter:"Enter",escape:"Escape",space:" ",up:"ArrowUp",down:"ArrowDown",delete:"Delete"},e}();t.Constants=i},441:function(e,t,i){var n=this&&this.__createBinding||(Object.create?function(e,t,i,n){void 0===n&&(n=i),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[i]}})}:function(e,t,i,n){void 0===n&&(n=i),e[n]=t[i]}),r=this&&this.__exportStar||function(e,t){for(var i in e)"default"===i||Object.prototype.hasOwnProperty.call(t,i)||n(t,e,i)};Object.defineProperty(t,"__esModule",{value:!0}),r(i(57),t),r(i(467),t)},57:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MenuItem=void 0;var n=i(699),r=i(994),o=function(){function e(e,t){this._isEnabled=!0,this.key=e,this._value=t}return e.prototype.click=function(){this.isEnabled&&this.onClick&&this.onClick(this)},e.prototype.updateCssClasses=function(){if(this._element){var e=this._hostConfig?this._hostConfig:n.defaultHostConfig;this._element.className=e.makeCssClassName("ac-ctrl"),this._element.classList.add(e.makeCssClassName(this.isEnabled?"ac-ctrl-dropdown-item":"ac-ctrl-dropdown-item-disabled")),this.isEnabled||this._element.classList.add(e.makeCssClassName("ac-disabled"))}},e.prototype.toString=function(){return this.value},e.prototype.render=function(e){var t=this;return this._hostConfig=e,this._element||(this._element=document.createElement("span"),this._element.innerText=this.value,this._element.setAttribute("role","menuitem"),this.isEnabled||this._element.setAttribute("aria-disabled","true"),this._element.setAttribute("aria-current","false"),this._element.onmouseup=function(e){t.click()},this._element.onkeydown=function(e){e.key===r.Constants.keys.enter&&(e.stopPropagation(),e.preventDefault(),t.click())},this.updateCssClasses()),this._element},Object.defineProperty(e.prototype,"value",{get:function(){return this._value},set:function(e){this._value=e,this._element&&(this._element.innerText=e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isEnabled",{get:function(){return this._isEnabled},set:function(e){this._isEnabled!==e&&(this._isEnabled=e,this.updateCssClasses())},enumerable:!1,configurable:!0}),e}();t.MenuItem=o},866:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PopupControl=void 0;var n=i(994),r=i(755),o=i(699),s=function(){function e(){this._isOpen=!1}return e.prototype.keyDown=function(e){e.key===n.Constants.keys.escape&&this.closePopup(!0)},e.prototype.render=function(e){var t=this,i=document.createElement("div");return i.tabIndex=0,i.className=this.hostConfig.makeCssClassName("ac-ctrl","ac-ctrl-popup-container"),i.setAttribute("role","dialog"),i.setAttribute("aria-modal","true"),i.onkeydown=function(e){return t.keyDown(e),!e.cancelBubble},i.appendChild(this.renderContent()),i},e.prototype.focus=function(){this._popupElement&&this._popupElement.firstElementChild.focus()},e.prototype.popup=function(e){var t,i,n,o,s,a=this;if(!this._isOpen){this._overlayElement=document.createElement("div"),this._overlayElement.className=this.hostConfig.makeCssClassName("ac-ctrl-overlay"),this._overlayElement.tabIndex=0,this._overlayElement.style.width=document.documentElement.scrollWidth+"px",this._overlayElement.style.height=document.documentElement.scrollHeight+"px",this._overlayElement.onfocus=function(e){a.closePopup(!0)},document.body.appendChild(this._overlayElement);var l=e.getBoundingClientRect();this._popupElement=this.render(l),(t=this._popupElement.classList).remove.apply(t,this.hostConfig.makeCssClassNames("ac-ctrl-slide","ac-ctrl-slideLeftToRight","ac-ctrl-slideRightToLeft","ac-ctrl-slideTopToBottom","ac-ctrl-slideRightToLeft")),window.addEventListener("resize",(function(e){a.closePopup(!0)}));var p=e.getAttribute("aria-label");p&&this._popupElement.setAttribute("aria-label",p),this._overlayElement.appendChild(this._popupElement);var c,u=this._popupElement.getBoundingClientRect(),d=window.innerHeight-l.bottom,h=l.top,f=window.innerWidth-l.right,m=l.left,g=l.left+r.getScrollX();if(h<u.height&&d<u.height){var y=Math.min(u.height,window.innerHeight);if(this._popupElement.style.maxHeight=y+"px",c=y<u.height?r.getScrollY():r.getScrollY()+l.top+(l.height-y)/2,m<u.width&&f<u.width){var v=Math.min(u.width,window.innerWidth);this._popupElement.style.maxWidth=v+"px",g=v<u.width?r.getScrollX():r.getScrollX()+l.left+(l.width-v)/2}else f>=u.width?(g=r.getScrollX()+l.right,(i=this._popupElement.classList).add.apply(i,this.hostConfig.makeCssClassNames("ac-ctrl-slide","ac-ctrl-slideLeftToRight"))):(g=r.getScrollX()+l.left-u.width,(n=this._popupElement.classList).add.apply(n,this.hostConfig.makeCssClassNames("ac-ctrl-slide","ac-ctrl-slideRightToLeft")))}else d>=u.height?(c=r.getScrollY()+l.bottom,(o=this._popupElement.classList).add.apply(o,this.hostConfig.makeCssClassNames("ac-ctrl-slide","ac-ctrl-slideTopToBottom"))):(c=r.getScrollY()+l.top-u.height,(s=this._popupElement.classList).add.apply(s,this.hostConfig.makeCssClassNames("ac-ctrl-slide","ac-ctrl-slideBottomToTop"))),f<u.width&&(g=r.getScrollX()+l.right-u.width);this._popupElement.style.left=g+"px",this._popupElement.style.top=c+"px",this._popupElement.focus(),this._isOpen=!0}},e.prototype.closePopup=function(e){this._isOpen&&(document.body.removeChild(this._overlayElement),this._isOpen=!1,this.onClose&&this.onClose(this,e))},Object.defineProperty(e.prototype,"hostConfig",{get:function(){return this._hostConfig?this._hostConfig:o.defaultHostConfig},set:function(e){this._hostConfig=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return this._isOpen},enumerable:!1,configurable:!0}),e}();t.PopupControl=s},467:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)});Object.defineProperty(t,"__esModule",{value:!0}),t.PopupMenu=void 0;var o=i(994),s=i(846),a=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._items=new s.Collection,t._renderedItems=[],t._selectedIndex=-1,t}return r(t,e),t.prototype.renderContent=function(){var e=document.createElement("div");e.className=this.hostConfig.makeCssClassName("ac-ctrl ac-popup"),e.setAttribute("role","listbox");for(var t=0;t<this._items.length;t++){var i=this._items.get(t).render(this.hostConfig);i.tabIndex=0,e.appendChild(i),0==t&&i.setAttribute("aria-expanded","true"),t===this.selectedIndex&&i.focus(),this._renderedItems.push(i)}return e},t.prototype.keyDown=function(t){e.prototype.keyDown.call(this,t);var i=this._selectedIndex;switch(t.key){case o.Constants.keys.tab:this.closePopup(!0);break;case o.Constants.keys.up:(i<=0||--i<0)&&(i=this._renderedItems.length-1),this.selectedIndex=i,this.removeAriaExpanded(i),t.cancelBubble=!0;break;case o.Constants.keys.down:i<0?i=0:(++i>=this._renderedItems.length&&(i=0),this.removeAriaExpanded(i)),this.selectedIndex=i,t.cancelBubble=!0}},Object.defineProperty(t.prototype,"items",{get:function(){return this._items},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectedIndex",{get:function(){return this._selectedIndex},set:function(e){e>=0&&e<this._renderedItems.length&&(this._renderedItems[e].focus(),this._selectedIndex=e)},enumerable:!1,configurable:!0}),t.prototype.removeAriaExpanded=function(e){"true"===this._renderedItems[e].getAttribute("aria-expanded")&&this._renderedItems[e].removeAttribute("aria-expanded")},t}(i(866).PopupControl);t.PopupMenu=a},10:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ActionRole=t.CarouselInteractionEvent=t.LogLevel=t.RefreshMode=t.TypeErrorType=t.ContainerFitStatus=t.ValidationEvent=t.ValidationPhase=t.InputLabelPosition=t.InputTextStyle=t.ActionIconPlacement=t.FillMode=t.Orientation=t.ShowCardActionMode=t.ImageStyle=t.ActionAlignment=t.VerticalAlignment=t.HorizontalAlignment=t.TextColor=t.Spacing=t.FontType=t.TextWeight=t.InputStyle=t.TextSize=t.SizeUnit=t.ImageSetPresentationStyle=t.ImageSize=t.Size=t.ActionMode=t.ActionStyle=t.ContainerStyle=void 0;var i=function(){function e(){}return e.Default="default",e.Emphasis="emphasis",e.Accent="accent",e.Good="good",e.Attention="attention",e.Warning="warning",e}();t.ContainerStyle=i;var n=function(){function e(){}return e.Default="default",e.Positive="positive",e.Destructive="destructive",e}();t.ActionStyle=n;var r,o,s,a,l,p,c,u,d,h,f,m,g,y,v,b,C,w,S,E,x,P,_,A,T,I,O,M,V=function(){function e(){}return e.Primary="primary",e.Secondary="secondary",e}();t.ActionMode=V,(M=t.Size||(t.Size={}))[M.Auto=0]="Auto",M[M.Stretch=1]="Stretch",M[M.Small=2]="Small",M[M.Medium=3]="Medium",M[M.Large=4]="Large",(O=t.ImageSize||(t.ImageSize={}))[O.Small=0]="Small",O[O.Medium=1]="Medium",O[O.Large=2]="Large",(I=t.ImageSetPresentationStyle||(t.ImageSetPresentationStyle={}))[I.Default=0]="Default",I[I.Stacked=1]="Stacked",(T=t.SizeUnit||(t.SizeUnit={}))[T.Weight=0]="Weight",T[T.Pixel=1]="Pixel",(A=t.TextSize||(t.TextSize={}))[A.Small=0]="Small",A[A.Default=1]="Default",A[A.Medium=2]="Medium",A[A.Large=3]="Large",A[A.ExtraLarge=4]="ExtraLarge",(_=t.InputStyle||(t.InputStyle={}))[_.RevealOnHover=0]="RevealOnHover",_[_.Default=1]="Default",(P=t.TextWeight||(t.TextWeight={}))[P.Lighter=0]="Lighter",P[P.Default=1]="Default",P[P.Bolder=2]="Bolder",(x=t.FontType||(t.FontType={}))[x.Default=0]="Default",x[x.Monospace=1]="Monospace",(E=t.Spacing||(t.Spacing={}))[E.None=0]="None",E[E.Small=1]="Small",E[E.Default=2]="Default",E[E.Medium=3]="Medium",E[E.Large=4]="Large",E[E.ExtraLarge=5]="ExtraLarge",E[E.Padding=6]="Padding",(S=t.TextColor||(t.TextColor={}))[S.Default=0]="Default",S[S.Dark=1]="Dark",S[S.Light=2]="Light",S[S.Accent=3]="Accent",S[S.Good=4]="Good",S[S.Warning=5]="Warning",S[S.Attention=6]="Attention",(w=t.HorizontalAlignment||(t.HorizontalAlignment={}))[w.Left=0]="Left",w[w.Center=1]="Center",w[w.Right=2]="Right",(C=t.VerticalAlignment||(t.VerticalAlignment={}))[C.Top=0]="Top",C[C.Center=1]="Center",C[C.Bottom=2]="Bottom",(b=t.ActionAlignment||(t.ActionAlignment={}))[b.Left=0]="Left",b[b.Center=1]="Center",b[b.Right=2]="Right",b[b.Stretch=3]="Stretch",(v=t.ImageStyle||(t.ImageStyle={}))[v.Default=0]="Default",v[v.Person=1]="Person",(y=t.ShowCardActionMode||(t.ShowCardActionMode={}))[y.Inline=0]="Inline",y[y.Popup=1]="Popup",(g=t.Orientation||(t.Orientation={}))[g.Horizontal=0]="Horizontal",g[g.Vertical=1]="Vertical",(m=t.FillMode||(t.FillMode={}))[m.Cover=0]="Cover",m[m.RepeatHorizontally=1]="RepeatHorizontally",m[m.RepeatVertically=2]="RepeatVertically",m[m.Repeat=3]="Repeat",(f=t.ActionIconPlacement||(t.ActionIconPlacement={}))[f.LeftOfTitle=0]="LeftOfTitle",f[f.AboveTitle=1]="AboveTitle",(h=t.InputTextStyle||(t.InputTextStyle={}))[h.Text=0]="Text",h[h.Tel=1]="Tel",h[h.Url=2]="Url",h[h.Email=3]="Email",h[h.Password=4]="Password",(d=t.InputLabelPosition||(t.InputLabelPosition={}))[d.Inline=0]="Inline",d[d.Above=1]="Above",(u=t.ValidationPhase||(t.ValidationPhase={}))[u.Parse=0]="Parse",u[u.ToJSON=1]="ToJSON",u[u.Validation=2]="Validation",(c=t.ValidationEvent||(t.ValidationEvent={}))[c.Hint=0]="Hint",c[c.ActionTypeNotAllowed=1]="ActionTypeNotAllowed",c[c.CollectionCantBeEmpty=2]="CollectionCantBeEmpty",c[c.Deprecated=3]="Deprecated",c[c.ElementTypeNotAllowed=4]="ElementTypeNotAllowed",c[c.InteractivityNotAllowed=5]="InteractivityNotAllowed",c[c.InvalidPropertyValue=6]="InvalidPropertyValue",c[c.MissingCardType=7]="MissingCardType",c[c.PropertyCantBeNull=8]="PropertyCantBeNull",c[c.TooManyActions=9]="TooManyActions",c[c.UnknownActionType=10]="UnknownActionType",c[c.UnknownElementType=11]="UnknownElementType",c[c.UnsupportedCardVersion=12]="UnsupportedCardVersion",c[c.DuplicateId=13]="DuplicateId",c[c.UnsupportedProperty=14]="UnsupportedProperty",c[c.RequiredInputsShouldHaveLabel=15]="RequiredInputsShouldHaveLabel",c[c.RequiredInputsShouldHaveErrorMessage=16]="RequiredInputsShouldHaveErrorMessage",c[c.Other=17]="Other",(p=t.ContainerFitStatus||(t.ContainerFitStatus={}))[p.FullyInContainer=0]="FullyInContainer",p[p.Overflowing=1]="Overflowing",p[p.FullyOutOfContainer=2]="FullyOutOfContainer",(l=t.TypeErrorType||(t.TypeErrorType={}))[l.UnknownType=0]="UnknownType",l[l.ForbiddenType=1]="ForbiddenType",(a=t.RefreshMode||(t.RefreshMode={}))[a.Disabled=0]="Disabled",a[a.Manual=1]="Manual",a[a.Automatic=2]="Automatic",(s=t.LogLevel||(t.LogLevel={}))[s.Info=0]="Info",s[s.Warning=1]="Warning",s[s.Error=2]="Error",(o=t.CarouselInteractionEvent||(t.CarouselInteractionEvent={}))[o.NavigationNext=0]="NavigationNext",o[o.NavigationPrevious=1]="NavigationPrevious",o[o.Pagination=2]="Pagination",o[o.Autoplay=3]="Autoplay",(r=t.ActionRole||(t.ActionRole={}))[r.Button=0]="Button",r[r.Link=1]="Link",r[r.Tab=2]="Tab",r[r.Menu=3]="Menu",r[r.MenuItem=4]="MenuItem"},233:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)});Object.defineProperty(t,"__esModule",{value:!0}),t.HostCapabilities=void 0;var o=i(475),s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._capabilities={},t}return r(t,e),t.prototype.getSchemaKey=function(){return"HostCapabilities"},t.prototype.internalParse=function(t,i){if(e.prototype.internalParse.call(this,t,i),t)for(var n in t){var r=t[n];if("string"==typeof r)if("*"===r)this.addCapability(n,"*");else{var s=o.Version.parse(r,i);(null==s?void 0:s.isValid)&&this.addCapability(n,s)}}},t.prototype.internalToJSON=function(t,i){for(var n in e.prototype.internalToJSON.call(this,t,i),this._capabilities)t[n]=this._capabilities[n]},t.prototype.addCapability=function(e,t){this._capabilities[e]=t},t.prototype.removeCapability=function(e){delete this._capabilities[e]},t.prototype.clear=function(){this._capabilities={}},t.prototype.hasCapability=function(e,t){return!!this._capabilities.hasOwnProperty(e)&&("*"===t||"*"===this._capabilities[e]||t.compareTo(this._capabilities[e])<=0)},t.prototype.areAllMet=function(e){for(var t in this._capabilities)if(!e.hasCapability(t,this._capabilities[t]))return!1;return!0},t}(o.SerializableObject);t.HostCapabilities=s},699:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)});Object.defineProperty(t,"__esModule",{value:!0}),t.defaultHostConfig=t.HostConfig=t.CarouselConfig=t.FontTypeSet=t.FontTypeDefinition=t.ContainerStyleSet=t.ContainerStyleDefinition=t.ColorSetDefinition=t.ActionsConfig=t.ShowCardActionConfig=t.FactSetConfig=t.FactTitleDefinition=t.FactTextDefinition=t.InputConfig=t.InputLabelConfig=t.RequiredInputLabelTextDefinition=t.TextBlockConfig=t.TextStyleSet=t.TextStyleDefinition=t.BaseTextDefinition=t.TableConfig=t.MediaConfig=t.ImageSetConfig=t.AdaptiveCardConfig=t.TextColorDefinition=t.ColorDefinition=void 0;var o=i(10),s=i(755),a=i(764),l=i(233);function p(e,t,i){if("string"==typeof t){var n=s.parseEnum(e,t,i);return void 0!==n?n:i}return"number"==typeof t?t:i}var c=function(){function e(e,t){this.default="#000000",this.subtle="#666666",e&&(this.default=e),t&&(this.subtle=t)}return e.prototype.parse=function(e){e&&(this.default=e.default||this.default,this.subtle=e.subtle||this.subtle)},e}();t.ColorDefinition=c;var u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.highlightColors=new c("#22000000","#11000000"),t}return r(t,e),t.prototype.parse=function(t){e.prototype.parse.call(this,t),t&&this.highlightColors.parse(t.highlightColors)},t}(c);t.TextColorDefinition=u;var d=function(e){this.allowCustomStyle=!1,e&&(this.allowCustomStyle=e.allowCustomStyle||this.allowCustomStyle)};t.AdaptiveCardConfig=d;var h=function(){function e(e){this.imageSize=o.Size.Medium,this.maxImageHeight=100,e&&(this.imageSize=null!=e.imageSize?e.imageSize:this.imageSize,this.maxImageHeight=s.parseNumber(e.maxImageHeight,100))}return e.prototype.toJSON=function(){return{imageSize:o.Size[this.imageSize],maxImageHeight:this.maxImageHeight}},e}();t.ImageSetConfig=h;var f=function(){function e(e){this.allowInlinePlayback=!0,e&&(this.defaultPoster=e.defaultPoster,this.allowInlinePlayback=e.allowInlinePlayback||this.allowInlinePlayback)}return e.prototype.toJSON=function(){return{defaultPoster:this.defaultPoster,allowInlinePlayback:this.allowInlinePlayback}},e}();t.MediaConfig=f;var m=function(){function e(e){this.cellSpacing=8,e&&(this.cellSpacing=e.cellSpacing&&"number"==typeof e.cellSpacing?e.cellSpacing:this.cellSpacing)}return e.prototype.toJSON=function(){return{cellSpacing:this.cellSpacing}},e}();t.TableConfig=m;var g=function(){function e(e){this.size=o.TextSize.Default,this.color=o.TextColor.Default,this.isSubtle=!1,this.weight=o.TextWeight.Default,this.parse(e)}return e.prototype.parse=function(e){e&&(this.size=p(o.TextSize,e.size,this.size),this.color=p(o.TextColor,e.color,this.color),this.isSubtle=void 0!==e.isSubtle&&"boolean"==typeof e.isSubtle?e.isSubtle:this.isSubtle,this.weight=p(o.TextWeight,e.weight,this.getDefaultWeight()))},e.prototype.getDefaultWeight=function(){return o.TextWeight.Default},e.prototype.toJSON=function(){return{size:o.TextSize[this.size],color:o.TextColor[this.color],isSubtle:this.isSubtle,weight:o.TextWeight[this.weight]}},e}();t.BaseTextDefinition=g;var y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.fontType=o.FontType.Default,t}return r(t,e),t.prototype.parse=function(t){e.prototype.parse.call(this,t),t&&(this.fontType=p(o.FontType,t.fontType,this.fontType))},t}(g);t.TextStyleDefinition=y;var v=function(){function e(e){this.default=new y,this.heading=new y({size:"Large",weight:"Bolder"}),this.columnHeader=new y({weight:"Bolder"}),e&&(this.heading.parse(e.heading),this.columnHeader.parse(e.columnHeader))}return e.prototype.getStyleByName=function(e){switch(e.toLowerCase()){case"heading":return this.heading;case"columnHeader":return this.columnHeader;default:return this.default}},e}();t.TextStyleSet=v;var b=function(e){e&&(this.headingLevel=s.parseNumber(e.headingLevel))};t.TextBlockConfig=b;var C=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.suffix=" *",t.suffixColor=o.TextColor.Attention,t}return r(t,e),t.prototype.parse=function(t){e.prototype.parse.call(this,t),t&&(this.suffix=t.suffix||this.suffix,this.suffixColor=p(o.TextColor,t.suffixColor,this.suffixColor))},t.prototype.toJSON=function(){var t=e.prototype.toJSON.call(this);return t.suffix=this.suffix,t.suffixColor=o.TextColor[this.suffixColor],t},t}(g);t.RequiredInputLabelTextDefinition=C;var w=function(e){this.inputSpacing=o.Spacing.Small,this.width=30,this.requiredInputs=new C,this.optionalInputs=new g,e&&(this.inputSpacing=p(o.Spacing,e.inputSpacing,this.inputSpacing),this.requiredInputs=new C(e.requiredInputs),this.optionalInputs=new g(e.optionalInputs),this.width=null!=e.width?e.width:this.width)};t.InputLabelConfig=w;var S=function(e){this.label=new w,this.errorMessage=new g({color:o.TextColor.Attention}),this.debounceTimeInMilliSeconds=0,this.allowDynamicallyFilteredChoiceSet=!0,this.allowRevealOnHoverStyle=!1,e&&(this.label=new w(e.label),this.errorMessage=new g(e.errorMessage),this.allowRevealOnHoverStyle=e.allowRevealOnHoverStyle||this.allowRevealOnHoverStyle,this.allowDynamicallyFilteredChoiceSet=e.allowDynamicallyFilteredChoiceSet||this.allowDynamicallyFilteredChoiceSet,this.debounceTimeInMilliSeconds=e.debounceTimeInMilliSeconds||this.debounceTimeInMilliSeconds)};t.InputConfig=S;var E=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.wrap=!0,t}return r(t,e),t.prototype.parse=function(t){e.prototype.parse.call(this,t),t&&(this.wrap=null!=t.wrap?t.wrap:this.wrap)},t.prototype.toJSON=function(){var t=e.prototype.toJSON.call(this);return t.wrap=this.wrap,t},t}(g);t.FactTextDefinition=E;var x=function(e){function t(t){var i=e.call(this,t)||this;return i.maxWidth=150,i.weight=o.TextWeight.Bolder,t&&(i.maxWidth=null!=t.maxWidth?t.maxWidth:i.maxWidth,i.weight=p(o.TextWeight,t.weight,o.TextWeight.Bolder)),i}return r(t,e),t.prototype.getDefaultWeight=function(){return o.TextWeight.Bolder},t}(E);t.FactTitleDefinition=x;var P=function(e){this.title=new x,this.value=new E,this.spacing=10,e&&(this.title=new x(e.title),this.value=new E(e.value),this.spacing=e.spacing&&null!=e.spacing?e.spacing&&e.spacing:this.spacing)};t.FactSetConfig=P;var _=function(){function e(e){this.actionMode=o.ShowCardActionMode.Inline,this.inlineTopMargin=16,this.style=o.ContainerStyle.Emphasis,e&&(this.actionMode=p(o.ShowCardActionMode,e.actionMode,o.ShowCardActionMode.Inline),this.inlineTopMargin=null!=e.inlineTopMargin?e.inlineTopMargin:this.inlineTopMargin,this.style=e.style&&"string"==typeof e.style?e.style:o.ContainerStyle.Emphasis)}return e.prototype.toJSON=function(){return{actionMode:o.ShowCardActionMode[this.actionMode],inlineTopMargin:this.inlineTopMargin,style:this.style}},e}();t.ShowCardActionConfig=_;var A=function(){function e(e){if(this.maxActions=5,this.spacing=o.Spacing.Default,this.buttonSpacing=20,this.showCard=new _,this.preExpandSingleShowCardAction=!1,this.actionsOrientation=o.Orientation.Horizontal,this.actionAlignment=o.ActionAlignment.Left,this.iconPlacement=o.ActionIconPlacement.LeftOfTitle,this.allowTitleToWrap=!1,this.iconSize=16,e){this.maxActions=null!=e.maxActions?e.maxActions:this.maxActions,this.spacing=p(o.Spacing,e.spacing&&e.spacing,o.Spacing.Default),this.buttonSpacing=null!=e.buttonSpacing?e.buttonSpacing:this.buttonSpacing,this.showCard=new _(e.showCard),this.preExpandSingleShowCardAction=s.parseBool(e.preExpandSingleShowCardAction,!1),this.actionsOrientation=p(o.Orientation,e.actionsOrientation,o.Orientation.Horizontal),this.actionAlignment=p(o.ActionAlignment,e.actionAlignment,o.ActionAlignment.Left),this.iconPlacement=p(o.ActionIconPlacement,e.iconPlacement,o.ActionIconPlacement.LeftOfTitle),this.allowTitleToWrap=null!=e.allowTitleToWrap?e.allowTitleToWrap:this.allowTitleToWrap;try{var t=a.SizeAndUnit.parse(e.iconSize);t.unit===o.SizeUnit.Pixel&&(this.iconSize=t.physicalSize)}catch(e){}}}return e.prototype.toJSON=function(){return{maxActions:this.maxActions,spacing:o.Spacing[this.spacing],buttonSpacing:this.buttonSpacing,showCard:this.showCard,preExpandSingleShowCardAction:this.preExpandSingleShowCardAction,actionsOrientation:o.Orientation[this.actionsOrientation],actionAlignment:o.ActionAlignment[this.actionAlignment]}},e}();t.ActionsConfig=A;var T=function(){function e(e){this.default=new u,this.dark=new u,this.light=new u,this.accent=new u,this.good=new u,this.warning=new u,this.attention=new u,this.parse(e)}return e.prototype.parseSingleColor=function(e,t){e&&this[t].parse(e[t])},e.prototype.parse=function(e){e&&(this.parseSingleColor(e,"default"),this.parseSingleColor(e,"dark"),this.parseSingleColor(e,"light"),this.parseSingleColor(e,"accent"),this.parseSingleColor(e,"good"),this.parseSingleColor(e,"warning"),this.parseSingleColor(e,"attention"))},e}();t.ColorSetDefinition=T;var I=function(){function e(e){this.foregroundColors=new T({default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#E69500",subtle:"#DDE69500"},attention:{default:"#CC3300",subtle:"#DDCC3300"}}),this.parse(e)}return e.prototype.parse=function(e){e&&(this.backgroundColor=e.backgroundColor,this.foregroundColors.parse(e.foregroundColors),this.highlightBackgroundColor=e.highlightBackgroundColor,this.highlightForegroundColor=e.highlightForegroundColor,this.borderColor=e.borderColor)},Object.defineProperty(e.prototype,"isBuiltIn",{get:function(){return!1},enumerable:!1,configurable:!0}),e}();t.ContainerStyleDefinition=I;var O=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),Object.defineProperty(t.prototype,"isBuiltIn",{get:function(){return!0},enumerable:!1,configurable:!0}),t}(I),M=function(){function e(e){if(this._allStyles={},this._allStyles[o.ContainerStyle.Default]=new O,this._allStyles[o.ContainerStyle.Emphasis]=new O,this._allStyles[o.ContainerStyle.Accent]=new O,this._allStyles[o.ContainerStyle.Good]=new O,this._allStyles[o.ContainerStyle.Attention]=new O,this._allStyles[o.ContainerStyle.Warning]=new O,e){this._allStyles[o.ContainerStyle.Default].parse(e[o.ContainerStyle.Default]),this._allStyles[o.ContainerStyle.Emphasis].parse(e[o.ContainerStyle.Emphasis]),this._allStyles[o.ContainerStyle.Accent].parse(e[o.ContainerStyle.Accent]),this._allStyles[o.ContainerStyle.Good].parse(e[o.ContainerStyle.Good]),this._allStyles[o.ContainerStyle.Attention].parse(e[o.ContainerStyle.Attention]),this._allStyles[o.ContainerStyle.Warning].parse(e[o.ContainerStyle.Warning]);var t=e.customStyles;if(t&&Array.isArray(t))for(var i=0,n=t;i<n.length;i++){var r=n[i];if(r){var s=r.name;s&&"string"==typeof s&&(this._allStyles.hasOwnProperty(s)?this._allStyles[s].parse(r.style):this._allStyles[s]=new I(r.style))}}}}return e.prototype.toJSON=function(){var e=this,t=[];Object.keys(this._allStyles).forEach((function(i){e._allStyles[i].isBuiltIn||t.push({name:i,style:e._allStyles[i]})}));var i={default:this.default,emphasis:this.emphasis};return t.length>0&&(i.customStyles=t),i},e.prototype.getStyleByName=function(e,t){return e&&this._allStyles.hasOwnProperty(e)?this._allStyles[e]:t||this._allStyles[o.ContainerStyle.Default]},Object.defineProperty(e.prototype,"default",{get:function(){return this._allStyles[o.ContainerStyle.Default]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"emphasis",{get:function(){return this._allStyles[o.ContainerStyle.Emphasis]},enumerable:!1,configurable:!0}),e}();t.ContainerStyleSet=M;var V=function(){function e(e){this.fontFamily="Segoe UI,Segoe,Segoe WP,Helvetica Neue,Helvetica,sans-serif",this.fontSizes={small:12,default:14,medium:17,large:21,extraLarge:26},this.fontWeights={lighter:200,default:400,bolder:600},e&&(this.fontFamily=e)}return e.prototype.parse=function(e){this.fontFamily=e.fontFamily||this.fontFamily,this.fontSizes={small:e.fontSizes&&e.fontSizes.small||this.fontSizes.small,default:e.fontSizes&&e.fontSizes.default||this.fontSizes.default,medium:e.fontSizes&&e.fontSizes.medium||this.fontSizes.medium,large:e.fontSizes&&e.fontSizes.large||this.fontSizes.large,extraLarge:e.fontSizes&&e.fontSizes.extraLarge||this.fontSizes.extraLarge},this.fontWeights={lighter:e.fontWeights&&e.fontWeights.lighter||this.fontWeights.lighter,default:e.fontWeights&&e.fontWeights.default||this.fontWeights.default,bolder:e.fontWeights&&e.fontWeights.bolder||this.fontWeights.bolder}},e.monospace=new e("'Courier New', Courier, monospace"),e}();t.FontTypeDefinition=V;var k=function(){function e(e){this.default=new V,this.monospace=new V("'Courier New', Courier, monospace"),e&&(this.default.parse(e.default),this.monospace.parse(e.monospace))}return e.prototype.getStyleDefinition=function(e){switch(e){case o.FontType.Monospace:return this.monospace;case o.FontType.Default:default:return this.default}},e}();t.FontTypeSet=k;var z=function(){function e(e){this.maxCarouselPages=10,this.minAutoplayDelay=5e3,e&&(this.maxCarouselPages=null!=e.maxCarouselPages?e.maxCarouselPages:this.maxCarouselPages,this.minAutoplayDelay=null!=e.minAutoplayDelay?e.minAutoplayDelay:this.minAutoplayDelay)}return e.prototype.toJSON=function(){return{maxCarouselPages:this.maxCarouselPages,minAutoplayDelay:this.minAutoplayDelay}},e}();t.CarouselConfig=z;var L=function(){function e(e){this.hostCapabilities=new l.HostCapabilities,this.choiceSetInputValueSeparator=",",this.supportsInteractivity=!0,this.spacing={small:3,default:8,medium:20,large:30,extraLarge:40,padding:15},this.separator={lineThickness:1,lineColor:"#EEEEEE"},this.imageSizes={small:40,medium:80,large:160},this.containerStyles=new M,this.inputs=new S,this.actions=new A,this.adaptiveCard=new d,this.imageSet=new h,this.media=new f,this.factSet=new P,this.table=new m,this.textStyles=new v,this.textBlock=new b,this.carousel=new z,this.alwaysAllowBleed=!1,e&&(("string"==typeof e||e instanceof String)&&(e=JSON.parse(e)),this.choiceSetInputValueSeparator=e&&"string"==typeof e.choiceSetInputValueSeparator?e.choiceSetInputValueSeparator:this.choiceSetInputValueSeparator,this.supportsInteractivity=e&&"boolean"==typeof e.supportsInteractivity?e.supportsInteractivity:this.supportsInteractivity,this._legacyFontType=new V,this._legacyFontType.parse(e),e.fontTypes&&(this.fontTypes=new k(e.fontTypes)),e.lineHeights&&(this.lineHeights={small:e.lineHeights.small,default:e.lineHeights.default,medium:e.lineHeights.medium,large:e.lineHeights.large,extraLarge:e.lineHeights.extraLarge}),this.imageSizes={small:e.imageSizes&&e.imageSizes.small||this.imageSizes.small,medium:e.imageSizes&&e.imageSizes.medium||this.imageSizes.medium,large:e.imageSizes&&e.imageSizes.large||this.imageSizes.large},this.containerStyles=new M(e.containerStyles),this.spacing={small:e.spacing&&e.spacing.small||this.spacing.small,default:e.spacing&&e.spacing.default||this.spacing.default,medium:e.spacing&&e.spacing.medium||this.spacing.medium,large:e.spacing&&e.spacing.large||this.spacing.large,extraLarge:e.spacing&&e.spacing.extraLarge||this.spacing.extraLarge,padding:e.spacing&&e.spacing.padding||this.spacing.padding},this.separator={lineThickness:e.separator&&e.separator.lineThickness||this.separator.lineThickness,lineColor:e.separator&&e.separator.lineColor||this.separator.lineColor},this.inputs=new S(e.inputs||this.inputs),this.actions=new A(e.actions||this.actions),this.adaptiveCard=new d(e.adaptiveCard||this.adaptiveCard),this.imageSet=new h(e.imageSet),this.factSet=new P(e.factSet),this.textStyles=new v(e.textStyles),this.textBlock=new b(e.textBlock),this.carousel=new z(e.carousel))}return e.prototype.getFontTypeDefinition=function(e){return this.fontTypes?this.fontTypes.getStyleDefinition(e):e===o.FontType.Monospace?V.monospace:this._legacyFontType},e.prototype.getEffectiveSpacing=function(e){switch(e){case o.Spacing.Small:return this.spacing.small;case o.Spacing.Default:return this.spacing.default;case o.Spacing.Medium:return this.spacing.medium;case o.Spacing.Large:return this.spacing.large;case o.Spacing.ExtraLarge:return this.spacing.extraLarge;case o.Spacing.Padding:return this.spacing.padding;default:return 0}},e.prototype.paddingDefinitionToSpacingDefinition=function(e){return new a.SpacingDefinition(this.getEffectiveSpacing(e.top),this.getEffectiveSpacing(e.right),this.getEffectiveSpacing(e.bottom),this.getEffectiveSpacing(e.left))},e.prototype.makeCssClassNames=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var i=[],n=0,r=e;n<r.length;n++){var o=r[n];i.push((this.cssClassNamePrefix?this.cssClassNamePrefix+"-":"")+o)}return i},e.prototype.makeCssClassName=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var i=this.makeCssClassNames.apply(this,e).join(" ");return i||""},Object.defineProperty(e.prototype,"fontFamily",{get:function(){return this._legacyFontType.fontFamily},set:function(e){this._legacyFontType.fontFamily=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fontSizes",{get:function(){return this._legacyFontType.fontSizes},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fontWeights",{get:function(){return this._legacyFontType.fontWeights},enumerable:!1,configurable:!0}),e}();t.HostConfig=L,t.defaultHostConfig=new L({supportsInteractivity:!0,spacing:{small:10,default:20,medium:30,large:40,extraLarge:50,padding:20},separator:{lineThickness:1,lineColor:"#EEEEEE"},fontTypes:{default:{fontFamily:"'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",fontSizes:{small:12,default:14,medium:17,large:21,extraLarge:26},fontWeights:{lighter:200,default:400,bolder:600}},monospace:{fontFamily:"'Courier New', Courier, monospace",fontSizes:{small:12,default:14,medium:17,large:21,extraLarge:26},fontWeights:{lighter:200,default:400,bolder:600}}},imageSizes:{small:40,medium:80,large:160},containerStyles:{default:{backgroundColor:"#FFFFFF",foregroundColors:{default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},attention:{default:"#cc3300",subtle:"#DDcc3300"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#e69500",subtle:"#DDe69500"}}},emphasis:{backgroundColor:"#08000000",foregroundColors:{default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},attention:{default:"#cc3300",subtle:"#DDcc3300"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#e69500",subtle:"#DDe69500"}}},accent:{backgroundColor:"#C7DEF9",foregroundColors:{default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},attention:{default:"#cc3300",subtle:"#DDcc3300"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#e69500",subtle:"#DDe69500"}}},good:{backgroundColor:"#CCFFCC",foregroundColors:{default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},attention:{default:"#cc3300",subtle:"#DDcc3300"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#e69500",subtle:"#DDe69500"}}},attention:{backgroundColor:"#FFC5B2",foregroundColors:{default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},attention:{default:"#cc3300",subtle:"#DDcc3300"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#e69500",subtle:"#DDe69500"}}},warning:{backgroundColor:"#FFE2B2",foregroundColors:{default:{default:"#333333",subtle:"#EE333333"},dark:{default:"#000000",subtle:"#66000000"},light:{default:"#FFFFFF",subtle:"#33000000"},accent:{default:"#2E89FC",subtle:"#882E89FC"},attention:{default:"#cc3300",subtle:"#DDcc3300"},good:{default:"#028A02",subtle:"#DD027502"},warning:{default:"#e69500",subtle:"#DDe69500"}}}},inputs:{label:{requiredInputs:{weight:o.TextWeight.Bolder,suffix:" *",suffixColor:o.TextColor.Attention},optionalInputs:{weight:o.TextWeight.Bolder}},errorMessage:{color:o.TextColor.Attention,weight:o.TextWeight.Bolder},debounceTimeInMilliSeconds:250},actions:{maxActions:5,spacing:o.Spacing.Default,buttonSpacing:10,showCard:{actionMode:o.ShowCardActionMode.Inline,inlineTopMargin:16},actionsOrientation:o.Orientation.Horizontal,actionAlignment:o.ActionAlignment.Left},adaptiveCard:{allowCustomStyle:!1},imageSet:{imageSize:o.Size.Medium,maxImageHeight:100},factSet:{title:{color:o.TextColor.Default,size:o.TextSize.Default,isSubtle:!1,weight:o.TextWeight.Bolder,wrap:!0,maxWidth:150},value:{color:o.TextColor.Default,size:o.TextSize.Default,isSubtle:!1,weight:o.TextWeight.Default,wrap:!0},spacing:10},carousel:{maxCarouselPages:10,minAutoplayDuration:5e3},textBlock:{headingLevel:2}})},432:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalRegistry=t.CardObjectRegistry=void 0;var n=i(475),r=function(){function e(){this._items={}}return e.prototype.findByName=function(e){return this._items.hasOwnProperty(e)?this._items[e]:void 0},e.prototype.clear=function(){this._items={}},e.prototype.copyTo=function(e){for(var t=0,i=Object.keys(this._items);t<i.length;t++){var n=i[t],r=this._items[n];e.register(r.typeName,r.objectType,r.schemaVersion)}},e.prototype.register=function(e,t,i){void 0===i&&(i=n.Versions.v1_0);var r=this.findByName(e);void 0!==r?r.objectType=t:r={typeName:e,objectType:t,schemaVersion:i},this._items[e]=r},e.prototype.unregister=function(e){delete this._items[e]},e.prototype.createInstance=function(e,t){var i=this.findByName(e);return i&&i.schemaVersion.compareTo(t)<=0?new i.objectType:void 0},e.prototype.getItemCount=function(){return Object.keys(this._items).length},e.prototype.getItemAt=function(e){var t=this;return Object.keys(this._items).map((function(e){return t._items[e]}))[e]},e}();t.CardObjectRegistry=r;var o=function(){function e(){}return e.populateWithDefaultElements=function(t){t.clear(),e.defaultElements.copyTo(t)},e.populateWithDefaultActions=function(t){t.clear(),e.defaultActions.copyTo(t)},Object.defineProperty(e,"elements",{get:function(){return e._elements||(e._elements=new r,e.populateWithDefaultElements(e._elements)),e._elements},enumerable:!1,configurable:!0}),Object.defineProperty(e,"actions",{get:function(){return e._actions||(e._actions=new r,e.populateWithDefaultActions(e._actions)),e._actions},enumerable:!1,configurable:!0}),e.reset=function(){e._elements=void 0,e._actions=void 0},e.defaultElements=new r,e.defaultActions=new r,e}();t.GlobalRegistry=o},475:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)});Object.defineProperty(t,"__esModule",{value:!0}),t.SerializableObject=t.property=t.SerializableObjectSchema=t.CustomProperty=t.SerializableObjectCollectionProperty=t.SerializableObjectProperty=t.EnumProperty=t.ValueSetProperty=t.StringArrayProperty=t.PixelSizeProperty=t.NumProperty=t.BoolProperty=t.StringProperty=t.PropertyDefinition=t.BaseSerializationContext=t.isVersionLessOrEqual=t.Versions=t.Version=void 0;var o=i(764),s=i(755),a=i(10),l=i(653),p=function(){function e(e,t,i){void 0===e&&(e=1),void 0===t&&(t=1),this._isValid=!0,this._major=e,this._minor=t,this._label=i}return e.parse=function(t,i){if(t){var n=new e;n._versionString=t;var r=/(\d+).(\d+)/gi.exec(t);return null!=r&&3===r.length?(n._major=parseInt(r[1]),n._minor=parseInt(r[2])):n._isValid=!1,n._isValid||i.logParseEvent(void 0,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.invalidVersionString(n._versionString)),n}},e.prototype.toString=function(){return this._isValid?this._major+"."+this._minor:this._versionString},e.prototype.toJSON=function(){return this.toString()},e.prototype.compareTo=function(e){if(!this.isValid||!e.isValid)throw new Error("Cannot compare invalid version.");return this.major>e.major?1:this.major<e.major?-1:this.minor>e.minor?1:this.minor<e.minor?-1:0},Object.defineProperty(e.prototype,"label",{get:function(){return this._label?this._label:this.toString()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"major",{get:function(){return this._major},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"minor",{get:function(){return this._minor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isValid",{get:function(){return this._isValid},enumerable:!1,configurable:!0}),e}();t.Version=p;var c=function(){function e(){}return e.getAllDeclaredVersions=function(){var t=e,i=[];for(var n in t)if(n.match(/^v[0-9_]*$/))try{var r=t[n];r instanceof p&&i.push(r)}catch(e){}return i.sort((function(e,t){return e.compareTo(t)}))},e.v1_0=new p(1,0),e.v1_1=new p(1,1),e.v1_2=new p(1,2),e.v1_3=new p(1,3),e.v1_4=new p(1,4),e.v1_5=new p(1,5),e.v1_6=new p(1,6),e.latest=e.v1_6,e}();t.Versions=c,t.isVersionLessOrEqual=function(e,t){return!(e instanceof p)||!(t instanceof p)||t.compareTo(e)>=0};var u=function(){function e(e){void 0===e&&(e=c.latest),this._validationEvents=[],this.targetVersion=e}return e.prototype.isTemplateString=function(e){return"string"==typeof e&&e.startsWith("${")},e.prototype.tryDeleteValue=function(e,t){o.GlobalSettings.enableFullJsonRoundTrip||delete e[t]},e.prototype.tryDeleteDefaultValue=function(e,t){o.GlobalSettings.enableFullJsonRoundTrip&&this.isTemplateString(e[t])||delete e[t]},e.prototype.serializeValue=function(e,t,i,n,r){void 0===n&&(n=void 0),void 0===r&&(r=!1),null==i?o.GlobalSettings.enableFullJsonRoundTrip&&!r||delete e[t]:i===n?o.GlobalSettings.enableFullJsonRoundTrip&&!r&&this.isTemplateString(e[t])||delete e[t]:e[t]=i},e.prototype.serializeString=function(e,t,i,n){null==i?this.tryDeleteValue(e,t):i===n?this.tryDeleteDefaultValue(e,t):e[t]=i},e.prototype.serializeBool=function(e,t,i,n){null==i?this.tryDeleteValue(e,t):i===n?this.tryDeleteDefaultValue(e,t):e[t]=i},e.prototype.serializeNumber=function(e,t,i,n){null==i||isNaN(i)?this.tryDeleteValue(e,t):i===n?this.tryDeleteDefaultValue(e,t):e[t]=i},e.prototype.serializeEnum=function(e,t,i,n,r){void 0===r&&(r=void 0),null==n?this.tryDeleteValue(t,i):n===r?this.tryDeleteDefaultValue(t,i):t[i]=e[n]},e.prototype.serializeArray=function(e,t,i){var n=[];if(i)for(var r=0,o=i;r<o.length;r++){var s,a=o[r];void 0!==(s=a instanceof P?a.toJSON(this):a.toJSON?a.toJSON():a)&&n.push(s)}0===n.length?e.hasOwnProperty(t)&&Array.isArray(e[t])&&delete e[t]:this.serializeValue(e,t,n)},e.prototype.clearEvents=function(){this._validationEvents=[]},e.prototype.logEvent=function(e,t,i,n){this._validationEvents.push({source:e,phase:t,event:i,message:n})},e.prototype.logParseEvent=function(e,t,i){this.logEvent(e,a.ValidationPhase.Parse,t,i)},e.prototype.getEventAt=function(e){return this._validationEvents[e]},Object.defineProperty(e.prototype,"eventCount",{get:function(){return this._validationEvents.length},enumerable:!1,configurable:!0}),e}();t.BaseSerializationContext=u;var d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(u),h=function(){function e(t,i,n,r){this.targetVersion=t,this.name=i,this.defaultValue=n,this.onGetInitialValue=r,this.isSerializationEnabled=!0,this.sequentialNumber=e._sequentialNumber,e._sequentialNumber++}return e.prototype.getInternalName=function(){return this.name},e.prototype.parse=function(e,t,i){return t[this.name]},e.prototype.toJSON=function(e,t,i,n){n.serializeValue(t,this.name,i,this.defaultValue)},e._sequentialNumber=0,e}();t.PropertyDefinition=h;var f=function(e){function t(t,i,n,r,o,s){void 0===n&&(n=!0);var a=e.call(this,t,i,o,s)||this;return a.targetVersion=t,a.name=i,a.treatEmptyAsUndefined=n,a.regEx=r,a.defaultValue=o,a.onGetInitialValue=s,a}return r(t,e),t.prototype.parse=function(e,t,i){var n=s.parseString(t[this.name],this.defaultValue);if(void 0===n||""===n&&this.treatEmptyAsUndefined||void 0===this.regEx||this.regEx.exec(n))return n;i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.invalidPropertyValue(n,this.name))},t.prototype.toJSON=function(e,t,i,n){n.serializeString(t,this.name,""===i&&this.treatEmptyAsUndefined?void 0:i,this.defaultValue)},t}(h);t.StringProperty=f;var m=function(e){function t(t,i,n,r){var o=e.call(this,t,i,n,r)||this;return o.targetVersion=t,o.name=i,o.defaultValue=n,o.onGetInitialValue=r,o}return r(t,e),t.prototype.parse=function(e,t,i){return s.parseBool(t[this.name],this.defaultValue)},t.prototype.toJSON=function(e,t,i,n){n.serializeBool(t,this.name,i,this.defaultValue)},t}(h);t.BoolProperty=m;var g=function(e){function t(t,i,n,r){var o=e.call(this,t,i,n,r)||this;return o.targetVersion=t,o.name=i,o.defaultValue=n,o.onGetInitialValue=r,o}return r(t,e),t.prototype.parse=function(e,t,i){return s.parseNumber(t[this.name],this.defaultValue)},t.prototype.toJSON=function(e,t,i,n){n.serializeNumber(t,this.name,i,this.defaultValue)},t}(h);t.NumProperty=g;var y=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.parse=function(e,t,i){var n=void 0,r=t[this.name];if("string"==typeof r){var s=!1;try{var p=o.SizeAndUnit.parse(r,!0);p.unit===a.SizeUnit.Pixel&&(n=p.physicalSize,s=!0)}catch(e){}s||i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.invalidPropertyValue(t[this.name],this.name))}return n},t.prototype.toJSON=function(e,t,i,n){n.serializeValue(t,this.name,"number"!=typeof i||isNaN(i)?void 0:i+"px")},t}(h);t.PixelSizeProperty=y;var v=function(e){function t(t,i,n,r){var o=e.call(this,t,i,n,r)||this;return o.targetVersion=t,o.name=i,o.defaultValue=n,o.onGetInitialValue=r,o}return r(t,e),t.prototype.parse=function(e,t,i){var n=t[this.name];if(void 0===n||!Array.isArray(n))return this.defaultValue;for(var r=[],o=0,s=n;o<s.length;o++){var l=s[o];"string"==typeof l?r.push(l):i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,'Invalid array value "'+JSON.stringify(l)+'" of type "'+typeof l+'" ignored for "'+this.name+'".')}return r},t.prototype.toJSON=function(e,t,i,n){n.serializeArray(t,this.name,i)},t}(h);t.StringArrayProperty=v;var b=function(e){function t(t,i,n,r,o){var s=e.call(this,t,i,r,o)||this;return s.targetVersion=t,s.name=i,s.values=n,s.defaultValue=r,s.onGetInitialValue=o,s}return r(t,e),t.prototype.isValidValue=function(e,t){for(var i=0,n=this.values;i<n.length;i++){var r=n[i];if(e.toLowerCase()===r.value.toLowerCase())return(r.targetVersion?r.targetVersion:this.targetVersion).compareTo(t.targetVersion)<=0}return!1},t.prototype.parse=function(e,t,i){var n=t[this.name];if(void 0===n)return this.defaultValue;if("string"==typeof n)for(var r=0,o=this.values;r<o.length;r++){var s=o[r];if(n.toLowerCase()===s.value.toLowerCase()){var p=s.targetVersion?s.targetVersion:this.targetVersion;return p.compareTo(i.targetVersion)<=0?s.value:(i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.propertyValueNotSupported(n,this.name,p.toString(),i.targetVersion.toString())),this.defaultValue)}}return i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.invalidPropertyValue(n,this.name)),this.defaultValue},t.prototype.toJSON=function(e,t,i,n){var r=!1;if(void 0!==i){r=!0;for(var o=0,s=this.values;o<s.length;o++){var p=s[o];if(p.value===i){var c=p.targetVersion?p.targetVersion:this.targetVersion;if(c.compareTo(n.targetVersion)<=0){r=!1;break}n.logEvent(e,a.ValidationPhase.ToJSON,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.propertyValueNotSupported(i,this.name,c.toString(),n.targetVersion.toString()))}}}r||n.serializeValue(t,this.name,i,this.defaultValue,!0)},t}(h);t.ValueSetProperty=b;var C=function(e){function t(t,i,n,r,o,s){var a=e.call(this,t,i,r,s)||this;if(a.targetVersion=t,a.name=i,a.enumType=n,a.defaultValue=r,a.onGetInitialValue=s,a._values=[],o)a._values=o;else for(var l in n){var p=parseInt(l,10);p>=0&&a._values.push({value:p})}return a}return r(t,e),t.prototype.parse=function(e,t,i){var n=t[this.name];if("string"!=typeof n)return this.defaultValue;var r=s.getEnumValueByName(this.enumType,n);if(void 0!==r)for(var o=0,p=this.values;o<p.length;o++){var c=p[o];if(c.value===r){var u=c.targetVersion?c.targetVersion:this.targetVersion;return u.compareTo(i.targetVersion)<=0?r:(i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.propertyValueNotSupported(n,this.name,u.toString(),i.targetVersion.toString())),this.defaultValue)}}return i.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.invalidPropertyValue(n,this.name)),this.defaultValue},t.prototype.toJSON=function(e,t,i,n){var r=!1;if(void 0!==i){r=!0;for(var o=0,s=this.values;o<s.length;o++){var p=s[o];if(p.value===i){if((p.targetVersion?p.targetVersion:this.targetVersion).compareTo(n.targetVersion)<=0){r=!1;break}n.logEvent(e,a.ValidationPhase.ToJSON,a.ValidationEvent.InvalidPropertyValue,l.Strings.errors.invalidPropertyValue(i,this.name))}}}r||n.serializeEnum(this.enumType,t,this.name,i,this.defaultValue)},Object.defineProperty(t.prototype,"values",{get:function(){return this._values},enumerable:!1,configurable:!0}),t}(h);t.EnumProperty=C;var w=function(e){function t(t,i,n,r,o){void 0===r&&(r=!1);var s=e.call(this,t,i,o,(function(e){return s.nullable?void 0:new s.objectType}))||this;return s.targetVersion=t,s.name=i,s.objectType=n,s.nullable=r,s}return r(t,e),t.prototype.parse=function(e,t,i){var n=t[this.name];if(void 0===n)return this.onGetInitialValue?this.onGetInitialValue(e):this.defaultValue;var r=new this.objectType;return r.parse(n,i),r},t.prototype.toJSON=function(e,t,i,n){var r=void 0;void 0===i||i.hasAllDefaultValues()||(r=i.toJSON(n)),"object"==typeof r&&0===Object.keys(r).length&&(r=void 0),n.serializeValue(t,this.name,r,this.defaultValue,!0)},t}(h);t.SerializableObjectProperty=w;var S=function(e){function t(t,i,n,r){var o=e.call(this,t,i,void 0,(function(e){return[]}))||this;return o.targetVersion=t,o.name=i,o.objectType=n,o.onItemAdded=r,o}return r(t,e),t.prototype.parse=function(e,t,i){var n=[],r=t[this.name];if(Array.isArray(r))for(var o=0,s=r;o<s.length;o++){var a=s[o],l=new this.objectType;l.parse(a,i),n.push(l),this.onItemAdded&&this.onItemAdded(e,l)}return n.length>0?n:this.onGetInitialValue?this.onGetInitialValue(e):void 0},t.prototype.toJSON=function(e,t,i,n){n.serializeArray(t,this.name,i)},t}(h);t.SerializableObjectCollectionProperty=S;var E=function(e){function t(t,i,n,r,o,s){var a=e.call(this,t,i,o,s)||this;if(a.targetVersion=t,a.name=i,a.onParse=n,a.onToJSON=r,a.defaultValue=o,a.onGetInitialValue=s,!a.onParse)throw new Error("CustomPropertyDefinition instances must have an onParse handler.");if(!a.onToJSON)throw new Error("CustomPropertyDefinition instances must have an onToJSON handler.");return a}return r(t,e),t.prototype.parse=function(e,t,i){return this.onParse(e,this,t,i)},t.prototype.toJSON=function(e,t,i,n){this.onToJSON(e,this,t,i,n)},t}(h);t.CustomProperty=E;var x=function(){function e(){this._properties=[]}return e.prototype.indexOf=function(e){for(var t=0;t<this._properties.length;t++)if(this._properties[t]===e)return t;return-1},e.prototype.add=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var i=0,n=e;i<n.length;i++){var r=n[i];-1===this.indexOf(r)&&this._properties.push(r)}},e.prototype.remove=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var i=0,n=e;i<n.length;i++)for(var r=n[i];;){var o=this.indexOf(r);if(!(o>=0))break;this._properties.splice(o,1)}},e.prototype.getItemAt=function(e){return this._properties[e]},e.prototype.getCount=function(){return this._properties.length},e}();t.SerializableObjectSchema=x,t.property=function(e){return function(t,i){var n=Object.getOwnPropertyDescriptor(t,i)||{};n.get||n.set||(n.get=function(){return this.getValue(e)},n.set=function(t){this.setValue(e,t)},Object.defineProperty(t,i,n))}};var P=function(){function e(){this._propertyBag={},this._rawProperties={},this.maxVersion=e.defaultMaxVersion;for(var t=this.getSchema(),i=0;i<t.getCount();i++){var n=t.getItemAt(i);n.onGetInitialValue&&this.setValue(n,n.onGetInitialValue(this))}}return e.prototype.getDefaultSerializationContext=function(){return new d},e.prototype.populateSchema=function(t){var i=this.constructor,n=[];for(var r in i)try{var o=i[r];o instanceof h&&n.push(o)}catch(e){}if(n.length>0){var s=n.sort((function(e,t){return e.sequentialNumber>t.sequentialNumber?1:e.sequentialNumber<t.sequentialNumber?-1:0}));t.add.apply(t,s)}e.onRegisterCustomProperties&&e.onRegisterCustomProperties(this,t)},e.prototype.getValue=function(e){return this._propertyBag.hasOwnProperty(e.getInternalName())?this._propertyBag[e.getInternalName()]:e.defaultValue},e.prototype.setValue=function(e,t){null==t?delete this._propertyBag[e.getInternalName()]:this._propertyBag[e.getInternalName()]=t},e.prototype.internalParse=function(e,t){if(this._propertyBag={},this._rawProperties=o.GlobalSettings.enableFullJsonRoundTrip&&e||{},e)for(var i=this.getSchema(),n=0;n<i.getCount();n++){var r=i.getItemAt(n);if(r.isSerializationEnabled){var s=r.onGetInitialValue?r.onGetInitialValue(this):void 0;e.hasOwnProperty(r.name)&&(r.targetVersion.compareTo(t.targetVersion)<=0?s=r.parse(this,e,t):t.logParseEvent(this,a.ValidationEvent.UnsupportedProperty,l.Strings.errors.propertyNotSupported(r.name,r.targetVersion.toString(),t.targetVersion.toString()))),this.setValue(r,s)}}else this.resetDefaultValues()},e.prototype.internalToJSON=function(e,t){for(var i=this.getSchema(),n=[],r=0;r<i.getCount();r++){var o=i.getItemAt(r);o.isSerializationEnabled&&o.targetVersion.compareTo(t.targetVersion)<=0&&-1===n.indexOf(o.name)&&(o.toJSON(this,e,this.getValue(o),t),n.push(o.name))}},e.prototype.shouldSerialize=function(e){return!0},e.prototype.parse=function(e,t){this.internalParse(e,t||new d)},e.prototype.toJSON=function(e){var t;if(e&&e instanceof u?t=e:(t=this.getDefaultSerializationContext()).toJSONOriginalParam=e,this.shouldSerialize(t)){var i;return i=o.GlobalSettings.enableFullJsonRoundTrip&&this._rawProperties&&"object"==typeof this._rawProperties?this._rawProperties:{},this.internalToJSON(i,t),i}},e.prototype.hasDefaultValue=function(e){return this.getValue(e)===e.defaultValue},e.prototype.hasAllDefaultValues=function(){for(var e=this.getSchema(),t=0;t<e.getCount();t++){var i=e.getItemAt(t);if(!this.hasDefaultValue(i))return!1}return!0},e.prototype.resetDefaultValues=function(){for(var e=this.getSchema(),t=0;t<e.getCount();t++){var i=e.getItemAt(t);this.setValue(i,i.defaultValue)}},e.prototype.setCustomProperty=function(e,t){"string"==typeof t&&!t||null==t?delete this._rawProperties[e]:this._rawProperties[e]=t},e.prototype.getCustomProperty=function(e){return this._rawProperties[e]},e.prototype.getSchema=function(){var t=e._schemaCache[this.getSchemaKey()];return t||(t=new x,this.populateSchema(t),e._schemaCache[this.getSchemaKey()]=t),t},e.defaultMaxVersion=c.latest,e._schemaCache={},e}();t.SerializableObject=P},764:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UUID=t.SizeAndUnit=t.PaddingDefinition=t.SpacingDefinition=t.StringWithSubstitutions=t.ContentTypes=t.GlobalSettings=void 0;var n=i(10),r=function(){function e(){}return e.useAdvancedTextBlockTruncation=!0,e.useAdvancedCardBottomTruncation=!1,e.useMarkdownInRadioButtonAndCheckbox=!0,e.allowMarkForTextHighlighting=!1,e.alwaysBleedSeparators=!1,e.enableFullJsonRoundTrip=!1,e.displayInputValidationErrors=!0,e.allowPreProcessingPropertyValues=!1,e.setTabIndexAtCardRoot=!0,e.enableFallback=!0,e.useWebkitLineClamp=!0,e.allowMoreThanMaxActionsInOverflowMenu=!1,e.removePaddingFromContainersWithBackgroundImage=!1,e.resetInputsDirtyStateAfterActionExecution=!0,e.applets={logEnabled:!0,logLevel:n.LogLevel.Error,maximumRetryAttempts:3,defaultTimeBetweenRetryAttempts:3e3,authPromptWidth:400,authPromptHeight:600,refresh:{mode:n.RefreshMode.Manual,timeBetweenAutomaticRefreshes:3e3,maximumConsecutiveAutomaticRefreshes:3,allowManualRefreshesAfterAutomaticRefreshes:!0}},e}();t.GlobalSettings=r,t.ContentTypes={applicationJson:"application/json",applicationXWwwFormUrlencoded:"application/x-www-form-urlencoded"};var o=function(){function e(){this._isProcessed=!1}return e.prototype.getReferencedInputs=function(e,t){if(!t)throw new Error("The referencedInputs parameter cannot be null.");if(this._original)for(var i=0,n=e;i<n.length;i++){var r=n[i];null!=new RegExp("\\{{2}("+r.id+").value\\}{2}","gi").exec(this._original)&&r.id&&(t[r.id]=r)}},e.prototype.substituteInputValues=function(e,i){if(this._processed=this._original,this._original)for(var n=/\{{2}([a-z0-9_$@]+).value\}{2}/gi,r=void 0;null!==(r=n.exec(this._original))&&this._processed;)for(var o=0,s=Object.keys(e);o<s.length;o++){var a=s[o];if(a.toLowerCase()===r[1].toLowerCase()){var l=e[a],p="";l.value&&(p=l.value),i===t.ContentTypes.applicationJson?p=(p=JSON.stringify(p)).slice(1,-1):i===t.ContentTypes.applicationXWwwFormUrlencoded&&(p=encodeURIComponent(p)),this._processed=this._processed.replace(r[0],p);break}}this._isProcessed=!0},e.prototype.getOriginal=function(){return this._original},e.prototype.get=function(){return this._isProcessed?this._processed:this._original},e.prototype.set=function(e){this._original=e,this._isProcessed=!1},e}();t.StringWithSubstitutions=o;t.SpacingDefinition=function(e,t,i,n){void 0===e&&(e=0),void 0===t&&(t=0),void 0===i&&(i=0),void 0===n&&(n=0),this.left=0,this.top=0,this.right=0,this.bottom=0,this.top=e,this.right=t,this.bottom=i,this.left=n};t.PaddingDefinition=function(e,t,i,r){void 0===e&&(e=n.Spacing.None),void 0===t&&(t=n.Spacing.None),void 0===i&&(i=n.Spacing.None),void 0===r&&(r=n.Spacing.None),this.top=n.Spacing.None,this.right=n.Spacing.None,this.bottom=n.Spacing.None,this.left=n.Spacing.None,this.top=e,this.right=t,this.bottom=i,this.left=r};var s=function(){function e(e,t){this.physicalSize=e,this.unit=t}return e.parse=function(t,i){void 0===i&&(i=!1);var r=new e(0,n.SizeUnit.Weight);if("number"==typeof t)return r.physicalSize=t,r;if("string"==typeof t){var o=/^([0-9]+)(px|\*)?$/g.exec(t),s=i?3:2;if(o&&o.length>=s)return r.physicalSize=parseInt(o[1]),3===o.length&&"px"===o[2]&&(r.unit=n.SizeUnit.Pixel),r}throw new Error("Invalid size: "+t)},e}();t.SizeAndUnit=s;var a=function(){function e(){}return e.generate=function(){var t=4294967295*Math.random()|0,i=4294967295*Math.random()|0,n=4294967295*Math.random()|0,r=4294967295*Math.random()|0;return e.lut[255&t]+e.lut[t>>8&255]+e.lut[t>>16&255]+e.lut[t>>24&255]+"-"+e.lut[255&i]+e.lut[i>>8&255]+"-"+e.lut[i>>16&15|64]+e.lut[i>>24&255]+"-"+e.lut[63&n|128]+e.lut[n>>8&255]+"-"+e.lut[n>>16&255]+e.lut[n>>24&255]+e.lut[255&r]+e.lut[r>>8&255]+e.lut[r>>16&255]+e.lut[r>>24&255]},e.initialize=function(){for(var t=0;t<256;t++)e.lut[t]=(t<16?"0":"")+t.toString(16)},e.lut=[],e}();t.UUID=a,a.initialize()},653:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Strings=void 0;var i=function(){function e(){}return e.errors={unknownElementType:function(e){return'Unknown element type "'+e+'". Fallback will be used if present.'},unknownActionType:function(e){return'Unknown action type "'+e+'". Fallback will be used if present.'},elementTypeNotAllowed:function(e){return'Element type "'+e+'" is not allowed in this context.'},actionTypeNotAllowed:function(e){return'Action type "'+e+'" is not allowed in this context.'},invalidPropertyValue:function(e,t){return'Invalid value "'+e+'" for property "'+t+'".'},showCardMustHaveCard:function(){return'"An Action.ShowCard must have its "card" property set to a valid AdaptiveCard object.'},invalidColumnWidth:function(e){return'Invalid column width "'+e+'" - defaulting to "auto".'},invalidCardVersion:function(e){return'Invalid card version. Defaulting to "'+e+'".'},invalidVersionString:function(e){return'Invalid version string "'+e+'".'},propertyValueNotSupported:function(e,t,i,n){return'Value "'+e+'" for property "'+t+'" is supported in version '+i+", but you are using version "+n+"."},propertyNotSupported:function(e,t,i){return'Property "'+e+'" is supported in version '+t+", but you are using version "+i+"."},indexOutOfRange:function(e){return"Index out of range ("+e+")."},elementCannotBeUsedAsInline:function(){return"RichTextBlock.addInline: the specified card element cannot be used as a RichTextBlock inline."},inlineAlreadyParented:function(){return"RichTextBlock.addInline: the specified inline already belongs to another RichTextBlock."},interactivityNotAllowed:function(){return"Interactivity is not allowed."},inputsMustHaveUniqueId:function(){return"All inputs must have a unique Id."},choiceSetMustHaveAtLeastOneChoice:function(){return"An Input.ChoiceSet must have at least one choice defined."},choiceSetChoicesMustHaveTitleAndValue:function(){return"All choices in an Input.ChoiceSet must have their title and value properties set."},propertyMustBeSet:function(e){return'Property "'+e+'" must be set.'},actionHttpHeadersMustHaveNameAndValue:function(){return"All headers of an Action.Http must have their name and value properties set."},tooManyActions:function(e){return"Maximum number of actions exceeded ("+e+")."},tooLittleTimeDelay:function(e){return"Autoplay Delay is too short ("+e+")."},tooManyCarouselPages:function(e){return"Maximum number of Carousel pages exceeded ("+e+")."},invalidInitialPageIndex:function(e){return"InitialPage for carousel is invalid ("+e+")."},columnAlreadyBelongsToAnotherSet:function(){return"This column already belongs to another ColumnSet."},invalidCardType:function(){return'Invalid or missing card type. Make sure the card\'s type property is set to "AdaptiveCard".'},unsupportedCardVersion:function(e,t){return"The specified card version ("+e+") is not supported or still in preview. The latest released card version is "+t+"."},duplicateId:function(e){return'Duplicate Id "'+e+'".'},markdownProcessingNotEnabled:function(){return"Markdown processing isn't enabled. Please see https://www.npmjs.com/package/adaptivecards#supporting-markdown"},processMarkdownEventRemoved:function(){return"The processMarkdown event has been removed. Please update your code and set onProcessMarkdown instead."},elementAlreadyParented:function(){return"The element already belongs to another container."},actionAlreadyParented:function(){return"The action already belongs to another element."},elementTypeNotStandalone:function(e){return"Elements of type "+e+" cannot be used as standalone elements."},invalidInputLabelWidth:function(){return"Invalid input label width. Defaulting to label width from host config."}},e.magicCodeInputCard={tryAgain:function(){return"That didn't work... let's try again."},pleaseLogin:function(){return'Please login in the popup. You will obtain a magic code. Paste that code below and select "Submit"'},enterMagicCode:function(){return"Enter magic code"},pleaseEnterMagicCodeYouReceived:function(){return"Please enter the magic code you received."},submit:function(){return"Submit"},cancel:function(){return"Cancel"},somethingWentWrong:function(){return"Something went wrong. This action can't be handled."},authenticationFailed:function(){return"Authentication failed."}},e.runtime={automaticRefreshPaused:function(){return"Automatic refresh paused."},clckToRestartAutomaticRefresh:function(){return"Click to restart."},refreshThisCard:function(){return"Refresh this card"}},e.hints={dontUseWeightedAndStrecthedColumnsInSameSet:function(){return"It is not recommended to use weighted and stretched columns in the same ColumnSet, because in such a situation stretched columns will always get the minimum amount of space."}},e.defaults={inlineActionTitle:function(){return"Inline Action"},overflowButtonText:function(){return"..."},overflowButtonTooltip:function(){return"More options"},emptyElementText:function(e){return"Empty "+e},mediaPlayerAriaLabel:function(){return"Media content"},mediaPlayerPlayMedia:function(){return"Play media"},youTubeVideoPlayer:function(){return"YouTube video player"},vimeoVideoPlayer:function(){return"Vimeo video player"},dailymotionVideoPlayer:function(){return"Dailymotion video player"},carouselNavigationPreviousTooltip:function(){return"Previous carousel page"},carouselNavigationNextTooltip:function(){return"Next carousel page"}},e}();t.Strings=i},74:function(e,t,i){var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),o=this&&this.__decorate||function(e,t,i,n){var r,o=arguments.length,s=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,i,n);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,i,s):r(t,i))||s);return o>3&&s&&Object.defineProperty(t,i,s),s};Object.defineProperty(t,"__esModule",{value:!0}),t.Table=t.TableRow=t.TableCell=t.StylableContainer=t.TableColumnDefinition=void 0;var s=i(651),a=i(10),l=i(432),p=i(475),c=i(764),u=i(653),d=i(755),h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.width=new c.SizeAndUnit(1,a.SizeUnit.Weight),t}return r(t,e),t.prototype.getSchemaKey=function(){return"ColumnDefinition"},t.horizontalCellContentAlignmentProperty=new p.EnumProperty(p.Versions.v1_5,"horizontalCellContentAlignment",a.HorizontalAlignment),t.verticalCellContentAlignmentProperty=new p.EnumProperty(p.Versions.v1_5,"verticalCellContentAlignment",a.VerticalAlignment),t.widthProperty=new p.CustomProperty(p.Versions.v1_5,"width",(function(e,t,i,n){var r=t.defaultValue,o=i[t.name],s=!1;if("number"!=typeof o||isNaN(o))if("string"==typeof o)try{r=c.SizeAndUnit.parse(o)}catch(e){s=!0}else s=!0;else r=new c.SizeAndUnit(o,a.SizeUnit.Weight);return s&&n.logParseEvent(e,a.ValidationEvent.InvalidPropertyValue,u.Strings.errors.invalidColumnWidth(o)),r}),(function(e,t,i,n,r){n.unit===a.SizeUnit.Pixel?r.serializeValue(i,"width",n.physicalSize+"px"):r.serializeNumber(i,"width",n.physicalSize)}),new c.SizeAndUnit(1,a.SizeUnit.Weight)),o([(0,p.property)(t.horizontalCellContentAlignmentProperty)],t.prototype,"horizontalCellContentAlignment",void 0),o([(0,p.property)(t.verticalCellContentAlignmentProperty)],t.prototype,"verticalCellContentAlignment",void 0),o([(0,p.property)(t.widthProperty)],t.prototype,"width",void 0),t}(p.SerializableObject);t.TableColumnDefinition=h;var f=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._items=[],t}return r(t,e),t.prototype.parseItem=function(e,t){var i=this;return t.parseCardObject(this,e,[],!this.isDesignMode(),(function(e){return i.createItemInstance(e)}),(function(e,i){t.logParseEvent(void 0,a.ValidationEvent.ElementTypeNotAllowed,u.Strings.errors.elementTypeNotAllowed(e))}))},t.prototype.internalAddItem=function(e){if(e.parent)throw new Error(u.Strings.errors.elementAlreadyParented());this._items.push(e),e.setParent(this)},t.prototype.internalRemoveItem=function(e){var t=this._items.indexOf(e);return t>=0&&(this._items.splice(t,1),e.setParent(void 0),this.updateLayout(),!0)},t.prototype.internalIndexOf=function(e){return this._items.indexOf(e)},t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this._items=[];var n=t[this.getCollectionPropertyName()];if(Array.isArray(n))for(var r=0,o=n;r<o.length;r++){var s=o[r],a=this.parseItem(s,i);a&&this._items.push(a)}},t.prototype.internalToJSON=function(t,i){e.prototype.internalToJSON.call(this,t,i),i.serializeArray(t,this.getCollectionPropertyName(),this._items)},t.prototype.removeItem=function(e){return this.internalRemoveItem(e)},t.prototype.getItemCount=function(){return this._items.length},t.prototype.getItemAt=function(e){return this._items[e]},t.prototype.getFirstVisibleRenderedItem=function(){return this.getItemCount()>0?this.getItemAt(0):void 0},t.prototype.getLastVisibleRenderedItem=function(){return this.getItemCount()>0?this.getItemAt(this.getItemCount()-1):void 0},t}(s.StylableCardElementContainer);t.StylableContainer=f;var m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._columnIndex=-1,t._cellType="data",t}return r(t,e),t.prototype.getHasBorder=function(){return this.parentRow.parentTable.showGridLines},t.prototype.applyBorder=function(){if(this.renderedElement&&this.getHasBorder()){var e=this.hostConfig.containerStyles.getStyleByName(this.parentRow.parentTable.gridStyle);if(e.borderColor){var t=(0,d.stringToCssColor)(e.borderColor);t&&(this.renderedElement.style.borderRight="1px solid "+t,this.renderedElement.style.borderBottom="1px solid "+t)}}},t.prototype.getDefaultPadding=function(){return this.getHasBackground()||this.getHasBorder()?new c.PaddingDefinition(a.Spacing.Small,a.Spacing.Small,a.Spacing.Small,a.Spacing.Small):e.prototype.getDefaultPadding.call(this)},t.prototype.internalRender=function(){var t=e.prototype.internalRender.call(this);return t&&(t.setAttribute("role","data"===this.cellType?"cell":"columnheader"),t.style.minWidth="0","header"===this.cellType&&t.setAttribute("scope","col")),t},t.prototype.shouldSerialize=function(e){return!0},t.prototype.getJsonTypeName=function(){return"TableCell"},t.prototype.getEffectiveTextStyleDefinition=function(){return"header"===this.cellType?this.hostConfig.textStyles.columnHeader:e.prototype.getEffectiveTextStyleDefinition.call(this)},t.prototype.getEffectiveHorizontalAlignment=function(){if(void 0!==this.horizontalAlignment)return this.horizontalAlignment;if(void 0!==this.parentRow.horizontalCellContentAlignment)return this.parentRow.horizontalCellContentAlignment;if(this.columnIndex>=0){var t=this.parentRow.parentTable.getColumnAt(this.columnIndex).horizontalCellContentAlignment;if(void 0!==t)return t}return void 0!==this.parentRow.parentTable.horizontalCellContentAlignment?this.parentRow.parentTable.horizontalCellContentAlignment:e.prototype.getEffectiveHorizontalAlignment.call(this)},t.prototype.getEffectiveVerticalContentAlignment=function(){if(void 0!==this.verticalContentAlignment)return this.verticalContentAlignment;if(void 0!==this.parentRow.verticalCellContentAlignment)return this.parentRow.verticalCellContentAlignment;if(this.columnIndex>=0){var t=this.parentRow.parentTable.getColumnAt(this.columnIndex).verticalCellContentAlignment;if(void 0!==t)return t}return void 0!==this.parentRow.parentTable.verticalCellContentAlignment?this.parentRow.parentTable.verticalCellContentAlignment:e.prototype.getEffectiveVerticalContentAlignment.call(this)},Object.defineProperty(t.prototype,"columnIndex",{get:function(){return this._columnIndex},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"cellType",{get:function(){return this._cellType},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"parentRow",{get:function(){return this.parent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),t}(s.Container);t.TableCell=m;var g=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.getDefaultPadding=function(){return new c.PaddingDefinition(a.Spacing.None,a.Spacing.None,a.Spacing.None,a.Spacing.None)},t.prototype.applyBackground=function(){if(this.renderedElement){var e=this.hostConfig.containerStyles.getStyleByName(this.style,this.hostConfig.containerStyles.getStyleByName(this.defaultStyle));if(e.backgroundColor){var t=(0,d.stringToCssColor)(e.backgroundColor);t&&(this.renderedElement.style.backgroundColor=t)}}},t.prototype.getCollectionPropertyName=function(){return"cells"},t.prototype.createItemInstance=function(e){return e&&"TableCell"!==e?void 0:new m},t.prototype.internalRender=function(){var e=this.getIsFirstRow(),t=this.hostConfig.table.cellSpacing,i=document.createElement("div");i.setAttribute("role","row"),i.style.display="flex",i.style.flexDirection="row";for(var n=0;n<Math.min(this.getItemCount(),this.parentTable.getColumnCount());n++){var r=this.getItemAt(n);r._columnIndex=n,r._cellType=this.parentTable.firstRowAsHeaders&&e?"header":"data";var o=r.render();if(o){var s=this.parentTable.getColumnAt(n);s.computedWidth.unit===a.SizeUnit.Pixel?o.style.flex="0 0 "+s.computedWidth.physicalSize+"px":o.style.flex="1 1 "+s.computedWidth.physicalSize+"%",n>0&&!this.parentTable.showGridLines&&t>0&&(o.style.marginLeft=t+"px"),i.appendChild(o)}}return i.children.length>0?i:void 0},t.prototype.shouldSerialize=function(e){return!0},t.prototype.addCell=function(e){this.internalAddItem(e)},t.prototype.removeCellAt=function(e){return e>=0&&e<this.getItemCount()&&this.removeItem(this.getItemAt(e))},t.prototype.indexOf=function(e){return e instanceof m?this.internalIndexOf(e):-1},t.prototype.ensureHasEnoughCells=function(e){for(;this.getItemCount()<e;)this.addCell(new m)},t.prototype.getJsonTypeName=function(){return"TableRow"},t.prototype.getIsFirstRow=function(){return this.parentTable.getItemAt(0)===this},Object.defineProperty(t.prototype,"parentTable",{get:function(){return this.parent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isStandalone",{get:function(){return!1},enumerable:!1,configurable:!0}),t.styleProperty=new s.ContainerStyleProperty(p.Versions.v1_5,"style"),t.horizontalCellContentAlignmentProperty=new p.EnumProperty(p.Versions.v1_5,"horizontalCellContentAlignment",a.HorizontalAlignment),t.verticalCellContentAlignmentProperty=new p.EnumProperty(p.Versions.v1_5,"verticalCellContentAlignment",a.VerticalAlignment),o([(0,p.property)(t.horizontalCellContentAlignmentProperty)],t.prototype,"horizontalCellContentAlignment",void 0),o([(0,p.property)(t.verticalCellContentAlignmentProperty)],t.prototype,"verticalCellContentAlignment",void 0),t}(f);t.TableRow=g;var y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._columns=[],t.firstRowAsHeaders=!0,t.showGridLines=!0,t}return r(t,e),Object.defineProperty(t.prototype,"gridStyle",{get:function(){var e=this.getValue(t.gridStyleProperty);if(e&&this.hostConfig.containerStyles.getStyleByName(e))return e},set:function(e){this.setValue(t.gridStyleProperty,e)},enumerable:!1,configurable:!0}),t.prototype.ensureRowsHaveEnoughCells=function(){for(var e=0;e<this.getItemCount();e++)this.getItemAt(e).ensureHasEnoughCells(this.getColumnCount())},t.prototype.removeCellsFromColumn=function(e){for(var t=0;t<this.getItemCount();t++)this.getItemAt(t).removeCellAt(e)},t.prototype.getCollectionPropertyName=function(){return"rows"},t.prototype.createItemInstance=function(e){return e&&"TableRow"!==e?void 0:new g},t.prototype.internalParse=function(t,i){e.prototype.internalParse.call(this,t,i),this.ensureRowsHaveEnoughCells()},t.prototype.internalRender=function(){if(this.getItemCount()>0){for(var e=0,t=0,i=this._columns;t<i.length;t++)(o=i[t]).width.unit===a.SizeUnit.Weight&&(e+=o.width.physicalSize);for(var n=0,r=this._columns;n<r.length;n++){var o;(o=r[n]).width.unit===a.SizeUnit.Pixel?o.computedWidth=new c.SizeAndUnit(o.width.physicalSize,a.SizeUnit.Pixel):o.computedWidth=new c.SizeAndUnit(100/e*o.width.physicalSize,a.SizeUnit.Weight)}var s=document.createElement("div");if(s.setAttribute("role","table"),s.style.display="flex",s.style.flexDirection="column",this.showGridLines){var l=this.hostConfig.containerStyles.getStyleByName(this.gridStyle);if(l.borderColor){var p=(0,d.stringToCssColor)(l.borderColor);p&&(s.style.borderTop="1px solid "+p,s.style.borderLeft="1px solid "+p)}}for(var u=this.hostConfig.table.cellSpacing,h=0;h<this.getItemCount();h++){var f=this.getItemAt(h).render();if(f){if(h>0&&!this.showGridLines&&u>0){var m=document.createElement("div");m.setAttribute("aria-hidden","true"),m.style.height=u+"px",s.appendChild(m)}s.appendChild(f)}}return s}},t.prototype.addColumn=function(e){this._columns.push(e),this.ensureRowsHaveEnoughCells()},t.prototype.removeColumn=function(e){var t=this._columns.indexOf(e);t>=0&&(this.removeCellsFromColumn(t),this._columns.splice(t,1))},t.prototype.getColumnCount=function(){return this._columns.length},t.prototype.getColumnAt=function(e){return this._columns[e]},t.prototype.addRow=function(e){this.internalAddItem(e),e.ensureHasEnoughCells(this.getColumnCount())},t.prototype.indexOf=function(e){return e instanceof g?this.internalIndexOf(e):-1},t.prototype.getJsonTypeName=function(){return"Table"},t._columnsProperty=new p.SerializableObjectCollectionProperty(p.Versions.v1_5,"columns",h),t.firstRowAsHeadersProperty=new p.BoolProperty(p.Versions.v1_5,"firstRowAsHeaders",!0),t.showGridLinesProperty=new p.BoolProperty(p.Versions.v1_5,"showGridLines",!0),t.gridStyleProperty=new s.ContainerStyleProperty(p.Versions.v1_5,"gridStyle"),t.horizontalCellContentAlignmentProperty=new p.EnumProperty(p.Versions.v1_5,"horizontalCellContentAlignment",a.HorizontalAlignment),t.verticalCellContentAlignmentProperty=new p.EnumProperty(p.Versions.v1_5,"verticalCellContentAlignment",a.VerticalAlignment),o([(0,p.property)(t._columnsProperty)],t.prototype,"_columns",void 0),o([(0,p.property)(t.firstRowAsHeadersProperty)],t.prototype,"firstRowAsHeaders",void 0),o([(0,p.property)(t.showGridLinesProperty)],t.prototype,"showGridLines",void 0),o([(0,p.property)(t.gridStyleProperty)],t.prototype,"gridStyle",null),o([(0,p.property)(t.horizontalCellContentAlignmentProperty)],t.prototype,"horizontalCellContentAlignment",void 0),o([(0,p.property)(t.verticalCellContentAlignmentProperty)],t.prototype,"verticalCellContentAlignment",void 0),t}(f);t.Table=y,l.GlobalRegistry.defaultElements.register("Table",y,p.Versions.v1_5)},639:function(e,t){var i,n=this&&this.__extends||(i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},i(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.formatText=void 0;var r=function(){function e(e){this._regularExpression=e}return e.prototype.format=function(e,t){var i;if(t){for(var n=t;null!=(i=this._regularExpression.exec(t));)n=n.replace(i[0],this.internalFormat(e,i));return n}return t},e}(),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.internalFormat=function(e,t){var i=new Date(Date.parse(t[1])),n=void 0!==t[2]?t[2].toLowerCase():"compact";return"compact"!==n?i.toLocaleDateString(e,{day:"numeric",weekday:n,month:n,year:"numeric"}):i.toLocaleDateString()},t}(r),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.prototype.internalFormat=function(e,t){return new Date(Date.parse(t[1])).toLocaleTimeString(e,{hour:"numeric",minute:"2-digit"})},t}(r);t.formatText=function(e,t){for(var i=t,n=0,r=[new o(/\{{2}DATE\((\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:Z|(?:(?:-|\+)\d{2}:\d{2})))(?:, ?(COMPACT|LONG|SHORT))?\)\}{2}/g),new s(/\{{2}TIME\((\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:Z|(?:(?:-|\+)\d{2}:\d{2})))\)\}{2}/g)];n<r.length;n++)i=r[n].format(e,i);return i}},755:(e,t,i)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),t.debounce=t.addCancelSelectActionEventHandler=t.clearElementChildren=t.getScrollY=t.getScrollX=t.getFitStatus=t.truncate=t.truncateText=t.stringToCssColor=t.parseEnum=t.getEnumValueByName=t.parseBool=t.parseNumber=t.parseString=t.appendChild=t.generateUniqueId=t.isMobileOS=t.isInternetExplorer=void 0;var r=i(10),o=i(764);function s(e,t){for(var i in e){var n=parseInt(i,10);if(n>=0){var r=e[i];if(r&&"string"==typeof r&&r.toLowerCase()===t.toLowerCase())return n}}}function a(e,t,i,n,r){var o=function(){return t-e.scrollHeight>=-1};if(!o()){for(var s=function(e){for(var t=[],i=p(e,-1);i<e.length;)" "===e[i]&&t.push(i),i=p(e,i);return t}(i),a=0,l=s.length,c=0;a<l;){var u=Math.floor((a+l)/2);n(i,s[u]),o()?(c=s[u],a=u+1):l=u}if(n(i,c),r&&t-e.scrollHeight>=r-1){for(var d=p(i,c);d<i.length&&(n(i,d),o());)c=d,d=p(i,d);n(i,c)}}}t.isInternetExplorer=function(){return void 0!==window.document.documentMode},t.isMobileOS=function(){var e=window.navigator.userAgent;return!!e.match(/Android/i)||!!e.match(/iPad/i)||!!e.match(/iPhone/i)},t.generateUniqueId=function(){return"__ac-"+o.UUID.generate()},t.appendChild=function(e,t){t&&e.appendChild(t)},t.parseString=function(e,t){return"string"==typeof e?e:t},t.parseNumber=function(e,t){return"number"==typeof e?e:t},t.parseBool=function(e,t){if("boolean"==typeof e)return e;if("string"==typeof e)switch(e.toLowerCase()){case"true":return!0;case"false":return!1;default:return t}return t},t.getEnumValueByName=s,t.parseEnum=function(e,t,i){if(!t)return i;var n=s(e,t);return void 0!==n?n:i},t.stringToCssColor=function(e){if(e){var t=/#([0-9A-F]{2})([0-9A-F]{2})([0-9A-F]{2})([0-9A-F]{2})?/gi.exec(e);if(t&&t[4]){var i=parseInt(t[1],16)/255;return"rgba("+parseInt(t[2],16)+","+parseInt(t[3],16)+","+parseInt(t[4],16)+","+i+")"}}return e},t.truncateText=function(e,t,i){a(e,t,e.innerText,(function(t,i){e.innerText=t.substring(0,i)+"..."}),i)};var l="undefined"==typeof window||null===(n=window.trustedTypes)||void 0===n?void 0:n.createPolicy("adaptivecards#deprecatedExportedFunctionPolicy",{createHTML:function(e){return e}});function p(e,t){for(t+=1;t<e.length&&"<"===e[t];)for(;t<e.length&&">"!==e[t++];);return t}t.truncate=function(e,t,i){a(e,t,e.innerHTML,(function(t,i){var n,r=t.substring(0,i)+"...",o=null!==(n=null==l?void 0:l.createHTML(r))&&void 0!==n?n:r;e.innerHTML=o}),i)},t.getFitStatus=function(e,t){var i=e.offsetTop;return i+e.clientHeight<=t?r.ContainerFitStatus.FullyInContainer:i<t?r.ContainerFitStatus.Overflowing:r.ContainerFitStatus.FullyOutOfContainer},t.getScrollX=function(){return window.pageXOffset},t.getScrollY=function(){return window.pageYOffset},t.clearElementChildren=function(e){for(;e.firstChild;)e.removeChild(e.firstChild)},t.addCancelSelectActionEventHandler=function(e){e.onclick=function(e){e.preventDefault(),e.cancelBubble=!0}},t.debounce=function(e,t){var i;return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];i&&clearTimeout(i),i=setTimeout((function(){e.apply(void 0,n)}),t)}}},20:(e,t,i)=>{function n(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(e={},t={}){Object.keys(t).forEach((i=>{void 0===e[i]?e[i]=t[i]:n(t[i])&&n(e[i])&&Object.keys(t[i]).length>0&&r(e[i],t[i])}))}i.r(t),i.d(t,{A11y:()=>ce,Autoplay:()=>he,Controller:()=>pe,EffectCards:()=>Ve,EffectCoverflow:()=>Oe,EffectCreative:()=>Me,EffectCube:()=>Ae,EffectFade:()=>_e,EffectFlip:()=>Ie,FreeMode:()=>me,Grid:()=>ge,HashNavigation:()=>de,History:()=>ue,Keyboard:()=>Z,Lazy:()=>le,Manipulation:()=>Se,Mousewheel:()=>ee,Navigation:()=>ie,Pagination:()=>re,Parallax:()=>se,Scrollbar:()=>oe,Swiper:()=>K,Thumbs:()=>fe,Virtual:()=>Q,Zoom:()=>ae,default:()=>K});const o={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function s(){const e="undefined"!=typeof document?document:{};return r(e,o),e}const a={document:o,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function l(){const e="undefined"!=typeof window?window:{};return r(e,a),e}class p extends Array{constructor(e){"number"==typeof e?super(e):(super(...e||[]),function(e){const t=e.__proto__;Object.defineProperty(e,"__proto__",{get:()=>t,set(e){t.__proto__=e}})}(this))}}function c(e=[]){const t=[];return e.forEach((e=>{Array.isArray(e)?t.push(...c(e)):t.push(e)})),t}function u(e,t){return Array.prototype.filter.call(e,t)}function d(e,t){const i=l(),n=s();let r=[];if(!t&&e instanceof p)return e;if(!e)return new p(r);if("string"==typeof e){const i=e.trim();if(i.indexOf("<")>=0&&i.indexOf(">")>=0){let e="div";0===i.indexOf("<li")&&(e="ul"),0===i.indexOf("<tr")&&(e="tbody"),0!==i.indexOf("<td")&&0!==i.indexOf("<th")||(e="tr"),0===i.indexOf("<tbody")&&(e="table"),0===i.indexOf("<option")&&(e="select");const t=n.createElement(e);t.innerHTML=i;for(let e=0;e<t.childNodes.length;e+=1)r.push(t.childNodes[e])}else r=function(e,t){if("string"!=typeof e)return[e];const i=[],n=t.querySelectorAll(e);for(let e=0;e<n.length;e+=1)i.push(n[e]);return i}(e.trim(),t||n)}else if(e.nodeType||e===i||e===n)r.push(e);else if(Array.isArray(e)){if(e instanceof p)return e;r=e}return new p(function(e){const t=[];for(let i=0;i<e.length;i+=1)-1===t.indexOf(e[i])&&t.push(e[i]);return t}(r))}d.fn=p.prototype;const h="resize scroll".split(" ");function f(e){return function(...t){if(void 0===t[0]){for(let t=0;t<this.length;t+=1)h.indexOf(e)<0&&(e in this[t]?this[t][e]():d(this[t]).trigger(e));return this}return this.on(e,...t)}}f("click"),f("blur"),f("focus"),f("focusin"),f("focusout"),f("keyup"),f("keydown"),f("keypress"),f("submit"),f("change"),f("mousedown"),f("mousemove"),f("mouseup"),f("mouseenter"),f("mouseleave"),f("mouseout"),f("mouseover"),f("touchstart"),f("touchend"),f("touchmove"),f("resize"),f("scroll");const m={addClass:function(...e){const t=c(e.map((e=>e.split(" "))));return this.forEach((e=>{e.classList.add(...t)})),this},removeClass:function(...e){const t=c(e.map((e=>e.split(" "))));return this.forEach((e=>{e.classList.remove(...t)})),this},hasClass:function(...e){const t=c(e.map((e=>e.split(" "))));return u(this,(e=>t.filter((t=>e.classList.contains(t))).length>0)).length>0},toggleClass:function(...e){const t=c(e.map((e=>e.split(" "))));this.forEach((e=>{t.forEach((t=>{e.classList.toggle(t)}))}))},attr:function(e,t){if(1===arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let i=0;i<this.length;i+=1)if(2===arguments.length)this[i].setAttribute(e,t);else for(const t in e)this[i][t]=e[t],this[i].setAttribute(t,e[t]);return this},removeAttr:function(e){for(let t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(let t=0;t<this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(let t=0;t<this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?`${e}ms`:e;return this},on:function(...e){let[t,i,n,r]=e;function o(e){const t=e.target;if(!t)return;const r=e.target.dom7EventData||[];if(r.indexOf(e)<0&&r.unshift(e),d(t).is(i))n.apply(t,r);else{const e=d(t).parents();for(let t=0;t<e.length;t+=1)d(e[t]).is(i)&&n.apply(e[t],r)}}function s(e){const t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),n.apply(this,t)}"function"==typeof e[1]&&([t,n,r]=e,i=void 0),r||(r=!1);const a=t.split(" ");let l;for(let e=0;e<this.length;e+=1){const t=this[e];if(i)for(l=0;l<a.length;l+=1){const e=a[l];t.dom7LiveListeners||(t.dom7LiveListeners={}),t.dom7LiveListeners[e]||(t.dom7LiveListeners[e]=[]),t.dom7LiveListeners[e].push({listener:n,proxyListener:o}),t.addEventListener(e,o,r)}else for(l=0;l<a.length;l+=1){const e=a[l];t.dom7Listeners||(t.dom7Listeners={}),t.dom7Listeners[e]||(t.dom7Listeners[e]=[]),t.dom7Listeners[e].push({listener:n,proxyListener:s}),t.addEventListener(e,s,r)}}return this},off:function(...e){let[t,i,n,r]=e;"function"==typeof e[1]&&([t,n,r]=e,i=void 0),r||(r=!1);const o=t.split(" ");for(let e=0;e<o.length;e+=1){const t=o[e];for(let e=0;e<this.length;e+=1){const o=this[e];let s;if(!i&&o.dom7Listeners?s=o.dom7Listeners[t]:i&&o.dom7LiveListeners&&(s=o.dom7LiveListeners[t]),s&&s.length)for(let e=s.length-1;e>=0;e-=1){const i=s[e];n&&i.listener===n||n&&i.listener&&i.listener.dom7proxy&&i.listener.dom7proxy===n?(o.removeEventListener(t,i.proxyListener,r),s.splice(e,1)):n||(o.removeEventListener(t,i.proxyListener,r),s.splice(e,1))}}}return this},trigger:function(...e){const t=l(),i=e[0].split(" "),n=e[1];for(let r=0;r<i.length;r+=1){const o=i[r];for(let i=0;i<this.length;i+=1){const r=this[i];if(t.CustomEvent){const i=new t.CustomEvent(o,{detail:n,bubbles:!0,cancelable:!0});r.dom7EventData=e.filter(((e,t)=>t>0)),r.dispatchEvent(i),r.dom7EventData=[],delete r.dom7EventData}}}return this},transitionEnd:function(e){const t=this;return e&&t.on("transitionend",(function i(n){n.target===this&&(e.call(this,n),t.off("transitionend",i))})),this},outerWidth:function(e){if(this.length>0){if(e){const e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(this.length>0){if(e){const e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){const e=l();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(this.length>0){const e=l(),t=s(),i=this[0],n=i.getBoundingClientRect(),r=t.body,o=i.clientTop||r.clientTop||0,a=i.clientLeft||r.clientLeft||0,p=i===e?e.scrollY:i.scrollTop,c=i===e?e.scrollX:i.scrollLeft;return{top:n.top+p-o,left:n.left+c-a}}return null},css:function(e,t){const i=l();let n;if(1===arguments.length){if("string"!=typeof e){for(n=0;n<this.length;n+=1)for(const t in e)this[n].style[t]=e[t];return this}if(this[0])return i.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"==typeof e){for(n=0;n<this.length;n+=1)this[n].style[e]=t;return this}return this},each:function(e){return e?(this.forEach(((t,i)=>{e.apply(t,[t,i])})),this):this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(let t=0;t<this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(let t=0;t<this.length;t+=1)this[t].textContent=e;return this},is:function(e){const t=l(),i=s(),n=this[0];let r,o;if(!n||void 0===e)return!1;if("string"==typeof e){if(n.matches)return n.matches(e);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(e);if(n.msMatchesSelector)return n.msMatchesSelector(e);for(r=d(e),o=0;o<r.length;o+=1)if(r[o]===n)return!0;return!1}if(e===i)return n===i;if(e===t)return n===t;if(e.nodeType||e instanceof p){for(r=e.nodeType?[e]:e,o=0;o<r.length;o+=1)if(r[o]===n)return!0;return!1}return!1},index:function(){let e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},eq:function(e){if(void 0===e)return this;const t=this.length;if(e>t-1)return d([]);if(e<0){const i=t+e;return d(i<0?[]:[this[i]])}return d([this[e]])},append:function(...e){let t;const i=s();for(let n=0;n<e.length;n+=1){t=e[n];for(let e=0;e<this.length;e+=1)if("string"==typeof t){const n=i.createElement("div");for(n.innerHTML=t;n.firstChild;)this[e].appendChild(n.firstChild)}else if(t instanceof p)for(let i=0;i<t.length;i+=1)this[e].appendChild(t[i]);else this[e].appendChild(t)}return this},prepend:function(e){const t=s();let i,n;for(i=0;i<this.length;i+=1)if("string"==typeof e){const r=t.createElement("div");for(r.innerHTML=e,n=r.childNodes.length-1;n>=0;n-=1)this[i].insertBefore(r.childNodes[n],this[i].childNodes[0])}else if(e instanceof p)for(n=0;n<e.length;n+=1)this[i].insertBefore(e[n],this[i].childNodes[0]);else this[i].insertBefore(e,this[i].childNodes[0]);return this},next:function(e){return this.length>0?e?this[0].nextElementSibling&&d(this[0].nextElementSibling).is(e)?d([this[0].nextElementSibling]):d([]):this[0].nextElementSibling?d([this[0].nextElementSibling]):d([]):d([])},nextAll:function(e){const t=[];let i=this[0];if(!i)return d([]);for(;i.nextElementSibling;){const n=i.nextElementSibling;e?d(n).is(e)&&t.push(n):t.push(n),i=n}return d(t)},prev:function(e){if(this.length>0){const t=this[0];return e?t.previousElementSibling&&d(t.previousElementSibling).is(e)?d([t.previousElementSibling]):d([]):t.previousElementSibling?d([t.previousElementSibling]):d([])}return d([])},prevAll:function(e){const t=[];let i=this[0];if(!i)return d([]);for(;i.previousElementSibling;){const n=i.previousElementSibling;e?d(n).is(e)&&t.push(n):t.push(n),i=n}return d(t)},parent:function(e){const t=[];for(let i=0;i<this.length;i+=1)null!==this[i].parentNode&&(e?d(this[i].parentNode).is(e)&&t.push(this[i].parentNode):t.push(this[i].parentNode));return d(t)},parents:function(e){const t=[];for(let i=0;i<this.length;i+=1){let n=this[i].parentNode;for(;n;)e?d(n).is(e)&&t.push(n):t.push(n),n=n.parentNode}return d(t)},closest:function(e){let t=this;return void 0===e?d([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){const t=[];for(let i=0;i<this.length;i+=1){const n=this[i].querySelectorAll(e);for(let e=0;e<n.length;e+=1)t.push(n[e])}return d(t)},children:function(e){const t=[];for(let i=0;i<this.length;i+=1){const n=this[i].children;for(let i=0;i<n.length;i+=1)e&&!d(n[i]).is(e)||t.push(n[i])}return d(t)},filter:function(e){return d(u(this,e))},remove:function(){for(let e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};Object.keys(m).forEach((e=>{Object.defineProperty(d.fn,e,{value:m[e],writable:!0})}));const g=d;function y(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function v(){return Date.now()}function b(e,t){void 0===t&&(t="x");const i=l();let n,r,o;const s=function(e){const t=l();let i;return t.getComputedStyle&&(i=t.getComputedStyle(e,null)),!i&&e.currentStyle&&(i=e.currentStyle),i||(i=e.style),i}(e);return i.WebKitCSSMatrix?(r=s.transform||s.webkitTransform,r.split(",").length>6&&(r=r.split(", ").map((e=>e.replace(",","."))).join(", ")),o=new i.WebKitCSSMatrix("none"===r?"":r)):(o=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),n=o.toString().split(",")),"x"===t&&(r=i.WebKitCSSMatrix?o.m41:16===n.length?parseFloat(n[12]):parseFloat(n[4])),"y"===t&&(r=i.WebKitCSSMatrix?o.m42:16===n.length?parseFloat(n[13]):parseFloat(n[5])),r||0}function C(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function w(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function S(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const n=i<0||arguments.length<=i?void 0:arguments[i];if(null!=n&&!w(n)){const i=Object.keys(Object(n)).filter((e=>t.indexOf(e)<0));for(let t=0,r=i.length;t<r;t+=1){const r=i[t],o=Object.getOwnPropertyDescriptor(n,r);void 0!==o&&o.enumerable&&(C(e[r])&&C(n[r])?n[r].__swiper__?e[r]=n[r]:S(e[r],n[r]):!C(e[r])&&C(n[r])?(e[r]={},n[r].__swiper__?e[r]=n[r]:S(e[r],n[r])):e[r]=n[r])}}}return e}function E(e,t,i){e.style.setProperty(t,i)}function x(e){let{swiper:t,targetPosition:i,side:n}=e;const r=l(),o=-t.translate;let s,a=null;const p=t.params.speed;t.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(t.cssModeFrameID);const c=i>o?"next":"prev",u=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,d=()=>{s=(new Date).getTime(),null===a&&(a=s);const e=Math.max(Math.min((s-a)/p,1),0),l=.5-Math.cos(e*Math.PI)/2;let c=o+l*(i-o);if(u(c,i)&&(c=i),t.wrapperEl.scrollTo({[n]:c}),u(c,i))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout((()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[n]:c})})),void r.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=r.requestAnimationFrame(d)};d()}let P,_,A;function T(){return P||(P=function(){const e=l(),t=s();return{smoothScroll:t.documentElement&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch),passiveListener:function(){let t=!1;try{const i=Object.defineProperty({},"passive",{get(){t=!0}});e.addEventListener("testPassiveListener",null,i)}catch(e){}return t}(),gestures:"ongesturestart"in e}}()),P}const I={on(e,t,i){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof t)return n;const r=i?"unshift":"push";return e.split(" ").forEach((e=>{n.eventsListeners[e]||(n.eventsListeners[e]=[]),n.eventsListeners[e][r](t)})),n},once(e,t,i){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!=typeof t)return n;function r(){n.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];t.apply(n,o)}return r.__emitterProxy=t,n.on(e,r,i)},onAny(e,t){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;const n=t?"unshift":"push";return i.eventsAnyListeners.indexOf(e)<0&&i.eventsAnyListeners[n](e),i},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const i=t.eventsAnyListeners.indexOf(e);return i>=0&&t.eventsAnyListeners.splice(i,1),t},off(e,t){const i=this;return!i.eventsListeners||i.destroyed?i:i.eventsListeners?(e.split(" ").forEach((e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach(((n,r)=>{(n===t||n.__emitterProxy&&n.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)}))})),i):i},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,i,n;for(var r=arguments.length,o=new Array(r),s=0;s<r;s++)o[s]=arguments[s];return"string"==typeof o[0]||Array.isArray(o[0])?(t=o[0],i=o.slice(1,o.length),n=e):(t=o[0].events,i=o[0].data,n=o[0].context||e),i.unshift(n),(Array.isArray(t)?t:t.split(" ")).forEach((t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach((e=>{e.apply(n,[t,...i])})),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach((e=>{e.apply(n,i)}))})),e}},O={updateSize:function(){const e=this;let t,i;const n=e.$el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:n[0].clientWidth,i=void 0!==e.params.height&&null!==e.params.height?e.params.height:n[0].clientHeight,0===t&&e.isHorizontal()||0===i&&e.isVertical()||(t=t-parseInt(n.css("padding-left")||0,10)-parseInt(n.css("padding-right")||0,10),i=i-parseInt(n.css("padding-top")||0,10)-parseInt(n.css("padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(i)&&(i=0),Object.assign(e,{width:t,height:i,size:e.isHorizontal()?t:i}))},updateSlides:function(){const e=this;function t(t){return e.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}function i(e,i){return parseFloat(e.getPropertyValue(t(i))||0)}const n=e.params,{$wrapperEl:r,size:o,rtlTranslate:s,wrongRTL:a}=e,l=e.virtual&&n.virtual.enabled,p=l?e.virtual.slides.length:e.slides.length,c=r.children(`.${e.params.slideClass}`),u=l?e.virtual.slides.length:c.length;let d=[];const h=[],f=[];let m=n.slidesOffsetBefore;"function"==typeof m&&(m=n.slidesOffsetBefore.call(e));let g=n.slidesOffsetAfter;"function"==typeof g&&(g=n.slidesOffsetAfter.call(e));const y=e.snapGrid.length,v=e.slidesGrid.length;let b=n.spaceBetween,C=-m,w=0,S=0;if(void 0===o)return;"string"==typeof b&&b.indexOf("%")>=0&&(b=parseFloat(b.replace("%",""))/100*o),e.virtualSize=-b,s?c.css({marginLeft:"",marginBottom:"",marginTop:""}):c.css({marginRight:"",marginBottom:"",marginTop:""}),n.centeredSlides&&n.cssMode&&(E(e.wrapperEl,"--swiper-centered-offset-before",""),E(e.wrapperEl,"--swiper-centered-offset-after",""));const x=n.grid&&n.grid.rows>1&&e.grid;let P;x&&e.grid.initSlides(u);const _="auto"===n.slidesPerView&&n.breakpoints&&Object.keys(n.breakpoints).filter((e=>void 0!==n.breakpoints[e].slidesPerView)).length>0;for(let r=0;r<u;r+=1){P=0;const s=c.eq(r);if(x&&e.grid.updateSlide(r,s,u,t),"none"!==s.css("display")){if("auto"===n.slidesPerView){_&&(c[r].style[t("width")]="");const o=getComputedStyle(s[0]),a=s[0].style.transform,l=s[0].style.webkitTransform;if(a&&(s[0].style.transform="none"),l&&(s[0].style.webkitTransform="none"),n.roundLengths)P=e.isHorizontal()?s.outerWidth(!0):s.outerHeight(!0);else{const e=i(o,"width"),t=i(o,"padding-left"),n=i(o,"padding-right"),r=i(o,"margin-left"),a=i(o,"margin-right"),l=o.getPropertyValue("box-sizing");if(l&&"border-box"===l)P=e+r+a;else{const{clientWidth:i,offsetWidth:o}=s[0];P=e+t+n+r+a+(o-i)}}a&&(s[0].style.transform=a),l&&(s[0].style.webkitTransform=l),n.roundLengths&&(P=Math.floor(P))}else P=(o-(n.slidesPerView-1)*b)/n.slidesPerView,n.roundLengths&&(P=Math.floor(P)),c[r]&&(c[r].style[t("width")]=`${P}px`);c[r]&&(c[r].swiperSlideSize=P),f.push(P),n.centeredSlides?(C=C+P/2+w/2+b,0===w&&0!==r&&(C=C-o/2-b),0===r&&(C=C-o/2-b),Math.abs(C)<.001&&(C=0),n.roundLengths&&(C=Math.floor(C)),S%n.slidesPerGroup==0&&d.push(C),h.push(C)):(n.roundLengths&&(C=Math.floor(C)),(S-Math.min(e.params.slidesPerGroupSkip,S))%e.params.slidesPerGroup==0&&d.push(C),h.push(C),C=C+P+b),e.virtualSize+=P+b,w=P,S+=1}}if(e.virtualSize=Math.max(e.virtualSize,o)+g,s&&a&&("slide"===n.effect||"coverflow"===n.effect)&&r.css({width:`${e.virtualSize+n.spaceBetween}px`}),n.setWrapperSize&&r.css({[t("width")]:`${e.virtualSize+n.spaceBetween}px`}),x&&e.grid.updateWrapperSize(P,d,t),!n.centeredSlides){const t=[];for(let i=0;i<d.length;i+=1){let r=d[i];n.roundLengths&&(r=Math.floor(r)),d[i]<=e.virtualSize-o&&t.push(r)}d=t,Math.floor(e.virtualSize-o)-Math.floor(d[d.length-1])>1&&d.push(e.virtualSize-o)}if(0===d.length&&(d=[0]),0!==n.spaceBetween){const i=e.isHorizontal()&&s?"marginLeft":t("marginRight");c.filter(((e,t)=>!n.cssMode||t!==c.length-1)).css({[i]:`${b}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let e=0;f.forEach((t=>{e+=t+(n.spaceBetween?n.spaceBetween:0)})),e-=n.spaceBetween;const t=e-o;d=d.map((e=>e<0?-m:e>t?t+g:e))}if(n.centerInsufficientSlides){let e=0;if(f.forEach((t=>{e+=t+(n.spaceBetween?n.spaceBetween:0)})),e-=n.spaceBetween,e<o){const t=(o-e)/2;d.forEach(((e,i)=>{d[i]=e-t})),h.forEach(((e,i)=>{h[i]=e+t}))}}if(Object.assign(e,{slides:c,snapGrid:d,slidesGrid:h,slidesSizesGrid:f}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){E(e.wrapperEl,"--swiper-centered-offset-before",-d[0]+"px"),E(e.wrapperEl,"--swiper-centered-offset-after",e.size/2-f[f.length-1]/2+"px");const t=-e.snapGrid[0],i=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((e=>e+t)),e.slidesGrid=e.slidesGrid.map((e=>e+i))}if(u!==p&&e.emit("slidesLengthChange"),d.length!==y&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==v&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),!(l||n.cssMode||"slide"!==n.effect&&"fade"!==n.effect)){const t=`${n.containerModifierClass}backface-hidden`,i=e.$el.hasClass(t);u<=n.maxBackfaceHiddenSlides?i||e.$el.addClass(t):i&&e.$el.removeClass(t)}},updateAutoHeight:function(e){const t=this,i=[],n=t.virtual&&t.params.virtual.enabled;let r,o=0;"number"==typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const s=e=>n?t.slides.filter((t=>parseInt(t.getAttribute("data-swiper-slide-index"),10)===e))[0]:t.slides.eq(e)[0];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||g([])).each((e=>{i.push(e)}));else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const e=t.activeIndex+r;if(e>t.slides.length&&!n)break;i.push(s(e))}else i.push(s(t.activeIndex));for(r=0;r<i.length;r+=1)if(void 0!==i[r]){const e=i[r].offsetHeight;o=e>o?e:o}(o||0===o)&&t.$wrapperEl.css("height",`${o}px`)},updateSlidesOffset:function(){const e=this,t=e.slides;for(let i=0;i<t.length;i+=1)t[i].swiperSlideOffset=e.isHorizontal()?t[i].offsetLeft:t[i].offsetTop},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,i=t.params,{slides:n,rtlTranslate:r,snapGrid:o}=t;if(0===n.length)return;void 0===n[0].swiperSlideOffset&&t.updateSlidesOffset();let s=-e;r&&(s=e),n.removeClass(i.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(let e=0;e<n.length;e+=1){const a=n[e];let l=a.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(l-=n[0].swiperSlideOffset);const p=(s+(i.centeredSlides?t.minTranslate():0)-l)/(a.swiperSlideSize+i.spaceBetween),c=(s-o[0]+(i.centeredSlides?t.minTranslate():0)-l)/(a.swiperSlideSize+i.spaceBetween),u=-(s-l),d=u+t.slidesSizesGrid[e];(u>=0&&u<t.size-1||d>1&&d<=t.size||u<=0&&d>=t.size)&&(t.visibleSlides.push(a),t.visibleSlidesIndexes.push(e),n.eq(e).addClass(i.slideVisibleClass)),a.progress=r?-p:p,a.originalProgress=r?-c:c}t.visibleSlides=g(t.visibleSlides)},updateProgress:function(e){const t=this;if(void 0===e){const i=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*i||0}const i=t.params,n=t.maxTranslate()-t.minTranslate();let{progress:r,isBeginning:o,isEnd:s}=t;const a=o,l=s;0===n?(r=0,o=!0,s=!0):(r=(e-t.minTranslate())/n,o=r<=0,s=r>=1),Object.assign(t,{progress:r,isBeginning:o,isEnd:s}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),o&&!a&&t.emit("reachBeginning toEdge"),s&&!l&&t.emit("reachEnd toEdge"),(a&&!o||l&&!s)&&t.emit("fromEdge"),t.emit("progress",r)},updateSlidesClasses:function(){const e=this,{slides:t,params:i,$wrapperEl:n,activeIndex:r,realIndex:o}=e,s=e.virtual&&i.virtual.enabled;let a;t.removeClass(`${i.slideActiveClass} ${i.slideNextClass} ${i.slidePrevClass} ${i.slideDuplicateActiveClass} ${i.slideDuplicateNextClass} ${i.slideDuplicatePrevClass}`),a=s?e.$wrapperEl.find(`.${i.slideClass}[data-swiper-slide-index="${r}"]`):t.eq(r),a.addClass(i.slideActiveClass),i.loop&&(a.hasClass(i.slideDuplicateClass)?n.children(`.${i.slideClass}:not(.${i.slideDuplicateClass})[data-swiper-slide-index="${o}"]`).addClass(i.slideDuplicateActiveClass):n.children(`.${i.slideClass}.${i.slideDuplicateClass}[data-swiper-slide-index="${o}"]`).addClass(i.slideDuplicateActiveClass));let l=a.nextAll(`.${i.slideClass}`).eq(0).addClass(i.slideNextClass);i.loop&&0===l.length&&(l=t.eq(0),l.addClass(i.slideNextClass));let p=a.prevAll(`.${i.slideClass}`).eq(0).addClass(i.slidePrevClass);i.loop&&0===p.length&&(p=t.eq(-1),p.addClass(i.slidePrevClass)),i.loop&&(l.hasClass(i.slideDuplicateClass)?n.children(`.${i.slideClass}:not(.${i.slideDuplicateClass})[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicateNextClass):n.children(`.${i.slideClass}.${i.slideDuplicateClass}[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicateNextClass),p.hasClass(i.slideDuplicateClass)?n.children(`.${i.slideClass}:not(.${i.slideDuplicateClass})[data-swiper-slide-index="${p.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicatePrevClass):n.children(`.${i.slideClass}.${i.slideDuplicateClass}[data-swiper-slide-index="${p.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicatePrevClass)),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,i=t.rtlTranslate?t.translate:-t.translate,{slidesGrid:n,snapGrid:r,params:o,activeIndex:s,realIndex:a,snapIndex:l}=t;let p,c=e;if(void 0===c){for(let e=0;e<n.length;e+=1)void 0!==n[e+1]?i>=n[e]&&i<n[e+1]-(n[e+1]-n[e])/2?c=e:i>=n[e]&&i<n[e+1]&&(c=e+1):i>=n[e]&&(c=e);o.normalizeSlideIndex&&(c<0||void 0===c)&&(c=0)}if(r.indexOf(i)>=0)p=r.indexOf(i);else{const e=Math.min(o.slidesPerGroupSkip,c);p=e+Math.floor((c-e)/o.slidesPerGroup)}if(p>=r.length&&(p=r.length-1),c===s)return void(p!==l&&(t.snapIndex=p,t.emit("snapIndexChange")));const u=parseInt(t.slides.eq(c).attr("data-swiper-slide-index")||c,10);Object.assign(t,{snapIndex:p,realIndex:u,previousIndex:s,activeIndex:c}),t.emit("activeIndexChange"),t.emit("snapIndexChange"),a!==u&&t.emit("realIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&t.emit("slideChange")},updateClickedSlide:function(e){const t=this,i=t.params,n=g(e).closest(`.${i.slideClass}`)[0];let r,o=!1;if(n)for(let e=0;e<t.slides.length;e+=1)if(t.slides[e]===n){o=!0,r=e;break}if(!n||!o)return t.clickedSlide=void 0,void(t.clickedIndex=void 0);t.clickedSlide=n,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(g(n).attr("data-swiper-slide-index"),10):t.clickedIndex=r,i.slideToClickedSlide&&void 0!==t.clickedIndex&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}};function M(e){let{swiper:t,runCallbacks:i,direction:n,step:r}=e;const{activeIndex:o,previousIndex:s}=t;let a=n;if(a||(a=o>s?"next":o<s?"prev":"reset"),t.emit(`transition${r}`),i&&o!==s){if("reset"===a)return void t.emit(`slideResetTransition${r}`);t.emit(`slideChangeTransition${r}`),"next"===a?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`)}}const V={slideTo:function(e,t,i,n,r){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"number"!=typeof e&&"string"!=typeof e)throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if("string"==typeof e){const t=parseInt(e,10);if(!isFinite(t))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const o=this;let s=e;s<0&&(s=0);const{params:a,snapGrid:l,slidesGrid:p,previousIndex:c,activeIndex:u,rtlTranslate:d,wrapperEl:h,enabled:f}=o;if(o.animating&&a.preventInteractionOnTransition||!f&&!n&&!r)return!1;const m=Math.min(o.params.slidesPerGroupSkip,s);let g=m+Math.floor((s-m)/o.params.slidesPerGroup);g>=l.length&&(g=l.length-1),(u||a.initialSlide||0)===(c||0)&&i&&o.emit("beforeSlideChangeStart");const y=-l[g];if(o.updateProgress(y),a.normalizeSlideIndex)for(let e=0;e<p.length;e+=1){const t=-Math.floor(100*y),i=Math.floor(100*p[e]),n=Math.floor(100*p[e+1]);void 0!==p[e+1]?t>=i&&t<n-(n-i)/2?s=e:t>=i&&t<n&&(s=e+1):t>=i&&(s=e)}if(o.initialized&&s!==u){if(!o.allowSlideNext&&y<o.translate&&y<o.minTranslate())return!1;if(!o.allowSlidePrev&&y>o.translate&&y>o.maxTranslate()&&(u||0)!==s)return!1}let v;if(v=s>u?"next":s<u?"prev":"reset",d&&-y===o.translate||!d&&y===o.translate)return o.updateActiveIndex(s),a.autoHeight&&o.updateAutoHeight(),o.updateSlidesClasses(),"slide"!==a.effect&&o.setTranslate(y),"reset"!==v&&(o.transitionStart(i,v),o.transitionEnd(i,v)),!1;if(a.cssMode){const e=o.isHorizontal(),i=d?y:-y;if(0===t){const t=o.virtual&&o.params.virtual.enabled;t&&(o.wrapperEl.style.scrollSnapType="none",o._immediateVirtual=!0),h[e?"scrollLeft":"scrollTop"]=i,t&&requestAnimationFrame((()=>{o.wrapperEl.style.scrollSnapType="",o._swiperImmediateVirtual=!1}))}else{if(!o.support.smoothScroll)return x({swiper:o,targetPosition:i,side:e?"left":"top"}),!0;h.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}return o.setTransition(t),o.setTranslate(y),o.updateActiveIndex(s),o.updateSlidesClasses(),o.emit("beforeTransitionStart",t,n),o.transitionStart(i,v),0===t?o.transitionEnd(i,v):o.animating||(o.animating=!0,o.onSlideToWrapperTransitionEnd||(o.onSlideToWrapperTransitionEnd=function(e){o&&!o.destroyed&&e.target===this&&(o.$wrapperEl[0].removeEventListener("transitionend",o.onSlideToWrapperTransitionEnd),o.$wrapperEl[0].removeEventListener("webkitTransitionEnd",o.onSlideToWrapperTransitionEnd),o.onSlideToWrapperTransitionEnd=null,delete o.onSlideToWrapperTransitionEnd,o.transitionEnd(i,v))}),o.$wrapperEl[0].addEventListener("transitionend",o.onSlideToWrapperTransitionEnd),o.$wrapperEl[0].addEventListener("webkitTransitionEnd",o.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,n){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"string"==typeof e){const t=parseInt(e,10);if(!isFinite(t))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const r=this;let o=e;return r.params.loop&&(o+=r.loopedSlides),r.slideTo(o,t,i,n)},slideNext:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);const n=this,{animating:r,enabled:o,params:s}=n;if(!o)return n;let a=s.slidesPerGroup;"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(a=Math.max(n.slidesPerViewDynamic("current",!0),1));const l=n.activeIndex<s.slidesPerGroupSkip?1:a;if(s.loop){if(r&&s.loopPreventsSlide)return!1;n.loopFix(),n._clientLeft=n.$wrapperEl[0].clientLeft}return s.rewind&&n.isEnd?n.slideTo(0,e,t,i):n.slideTo(n.activeIndex+l,e,t,i)},slidePrev:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);const n=this,{params:r,animating:o,snapGrid:s,slidesGrid:a,rtlTranslate:l,enabled:p}=n;if(!p)return n;if(r.loop){if(o&&r.loopPreventsSlide)return!1;n.loopFix(),n._clientLeft=n.$wrapperEl[0].clientLeft}function c(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const u=c(l?n.translate:-n.translate),d=s.map((e=>c(e)));let h=s[d.indexOf(u)-1];if(void 0===h&&r.cssMode){let e;s.forEach(((t,i)=>{u>=t&&(e=i)})),void 0!==e&&(h=s[e>0?e-1:e])}let f=0;if(void 0!==h&&(f=a.indexOf(h),f<0&&(f=n.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(f=f-n.slidesPerViewDynamic("previous",!0)+1,f=Math.max(f,0))),r.rewind&&n.isBeginning){const r=n.params.virtual&&n.params.virtual.enabled&&n.virtual?n.virtual.slides.length-1:n.slides.length-1;return n.slideTo(r,e,t,i)}return n.slideTo(f,e,t,i)},slideReset:function(e,t,i){return void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,n){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===n&&(n=.5);const r=this;let o=r.activeIndex;const s=Math.min(r.params.slidesPerGroupSkip,o),a=s+Math.floor((o-s)/r.params.slidesPerGroup),l=r.rtlTranslate?r.translate:-r.translate;if(l>=r.snapGrid[a]){const e=r.snapGrid[a];l-e>(r.snapGrid[a+1]-e)*n&&(o+=r.params.slidesPerGroup)}else{const e=r.snapGrid[a-1];l-e<=(r.snapGrid[a]-e)*n&&(o-=r.params.slidesPerGroup)}return o=Math.max(o,0),o=Math.min(o,r.slidesGrid.length-1),r.slideTo(o,e,t,i)},slideToClickedSlide:function(){const e=this,{params:t,$wrapperEl:i}=e,n="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let r,o=e.clickedIndex;if(t.loop){if(e.animating)return;r=parseInt(g(e.clickedSlide).attr("data-swiper-slide-index"),10),t.centeredSlides?o<e.loopedSlides-n/2||o>e.slides.length-e.loopedSlides+n/2?(e.loopFix(),o=i.children(`.${t.slideClass}[data-swiper-slide-index="${r}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),y((()=>{e.slideTo(o)}))):e.slideTo(o):o>e.slides.length-n?(e.loopFix(),o=i.children(`.${t.slideClass}[data-swiper-slide-index="${r}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),y((()=>{e.slideTo(o)}))):e.slideTo(o)}else e.slideTo(o)}},k={loopCreate:function(){const e=this,t=s(),{params:i,$wrapperEl:n}=e,r=n.children().length>0?g(n.children()[0].parentNode):n;r.children(`.${i.slideClass}.${i.slideDuplicateClass}`).remove();let o=r.children(`.${i.slideClass}`);if(i.loopFillGroupWithBlank){const e=i.slidesPerGroup-o.length%i.slidesPerGroup;if(e!==i.slidesPerGroup){for(let n=0;n<e;n+=1){const e=g(t.createElement("div")).addClass(`${i.slideClass} ${i.slideBlankClass}`);r.append(e)}o=r.children(`.${i.slideClass}`)}}"auto"!==i.slidesPerView||i.loopedSlides||(i.loopedSlides=o.length),e.loopedSlides=Math.ceil(parseFloat(i.loopedSlides||i.slidesPerView,10)),e.loopedSlides+=i.loopAdditionalSlides,e.loopedSlides>o.length&&(e.loopedSlides=o.length);const a=[],l=[];o.each(((t,i)=>{const n=g(t);i<e.loopedSlides&&l.push(t),i<o.length&&i>=o.length-e.loopedSlides&&a.push(t),n.attr("data-swiper-slide-index",i)}));for(let e=0;e<l.length;e+=1)r.append(g(l[e].cloneNode(!0)).addClass(i.slideDuplicateClass));for(let e=a.length-1;e>=0;e-=1)r.prepend(g(a[e].cloneNode(!0)).addClass(i.slideDuplicateClass))},loopFix:function(){const e=this;e.emit("beforeLoopFix");const{activeIndex:t,slides:i,loopedSlides:n,allowSlidePrev:r,allowSlideNext:o,snapGrid:s,rtlTranslate:a}=e;let l;e.allowSlidePrev=!0,e.allowSlideNext=!0;const p=-s[t]-e.getTranslate();t<n?(l=i.length-3*n+t,l+=n,e.slideTo(l,0,!1,!0)&&0!==p&&e.setTranslate((a?-e.translate:e.translate)-p)):t>=i.length-n&&(l=-i.length+t+n,l+=n,e.slideTo(l,0,!1,!0)&&0!==p&&e.setTranslate((a?-e.translate:e.translate)-p)),e.allowSlidePrev=r,e.allowSlideNext=o,e.emit("loopFix")},loopDestroy:function(){const{$wrapperEl:e,params:t,slides:i}=this;e.children(`.${t.slideClass}.${t.slideDuplicateClass},.${t.slideClass}.${t.slideBlankClass}`).remove(),i.removeAttr("data-swiper-slide-index")}};function z(e){const t=this,i=s(),n=l(),r=t.touchEventsData,{params:o,touches:a,enabled:p}=t;if(!p)return;if(t.animating&&o.preventInteractionOnTransition)return;!t.animating&&o.cssMode&&o.loop&&t.loopFix();let c=e;c.originalEvent&&(c=c.originalEvent);let u=g(c.target);if("wrapper"===o.touchEventsTarget&&!u.closest(t.wrapperEl).length)return;if(r.isTouchEvent="touchstart"===c.type,!r.isTouchEvent&&"which"in c&&3===c.which)return;if(!r.isTouchEvent&&"button"in c&&c.button>0)return;if(r.isTouched&&r.isMoved)return;o.noSwipingClass&&""!==o.noSwipingClass&&c.target&&c.target.shadowRoot&&e.path&&e.path[0]&&(u=g(e.path[0]));const d=o.noSwipingSelector?o.noSwipingSelector:`.${o.noSwipingClass}`,h=!(!c.target||!c.target.shadowRoot);if(o.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===s()||i===l())return null;i.assignedSlot&&(i=i.assignedSlot);const n=i.closest(e);return n||i.getRootNode?n||t(i.getRootNode().host):null}(t)}(d,u[0]):u.closest(d)[0]))return void(t.allowClick=!0);if(o.swipeHandler&&!u.closest(o.swipeHandler)[0])return;a.currentX="touchstart"===c.type?c.targetTouches[0].pageX:c.pageX,a.currentY="touchstart"===c.type?c.targetTouches[0].pageY:c.pageY;const f=a.currentX,m=a.currentY,y=o.edgeSwipeDetection||o.iOSEdgeSwipeDetection,b=o.edgeSwipeThreshold||o.iOSEdgeSwipeThreshold;if(y&&(f<=b||f>=n.innerWidth-b)){if("prevent"!==y)return;e.preventDefault()}if(Object.assign(r,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=f,a.startY=m,r.touchStartTime=v(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,o.threshold>0&&(r.allowThresholdMove=!1),"touchstart"!==c.type){let e=!0;u.is(r.focusableElements)&&(e=!1,"SELECT"===u[0].nodeName&&(r.isTouched=!1)),i.activeElement&&g(i.activeElement).is(r.focusableElements)&&i.activeElement!==u[0]&&i.activeElement.blur();const n=e&&t.allowTouchMove&&o.touchStartPreventDefault;!o.touchStartForcePreventDefault&&!n||u[0].isContentEditable||c.preventDefault()}t.params.freeMode&&t.params.freeMode.enabled&&t.freeMode&&t.animating&&!o.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",c)}function L(e){const t=s(),i=this,n=i.touchEventsData,{params:r,touches:o,rtlTranslate:a,enabled:l}=i;if(!l)return;let p=e;if(p.originalEvent&&(p=p.originalEvent),!n.isTouched)return void(n.startMoving&&n.isScrolling&&i.emit("touchMoveOpposite",p));if(n.isTouchEvent&&"touchmove"!==p.type)return;const c="touchmove"===p.type&&p.targetTouches&&(p.targetTouches[0]||p.changedTouches[0]),u="touchmove"===p.type?c.pageX:p.pageX,d="touchmove"===p.type?c.pageY:p.pageY;if(p.preventedByNestedSwiper)return o.startX=u,void(o.startY=d);if(!i.allowTouchMove)return g(p.target).is(n.focusableElements)||(i.allowClick=!1),void(n.isTouched&&(Object.assign(o,{startX:u,startY:d,currentX:u,currentY:d}),n.touchStartTime=v()));if(n.isTouchEvent&&r.touchReleaseOnEdges&&!r.loop)if(i.isVertical()){if(d<o.startY&&i.translate<=i.maxTranslate()||d>o.startY&&i.translate>=i.minTranslate())return n.isTouched=!1,void(n.isMoved=!1)}else if(u<o.startX&&i.translate<=i.maxTranslate()||u>o.startX&&i.translate>=i.minTranslate())return;if(n.isTouchEvent&&t.activeElement&&p.target===t.activeElement&&g(p.target).is(n.focusableElements))return n.isMoved=!0,void(i.allowClick=!1);if(n.allowTouchCallbacks&&i.emit("touchMove",p),p.targetTouches&&p.targetTouches.length>1)return;o.currentX=u,o.currentY=d;const h=o.currentX-o.startX,f=o.currentY-o.startY;if(i.params.threshold&&Math.sqrt(h**2+f**2)<i.params.threshold)return;if(void 0===n.isScrolling){let e;i.isHorizontal()&&o.currentY===o.startY||i.isVertical()&&o.currentX===o.startX?n.isScrolling=!1:h*h+f*f>=25&&(e=180*Math.atan2(Math.abs(f),Math.abs(h))/Math.PI,n.isScrolling=i.isHorizontal()?e>r.touchAngle:90-e>r.touchAngle)}if(n.isScrolling&&i.emit("touchMoveOpposite",p),void 0===n.startMoving&&(o.currentX===o.startX&&o.currentY===o.startY||(n.startMoving=!0)),n.isScrolling)return void(n.isTouched=!1);if(!n.startMoving)return;i.allowClick=!1,!r.cssMode&&p.cancelable&&p.preventDefault(),r.touchMoveStopPropagation&&!r.nested&&p.stopPropagation(),n.isMoved||(r.loop&&!r.cssMode&&i.loopFix(),n.startTranslate=i.getTranslate(),i.setTransition(0),i.animating&&i.$wrapperEl.trigger("webkitTransitionEnd transitionend"),n.allowMomentumBounce=!1,!r.grabCursor||!0!==i.allowSlideNext&&!0!==i.allowSlidePrev||i.setGrabCursor(!0),i.emit("sliderFirstMove",p)),i.emit("sliderMove",p),n.isMoved=!0;let m=i.isHorizontal()?h:f;o.diff=m,m*=r.touchRatio,a&&(m=-m),i.swipeDirection=m>0?"prev":"next",n.currentTranslate=m+n.startTranslate;let y=!0,b=r.resistanceRatio;if(r.touchReleaseOnEdges&&(b=0),m>0&&n.currentTranslate>i.minTranslate()?(y=!1,r.resistance&&(n.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+n.startTranslate+m)**b)):m<0&&n.currentTranslate<i.maxTranslate()&&(y=!1,r.resistance&&(n.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-n.startTranslate-m)**b)),y&&(p.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),i.allowSlidePrev||i.allowSlideNext||(n.currentTranslate=n.startTranslate),r.threshold>0){if(!(Math.abs(m)>r.threshold||n.allowThresholdMove))return void(n.currentTranslate=n.startTranslate);if(!n.allowThresholdMove)return n.allowThresholdMove=!0,o.startX=o.currentX,o.startY=o.currentY,n.currentTranslate=n.startTranslate,void(o.diff=i.isHorizontal()?o.currentX-o.startX:o.currentY-o.startY)}r.followFinger&&!r.cssMode&&((r.freeMode&&r.freeMode.enabled&&i.freeMode||r.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),i.params.freeMode&&r.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(n.currentTranslate),i.setTranslate(n.currentTranslate))}function D(e){const t=this,i=t.touchEventsData,{params:n,touches:r,rtlTranslate:o,slidesGrid:s,enabled:a}=t;if(!a)return;let l=e;if(l.originalEvent&&(l=l.originalEvent),i.allowTouchCallbacks&&t.emit("touchEnd",l),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&n.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);n.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const p=v(),c=p-i.touchStartTime;if(t.allowClick){const e=l.path||l.composedPath&&l.composedPath();t.updateClickedSlide(e&&e[0]||l.target),t.emit("tap click",l),c<300&&p-i.lastClickTime<300&&t.emit("doubleTap doubleClick",l)}if(i.lastClickTime=v(),y((()=>{t.destroyed||(t.allowClick=!0)})),!i.isTouched||!i.isMoved||!t.swipeDirection||0===r.diff||i.currentTranslate===i.startTranslate)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);let u;if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,u=n.followFinger?o?t.translate:-t.translate:-i.currentTranslate,n.cssMode)return;if(t.params.freeMode&&n.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:u});let d=0,h=t.slidesSizesGrid[0];for(let e=0;e<s.length;e+=e<n.slidesPerGroupSkip?1:n.slidesPerGroup){const t=e<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;void 0!==s[e+t]?u>=s[e]&&u<s[e+t]&&(d=e,h=s[e+t]-s[e]):u>=s[e]&&(d=e,h=s[s.length-1]-s[s.length-2])}let f=null,m=null;n.rewind&&(t.isBeginning?m=t.params.virtual&&t.params.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(f=0));const g=(u-s[d])/h,b=d<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;if(c>n.longSwipesMs){if(!n.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(g>=n.longSwipesRatio?t.slideTo(n.rewind&&t.isEnd?f:d+b):t.slideTo(d)),"prev"===t.swipeDirection&&(g>1-n.longSwipesRatio?t.slideTo(d+b):null!==m&&g<0&&Math.abs(g)>n.longSwipesRatio?t.slideTo(m):t.slideTo(d))}else{if(!n.shortSwipes)return void t.slideTo(t.activeIndex);!t.navigation||l.target!==t.navigation.nextEl&&l.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(null!==f?f:d+b),"prev"===t.swipeDirection&&t.slideTo(null!==m?m:d)):l.target===t.navigation.nextEl?t.slideTo(d+b):t.slideTo(d)}}function N(){const e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:n,allowSlidePrev:r,snapGrid:o}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=r,e.allowSlideNext=n,e.params.watchOverflow&&o!==e.snapGrid&&e.checkOverflow()}function R(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function $(){const e=this,{wrapperEl:t,rtlTranslate:i,enabled:n}=e;if(!n)return;let r;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const o=e.maxTranslate()-e.minTranslate();r=0===o?0:(e.translate-e.minTranslate())/o,r!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}let B=!1;function H(){}const j=(e,t)=>{const i=s(),{params:n,touchEvents:r,el:o,wrapperEl:a,device:l,support:p}=e,c=!!n.nested,u="on"===t?"addEventListener":"removeEventListener",d=t;if(p.touch){const t=!("touchstart"!==r.start||!p.passiveListener||!n.passiveListeners)&&{passive:!0,capture:!1};o[u](r.start,e.onTouchStart,t),o[u](r.move,e.onTouchMove,p.passiveListener?{passive:!1,capture:c}:c),o[u](r.end,e.onTouchEnd,t),r.cancel&&o[u](r.cancel,e.onTouchEnd,t)}else o[u](r.start,e.onTouchStart,!1),i[u](r.move,e.onTouchMove,c),i[u](r.end,e.onTouchEnd,!1);(n.preventClicks||n.preventClicksPropagation)&&o[u]("click",e.onClick,!0),n.cssMode&&a[u]("scroll",e.onScroll),n.updateOnWindowResize?e[d](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",N,!0):e[d]("observerUpdate",N,!0)},F={attachEvents:function(){const e=this,t=s(),{params:i,support:n}=e;e.onTouchStart=z.bind(e),e.onTouchMove=L.bind(e),e.onTouchEnd=D.bind(e),i.cssMode&&(e.onScroll=$.bind(e)),e.onClick=R.bind(e),n.touch&&!B&&(t.addEventListener("touchstart",H),B=!0),j(e,"on")},detachEvents:function(){j(this,"off")}},W=(e,t)=>e.grid&&t.grid&&t.grid.rows>1,G={addClasses:function(){const e=this,{classNames:t,params:i,rtl:n,$el:r,device:o,support:s}=e,a=function(e,t){const i=[];return e.forEach((e=>{"object"==typeof e?Object.keys(e).forEach((n=>{e[n]&&i.push(t+n)})):"string"==typeof e&&i.push(t+e)})),i}(["initialized",i.direction,{"pointer-events":!s.touch},{"free-mode":e.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:n},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&"column"===i.grid.fill},{android:o.android},{ios:o.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);t.push(...a),r.addClass([...t].join(" ")),e.emitContainerClasses()},removeClasses:function(){const{$el:e,classNames:t}=this;e.removeClass(t.join(" ")),this.emitContainerClasses()}},q={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};function U(e,t){return function(i){void 0===i&&(i={});const n=Object.keys(i)[0],r=i[n];"object"==typeof r&&null!==r?(["navigation","pagination","scrollbar"].indexOf(n)>=0&&!0===e[n]&&(e[n]={auto:!0}),n in e&&"enabled"in r?(!0===e[n]&&(e[n]={enabled:!0}),"object"!=typeof e[n]||"enabled"in e[n]||(e[n].enabled=!0),e[n]||(e[n]={enabled:!1}),S(t,i)):S(t,i)):S(t,i)}}const J={eventsEmitter:I,update:O,translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:i,translate:n,$wrapperEl:r}=this;if(t.virtualTranslate)return i?-n:n;if(t.cssMode)return n;let o=b(r[0],e);return i&&(o=-o),o||0},setTranslate:function(e,t){const i=this,{rtlTranslate:n,params:r,$wrapperEl:o,wrapperEl:s,progress:a}=i;let l,p=0,c=0;i.isHorizontal()?p=n?-e:e:c=e,r.roundLengths&&(p=Math.floor(p),c=Math.floor(c)),r.cssMode?s[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-p:-c:r.virtualTranslate||o.transform(`translate3d(${p}px, ${c}px, 0px)`),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?p:c;const u=i.maxTranslate()-i.minTranslate();l=0===u?0:(e-i.minTranslate())/u,l!==a&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,n,r){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===n&&(n=!0);const o=this,{params:s,wrapperEl:a}=o;if(o.animating&&s.preventInteractionOnTransition)return!1;const l=o.minTranslate(),p=o.maxTranslate();let c;if(c=n&&e>l?l:n&&e<p?p:e,o.updateProgress(c),s.cssMode){const e=o.isHorizontal();if(0===t)a[e?"scrollLeft":"scrollTop"]=-c;else{if(!o.support.smoothScroll)return x({swiper:o,targetPosition:-c,side:e?"left":"top"}),!0;a.scrollTo({[e?"left":"top"]:-c,behavior:"smooth"})}return!0}return 0===t?(o.setTransition(0),o.setTranslate(c),i&&(o.emit("beforeTransitionStart",t,r),o.emit("transitionEnd"))):(o.setTransition(t),o.setTranslate(c),i&&(o.emit("beforeTransitionStart",t,r),o.emit("transitionStart")),o.animating||(o.animating=!0,o.onTranslateToWrapperTransitionEnd||(o.onTranslateToWrapperTransitionEnd=function(e){o&&!o.destroyed&&e.target===this&&(o.$wrapperEl[0].removeEventListener("transitionend",o.onTranslateToWrapperTransitionEnd),o.$wrapperEl[0].removeEventListener("webkitTransitionEnd",o.onTranslateToWrapperTransitionEnd),o.onTranslateToWrapperTransitionEnd=null,delete o.onTranslateToWrapperTransitionEnd,i&&o.emit("transitionEnd"))}),o.$wrapperEl[0].addEventListener("transitionend",o.onTranslateToWrapperTransitionEnd),o.$wrapperEl[0].addEventListener("webkitTransitionEnd",o.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){const i=this;i.params.cssMode||i.$wrapperEl.transition(e),i.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const i=this,{params:n}=i;n.cssMode||(n.autoHeight&&i.updateAutoHeight(),M({swiper:i,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const i=this,{params:n}=i;i.animating=!1,n.cssMode||(i.setTransition(0),M({swiper:i,runCallbacks:e,direction:t,step:"End"}))}},slide:V,loop:k,grabCursor:{setGrabCursor:function(e){const t=this;if(t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;i.style.cursor="move",i.style.cursor=e?"grabbing":"grab"},unsetGrabCursor:function(){const e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}},events:F,breakpoints:{setBreakpoint:function(){const e=this,{activeIndex:t,initialized:i,loopedSlides:n=0,params:r,$el:o}=e,s=r.breakpoints;if(!s||s&&0===Object.keys(s).length)return;const a=e.getBreakpoint(s,e.params.breakpointsBase,e.el);if(!a||e.currentBreakpoint===a)return;const l=(a in s?s[a]:void 0)||e.originalParams,p=W(e,r),c=W(e,l),u=r.enabled;p&&!c?(o.removeClass(`${r.containerModifierClass}grid ${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&c&&(o.addClass(`${r.containerModifierClass}grid`),(l.grid.fill&&"column"===l.grid.fill||!l.grid.fill&&"column"===r.grid.fill)&&o.addClass(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach((t=>{const i=r[t]&&r[t].enabled,n=l[t]&&l[t].enabled;i&&!n&&e[t].disable(),!i&&n&&e[t].enable()}));const d=l.direction&&l.direction!==r.direction,h=r.loop&&(l.slidesPerView!==r.slidesPerView||d);d&&i&&e.changeDirection(),S(e.params,l);const f=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),u&&!f?e.disable():!u&&f&&e.enable(),e.currentBreakpoint=a,e.emit("_beforeBreakpoint",l),h&&i&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-n+e.loopedSlides,0,!1)),e.emit("breakpoint",l)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let n=!1;const r=l(),o="window"===t?r.innerHeight:i.clientHeight,s=Object.keys(e).map((e=>{if("string"==typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:o*t,point:e}}return{value:e,point:e}}));s.sort(((e,t)=>parseInt(e.value,10)-parseInt(t.value,10)));for(let e=0;e<s.length;e+=1){const{point:o,value:a}=s[e];"window"===t?r.matchMedia(`(min-width: ${a}px)`).matches&&(n=o):a<=i.clientWidth&&(n=o)}return n||"max"}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:i}=e,{slidesOffsetBefore:n}=i;if(n){const t=e.slides.length-1,i=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*n;e.isLocked=e.size>i}else e.isLocked=1===e.snapGrid.length;!0===i.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===i.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:G,images:{loadImage:function(e,t,i,n,r,o){const s=l();let a;function p(){o&&o()}g(e).parent("picture")[0]||e.complete&&r?p():t?(a=new s.Image,a.onload=p,a.onerror=p,n&&(a.sizes=n),i&&(a.srcset=i),t&&(a.src=t)):p()},preloadImages:function(){const e=this;function t(){null!=e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(let i=0;i<e.imagesToLoad.length;i+=1){const n=e.imagesToLoad[i];e.loadImage(n,n.currentSrc||n.getAttribute("src"),n.srcset||n.getAttribute("srcset"),n.sizes||n.getAttribute("sizes"),!0,t)}}}},X={};class Y{constructor(){let e,t;for(var i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];if(1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?t=n[0]:[e,t]=n,t||(t={}),t=S({},t),e&&!t.el&&(t.el=e),t.el&&g(t.el).length>1){const e=[];return g(t.el).each((i=>{const n=S({},t,{el:i});e.push(new Y(n))})),e}const o=this;var s;o.__swiper__=!0,o.support=T(),o.device=(void 0===(s={userAgent:t.userAgent})&&(s={}),_||(_=function(e){let{userAgent:t}=void 0===e?{}:e;const i=T(),n=l(),r=n.navigator.platform,o=t||n.navigator.userAgent,s={ios:!1,android:!1},a=n.screen.width,p=n.screen.height,c=o.match(/(Android);?[\s\/]+([\d.]+)?/);let u=o.match(/(iPad).*OS\s([\d_]+)/);const d=o.match(/(iPod)(.*OS\s([\d_]+))?/),h=!u&&o.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="Win32"===r;let m="MacIntel"===r;return!u&&m&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${a}x${p}`)>=0&&(u=o.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),m=!1),c&&!f&&(s.os="android",s.android=!0),(u||h||d)&&(s.os="ios",s.ios=!0),s}(s)),_),o.browser=(A||(A=function(){const e=l();return{isSafari:function(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),A),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],t.modules&&Array.isArray(t.modules)&&o.modules.push(...t.modules);const a={};o.modules.forEach((e=>{e({swiper:o,extendParams:U(t,a),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})}));const p=S({},q,a);return o.params=S({},p,X,t),o.originalParams=S({},o.params),o.passedParams=S({},t),o.params&&o.params.on&&Object.keys(o.params.on).forEach((e=>{o.on(e,o.params.on[e])})),o.params&&o.params.onAny&&o.onAny(o.params.onAny),o.$=g,Object.assign(o,{enabled:o.params.enabled,el:e,classNames:[],slides:g(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===o.params.direction,isVertical:()=>"vertical"===o.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEvents:function(){const e=["touchstart","touchmove","touchend","touchcancel"],t=["pointerdown","pointermove","pointerup"];return o.touchEventsTouch={start:e[0],move:e[1],end:e[2],cancel:e[3]},o.touchEventsDesktop={start:t[0],move:t[1],end:t[2]},o.support.touch||!o.params.simulateTouch?o.touchEventsTouch:o.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:v(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const n=i.minTranslate(),r=(i.maxTranslate()-n)*e+n;i.translateTo(r,void 0===t?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter((t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter((e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass))).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.each((i=>{const n=e.getSlideClasses(i);t.push({slideEl:i,classNames:n}),e.emit("_slideClass",i,n)})),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:i,slides:n,slidesGrid:r,slidesSizesGrid:o,size:s,activeIndex:a}=this;let l=1;if(i.centeredSlides){let e,t=n[a].swiperSlideSize;for(let i=a+1;i<n.length;i+=1)n[i]&&!e&&(t+=n[i].swiperSlideSize,l+=1,t>s&&(e=!0));for(let i=a-1;i>=0;i-=1)n[i]&&!e&&(t+=n[i].swiperSlideSize,l+=1,t>s&&(e=!0))}else if("current"===e)for(let e=a+1;e<n.length;e+=1)(t?r[e]+o[e]-r[a]<s:r[e]-r[a]<s)&&(l+=1);else for(let e=a-1;e>=0;e-=1)r[a]-r[e]<s&&(l+=1);return l}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;function n(){const t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}let r;i.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode&&e.params.freeMode.enabled?(n(),e.params.autoHeight&&e.updateAutoHeight()):(r=("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),r||n()),i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const i=this,n=i.params.direction;return e||(e="horizontal"===n?"vertical":"horizontal"),e===n||"horizontal"!==e&&"vertical"!==e||(i.$el.removeClass(`${i.params.containerModifierClass}${n}`).addClass(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.each((t=>{"vertical"===e?t.style.width="":t.style.height=""})),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.$el.addClass(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.$el.removeClass(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;const i=g(e||t.params.el);if(!(e=i[0]))return!1;e.swiper=t;const n=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let r=(()=>{if(e&&e.shadowRoot&&e.shadowRoot.querySelector){const t=g(e.shadowRoot.querySelector(n()));return t.children=e=>i.children(e),t}return i.children?i.children(n()):g(i).children(n())})();if(0===r.length&&t.params.createElements){const e=s().createElement("div");r=g(e),e.className=t.params.wrapperClass,i.append(e),i.children(`.${t.params.slideClass}`).each((e=>{r.append(e)}))}return Object.assign(t,{$el:i,el:e,$wrapperEl:r,wrapperEl:r[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction")),wrongRTL:"-webkit-box"===r.css("display")}),!0}init(e){const t=this;return t.initialized||!1===t.mount(e)||(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const i=this,{params:n,$el:r,$wrapperEl:o,slides:s}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),n.loop&&i.loopDestroy(),t&&(i.removeClasses(),r.removeAttr("style"),o.removeAttr("style"),s&&s.length&&s.removeClass([n.slideVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),i.emit("destroy"),Object.keys(i.eventsListeners).forEach((e=>{i.off(e)})),!1!==e&&(i.$el[0].swiper=null,function(e){const t=e;Object.keys(t).forEach((e=>{try{t[e]=null}catch(e){}try{delete t[e]}catch(e){}}))}(i)),i.destroyed=!0),null}static extendDefaults(e){S(X,e)}static get extendedDefaults(){return X}static get defaults(){return q}static installModule(e){Y.prototype.__modules__||(Y.prototype.__modules__=[]);const t=Y.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach((e=>Y.installModule(e))),Y):(Y.installModule(e),Y)}}Object.keys(J).forEach((e=>{Object.keys(J[e]).forEach((t=>{Y.prototype[t]=J[e][t]}))})),Y.use([function(e){let{swiper:t,on:i,emit:n}=e;const r=l();let o=null,s=null;const a=()=>{t&&!t.destroyed&&t.initialized&&(n("beforeResize"),n("resize"))},p=()=>{t&&!t.destroyed&&t.initialized&&n("orientationchange")};i("init",(()=>{t.params.resizeObserver&&void 0!==r.ResizeObserver?t&&!t.destroyed&&t.initialized&&(o=new ResizeObserver((e=>{s=r.requestAnimationFrame((()=>{const{width:i,height:n}=t;let r=i,o=n;e.forEach((e=>{let{contentBoxSize:i,contentRect:n,target:s}=e;s&&s!==t.el||(r=n?n.width:(i[0]||i).inlineSize,o=n?n.height:(i[0]||i).blockSize)})),r===i&&o===n||a()}))})),o.observe(t.el)):(r.addEventListener("resize",a),r.addEventListener("orientationchange",p))})),i("destroy",(()=>{s&&r.cancelAnimationFrame(s),o&&o.unobserve&&t.el&&(o.unobserve(t.el),o=null),r.removeEventListener("resize",a),r.removeEventListener("orientationchange",p)}))},function(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;const o=[],s=l(),a=function(e,t){void 0===t&&(t={});const i=new(s.MutationObserver||s.WebkitMutationObserver)((e=>{if(1===e.length)return void r("observerUpdate",e[0]);const t=function(){r("observerUpdate",e[0])};s.requestAnimationFrame?s.requestAnimationFrame(t):s.setTimeout(t,0)}));i.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),o.push(i)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),n("init",(()=>{if(t.params.observer){if(t.params.observeParents){const e=t.$el.parents();for(let t=0;t<e.length;t+=1)a(e[t])}a(t.$el[0],{childList:t.params.observeSlideChildren}),a(t.$wrapperEl[0],{attributes:!1})}})),n("destroy",(()=>{o.forEach((e=>{e.disconnect()})),o.splice(0,o.length)}))}]);const K=Y;function Q(e){let t,{swiper:i,extendParams:n,on:r,emit:o}=e;function s(e,t){const n=i.params.virtual;if(n.cache&&i.virtual.cache[t])return i.virtual.cache[t];const r=n.renderSlide?g(n.renderSlide.call(i,e,t)):g(`<div class="${i.params.slideClass}" data-swiper-slide-index="${t}">${e}</div>`);return r.attr("data-swiper-slide-index")||r.attr("data-swiper-slide-index",t),n.cache&&(i.virtual.cache[t]=r),r}function a(e){const{slidesPerView:t,slidesPerGroup:n,centeredSlides:r}=i.params,{addSlidesBefore:a,addSlidesAfter:l}=i.params.virtual,{from:p,to:c,slides:u,slidesGrid:d,offset:h}=i.virtual;i.params.cssMode||i.updateActiveIndex();const f=i.activeIndex||0;let m,g,y;m=i.rtlTranslate?"right":i.isHorizontal()?"left":"top",r?(g=Math.floor(t/2)+n+l,y=Math.floor(t/2)+n+a):(g=t+(n-1)+l,y=n+a);const v=Math.max((f||0)-y,0),b=Math.min((f||0)+g,u.length-1),C=(i.slidesGrid[v]||0)-(i.slidesGrid[0]||0);function w(){i.updateSlides(),i.updateProgress(),i.updateSlidesClasses(),i.lazy&&i.params.lazy.enabled&&i.lazy.load(),o("virtualUpdate")}if(Object.assign(i.virtual,{from:v,to:b,offset:C,slidesGrid:i.slidesGrid}),p===v&&c===b&&!e)return i.slidesGrid!==d&&C!==h&&i.slides.css(m,`${C}px`),i.updateProgress(),void o("virtualUpdate");if(i.params.virtual.renderExternal)return i.params.virtual.renderExternal.call(i,{offset:C,from:v,to:b,slides:function(){const e=[];for(let t=v;t<=b;t+=1)e.push(u[t]);return e}()}),void(i.params.virtual.renderExternalUpdate?w():o("virtualUpdate"));const S=[],E=[];if(e)i.$wrapperEl.find(`.${i.params.slideClass}`).remove();else for(let e=p;e<=c;e+=1)(e<v||e>b)&&i.$wrapperEl.find(`.${i.params.slideClass}[data-swiper-slide-index="${e}"]`).remove();for(let t=0;t<u.length;t+=1)t>=v&&t<=b&&(void 0===c||e?E.push(t):(t>c&&E.push(t),t<p&&S.push(t)));E.forEach((e=>{i.$wrapperEl.append(s(u[e],e))})),S.sort(((e,t)=>t-e)).forEach((e=>{i.$wrapperEl.prepend(s(u[e],e))})),i.$wrapperEl.children(".swiper-slide").css(m,`${C}px`),w()}n({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}}),i.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]},r("beforeInit",(()=>{i.params.virtual.enabled&&(i.virtual.slides=i.params.virtual.slides,i.classNames.push(`${i.params.containerModifierClass}virtual`),i.params.watchSlidesProgress=!0,i.originalParams.watchSlidesProgress=!0,i.params.initialSlide||a())})),r("setTranslate",(()=>{i.params.virtual.enabled&&(i.params.cssMode&&!i._immediateVirtual?(clearTimeout(t),t=setTimeout((()=>{a()}),100)):a())})),r("init update resize",(()=>{i.params.virtual.enabled&&i.params.cssMode&&E(i.wrapperEl,"--swiper-virtual-size",`${i.virtualSize}px`)})),Object.assign(i.virtual,{appendSlide:function(e){if("object"==typeof e&&"length"in e)for(let t=0;t<e.length;t+=1)e[t]&&i.virtual.slides.push(e[t]);else i.virtual.slides.push(e);a(!0)},prependSlide:function(e){const t=i.activeIndex;let n=t+1,r=1;if(Array.isArray(e)){for(let t=0;t<e.length;t+=1)e[t]&&i.virtual.slides.unshift(e[t]);n=t+e.length,r=e.length}else i.virtual.slides.unshift(e);if(i.params.virtual.cache){const e=i.virtual.cache,t={};Object.keys(e).forEach((i=>{const n=e[i],o=n.attr("data-swiper-slide-index");o&&n.attr("data-swiper-slide-index",parseInt(o,10)+r),t[parseInt(i,10)+r]=n})),i.virtual.cache=t}a(!0),i.slideTo(n,0)},removeSlide:function(e){if(null==e)return;let t=i.activeIndex;if(Array.isArray(e))for(let n=e.length-1;n>=0;n-=1)i.virtual.slides.splice(e[n],1),i.params.virtual.cache&&delete i.virtual.cache[e[n]],e[n]<t&&(t-=1),t=Math.max(t,0);else i.virtual.slides.splice(e,1),i.params.virtual.cache&&delete i.virtual.cache[e],e<t&&(t-=1),t=Math.max(t,0);a(!0),i.slideTo(t,0)},removeAllSlides:function(){i.virtual.slides=[],i.params.virtual.cache&&(i.virtual.cache={}),a(!0),i.slideTo(0,0)},update:a})}function Z(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;const o=s(),a=l();function p(e){if(!t.enabled)return;const{rtlTranslate:i}=t;let n=e;n.originalEvent&&(n=n.originalEvent);const s=n.keyCode||n.charCode,l=t.params.keyboard.pageUpDown,p=l&&33===s,c=l&&34===s,u=37===s,d=39===s,h=38===s,f=40===s;if(!t.allowSlideNext&&(t.isHorizontal()&&d||t.isVertical()&&f||c))return!1;if(!t.allowSlidePrev&&(t.isHorizontal()&&u||t.isVertical()&&h||p))return!1;if(!(n.shiftKey||n.altKey||n.ctrlKey||n.metaKey||o.activeElement&&o.activeElement.nodeName&&("input"===o.activeElement.nodeName.toLowerCase()||"textarea"===o.activeElement.nodeName.toLowerCase()))){if(t.params.keyboard.onlyInViewport&&(p||c||u||d||h||f)){let e=!1;if(t.$el.parents(`.${t.params.slideClass}`).length>0&&0===t.$el.parents(`.${t.params.slideActiveClass}`).length)return;const n=t.$el,r=n[0].clientWidth,o=n[0].clientHeight,s=a.innerWidth,l=a.innerHeight,p=t.$el.offset();i&&(p.left-=t.$el[0].scrollLeft);const c=[[p.left,p.top],[p.left+r,p.top],[p.left,p.top+o],[p.left+r,p.top+o]];for(let t=0;t<c.length;t+=1){const i=c[t];if(i[0]>=0&&i[0]<=s&&i[1]>=0&&i[1]<=l){if(0===i[0]&&0===i[1])continue;e=!0}}if(!e)return}t.isHorizontal()?((p||c||u||d)&&(n.preventDefault?n.preventDefault():n.returnValue=!1),((c||d)&&!i||(p||u)&&i)&&t.slideNext(),((p||u)&&!i||(c||d)&&i)&&t.slidePrev()):((p||c||h||f)&&(n.preventDefault?n.preventDefault():n.returnValue=!1),(c||f)&&t.slideNext(),(p||h)&&t.slidePrev()),r("keyPress",s)}}function c(){t.keyboard.enabled||(g(o).on("keydown",p),t.keyboard.enabled=!0)}function u(){t.keyboard.enabled&&(g(o).off("keydown",p),t.keyboard.enabled=!1)}t.keyboard={enabled:!1},i({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),n("init",(()=>{t.params.keyboard.enabled&&c()})),n("destroy",(()=>{t.keyboard.enabled&&u()})),Object.assign(t.keyboard,{enable:c,disable:u})}function ee(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;const o=l();let s;i({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null}}),t.mousewheel={enabled:!1};let a,p=v();const c=[];function u(){t.enabled&&(t.mouseEntered=!0)}function d(){t.enabled&&(t.mouseEntered=!1)}function h(e){return!(t.params.mousewheel.thresholdDelta&&e.delta<t.params.mousewheel.thresholdDelta||t.params.mousewheel.thresholdTime&&v()-p<t.params.mousewheel.thresholdTime||!(e.delta>=6&&v()-p<60)&&(e.direction<0?t.isEnd&&!t.params.loop||t.animating||(t.slideNext(),r("scroll",e.raw)):t.isBeginning&&!t.params.loop||t.animating||(t.slidePrev(),r("scroll",e.raw)),p=(new o.Date).getTime(),1))}function f(e){let i=e,n=!0;if(!t.enabled)return;const o=t.params.mousewheel;t.params.cssMode&&i.preventDefault();let l=t.$el;if("container"!==t.params.mousewheel.eventsTarget&&(l=g(t.params.mousewheel.eventsTarget)),!t.mouseEntered&&!l[0].contains(i.target)&&!o.releaseOnEdges)return!0;i.originalEvent&&(i=i.originalEvent);let p=0;const u=t.rtlTranslate?-1:1,d=function(e){let t=0,i=0,n=0,r=0;return"detail"in e&&(i=e.detail),"wheelDelta"in e&&(i=-e.wheelDelta/120),"wheelDeltaY"in e&&(i=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=i,i=0),n=10*t,r=10*i,"deltaY"in e&&(r=e.deltaY),"deltaX"in e&&(n=e.deltaX),e.shiftKey&&!n&&(n=r,r=0),(n||r)&&e.deltaMode&&(1===e.deltaMode?(n*=40,r*=40):(n*=800,r*=800)),n&&!t&&(t=n<1?-1:1),r&&!i&&(i=r<1?-1:1),{spinX:t,spinY:i,pixelX:n,pixelY:r}}(i);if(o.forceToAxis)if(t.isHorizontal()){if(!(Math.abs(d.pixelX)>Math.abs(d.pixelY)))return!0;p=-d.pixelX*u}else{if(!(Math.abs(d.pixelY)>Math.abs(d.pixelX)))return!0;p=-d.pixelY}else p=Math.abs(d.pixelX)>Math.abs(d.pixelY)?-d.pixelX*u:-d.pixelY;if(0===p)return!0;o.invert&&(p=-p);let f=t.getTranslate()+p*o.sensitivity;if(f>=t.minTranslate()&&(f=t.minTranslate()),f<=t.maxTranslate()&&(f=t.maxTranslate()),n=!!t.params.loop||!(f===t.minTranslate()||f===t.maxTranslate()),n&&t.params.nested&&i.stopPropagation(),t.params.freeMode&&t.params.freeMode.enabled){const e={time:v(),delta:Math.abs(p),direction:Math.sign(p)},n=a&&e.time<a.time+500&&e.delta<=a.delta&&e.direction===a.direction;if(!n){a=void 0,t.params.loop&&t.loopFix();let l=t.getTranslate()+p*o.sensitivity;const u=t.isBeginning,d=t.isEnd;if(l>=t.minTranslate()&&(l=t.minTranslate()),l<=t.maxTranslate()&&(l=t.maxTranslate()),t.setTransition(0),t.setTranslate(l),t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses(),(!u&&t.isBeginning||!d&&t.isEnd)&&t.updateSlidesClasses(),t.params.freeMode.sticky){clearTimeout(s),s=void 0,c.length>=15&&c.shift();const i=c.length?c[c.length-1]:void 0,n=c[0];if(c.push(e),i&&(e.delta>i.delta||e.direction!==i.direction))c.splice(0);else if(c.length>=15&&e.time-n.time<500&&n.delta-e.delta>=1&&e.delta<=6){const i=p>0?.8:.2;a=e,c.splice(0),s=y((()=>{t.slideToClosest(t.params.speed,!0,void 0,i)}),0)}s||(s=y((()=>{a=e,c.splice(0),t.slideToClosest(t.params.speed,!0,void 0,.5)}),500))}if(n||r("scroll",i),t.params.autoplay&&t.params.autoplayDisableOnInteraction&&t.autoplay.stop(),l===t.minTranslate()||l===t.maxTranslate())return!0}}else{const i={time:v(),delta:Math.abs(p),direction:Math.sign(p),raw:e};c.length>=2&&c.shift();const n=c.length?c[c.length-1]:void 0;if(c.push(i),n?(i.direction!==n.direction||i.delta>n.delta||i.time>n.time+150)&&h(i):h(i),function(e){const i=t.params.mousewheel;if(e.direction<0){if(t.isEnd&&!t.params.loop&&i.releaseOnEdges)return!0}else if(t.isBeginning&&!t.params.loop&&i.releaseOnEdges)return!0;return!1}(i))return!0}return i.preventDefault?i.preventDefault():i.returnValue=!1,!1}function m(e){let i=t.$el;"container"!==t.params.mousewheel.eventsTarget&&(i=g(t.params.mousewheel.eventsTarget)),i[e]("mouseenter",u),i[e]("mouseleave",d),i[e]("wheel",f)}function b(){return t.params.cssMode?(t.wrapperEl.removeEventListener("wheel",f),!0):!t.mousewheel.enabled&&(m("on"),t.mousewheel.enabled=!0,!0)}function C(){return t.params.cssMode?(t.wrapperEl.addEventListener(event,f),!0):!!t.mousewheel.enabled&&(m("off"),t.mousewheel.enabled=!1,!0)}n("init",(()=>{!t.params.mousewheel.enabled&&t.params.cssMode&&C(),t.params.mousewheel.enabled&&b()})),n("destroy",(()=>{t.params.cssMode&&b(),t.mousewheel.enabled&&C()})),Object.assign(t.mousewheel,{enable:b,disable:C})}function te(e,t,i,n){const r=s();return e.params.createElements&&Object.keys(n).forEach((o=>{if(!i[o]&&!0===i.auto){let s=e.$el.children(`.${n[o]}`)[0];s||(s=r.createElement("div"),s.className=n[o],e.$el.append(s)),i[o]=s,t[o]=s}})),i}function ie(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;function o(e){let i;return e&&(i=g(e),t.params.uniqueNavElements&&"string"==typeof e&&i.length>1&&1===t.$el.find(e).length&&(i=t.$el.find(e))),i}function s(e,i){const n=t.params.navigation;e&&e.length>0&&(e[i?"addClass":"removeClass"](n.disabledClass),e[0]&&"BUTTON"===e[0].tagName&&(e[0].disabled=i),t.params.watchOverflow&&t.enabled&&e[t.isLocked?"addClass":"removeClass"](n.lockClass))}function a(){if(t.params.loop)return;const{$nextEl:e,$prevEl:i}=t.navigation;s(i,t.isBeginning&&!t.params.rewind),s(e,t.isEnd&&!t.params.rewind)}function l(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),r("navigationPrev"))}function p(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),r("navigationNext"))}function c(){const e=t.params.navigation;if(t.params.navigation=te(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!e.nextEl&&!e.prevEl)return;const i=o(e.nextEl),n=o(e.prevEl);i&&i.length>0&&i.on("click",p),n&&n.length>0&&n.on("click",l),Object.assign(t.navigation,{$nextEl:i,nextEl:i&&i[0],$prevEl:n,prevEl:n&&n[0]}),t.enabled||(i&&i.addClass(e.lockClass),n&&n.addClass(e.lockClass))}function u(){const{$nextEl:e,$prevEl:i}=t.navigation;e&&e.length&&(e.off("click",p),e.removeClass(t.params.navigation.disabledClass)),i&&i.length&&(i.off("click",l),i.removeClass(t.params.navigation.disabledClass))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,$nextEl:null,prevEl:null,$prevEl:null},n("init",(()=>{!1===t.params.navigation.enabled?d():(c(),a())})),n("toEdge fromEdge lock unlock",(()=>{a()})),n("destroy",(()=>{u()})),n("enable disable",(()=>{const{$nextEl:e,$prevEl:i}=t.navigation;e&&e[t.enabled?"removeClass":"addClass"](t.params.navigation.lockClass),i&&i[t.enabled?"removeClass":"addClass"](t.params.navigation.lockClass)})),n("click",((e,i)=>{const{$nextEl:n,$prevEl:o}=t.navigation,s=i.target;if(t.params.navigation.hideOnClick&&!g(s).is(o)&&!g(s).is(n)){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===s||t.pagination.el.contains(s)))return;let e;n?e=n.hasClass(t.params.navigation.hiddenClass):o&&(e=o.hasClass(t.params.navigation.hiddenClass)),r(!0===e?"navigationShow":"navigationHide"),n&&n.toggleClass(t.params.navigation.hiddenClass),o&&o.toggleClass(t.params.navigation.hiddenClass)}}));const d=()=>{t.$el.addClass(t.params.navigation.navigationDisabledClass),u()};Object.assign(t.navigation,{enable:()=>{t.$el.removeClass(t.params.navigation.navigationDisabledClass),c(),a()},disable:d,update:a,init:c,destroy:u})}function ne(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!\/])/g,"\\$1").replace(/ /g,".")}`}function re(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;const o="swiper-pagination";let s;i({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${o}-bullet`,bulletActiveClass:`${o}-bullet-active`,modifierClass:`${o}-`,currentClass:`${o}-current`,totalClass:`${o}-total`,hiddenClass:`${o}-hidden`,progressbarFillClass:`${o}-progressbar-fill`,progressbarOppositeClass:`${o}-progressbar-opposite`,clickableClass:`${o}-clickable`,lockClass:`${o}-lock`,horizontalClass:`${o}-horizontal`,verticalClass:`${o}-vertical`,paginationDisabledClass:`${o}-disabled`}}),t.pagination={el:null,$el:null,bullets:[]};let a=0;function l(){return!t.params.pagination.el||!t.pagination.el||!t.pagination.$el||0===t.pagination.$el.length}function p(e,i){const{bulletActiveClass:n}=t.params.pagination;e[i]().addClass(`${n}-${i}`)[i]().addClass(`${n}-${i}-${i}`)}function c(){const e=t.rtl,i=t.params.pagination;if(l())return;const n=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,o=t.pagination.$el;let c;const u=t.params.loop?Math.ceil((n-2*t.loopedSlides)/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(c=Math.ceil((t.activeIndex-t.loopedSlides)/t.params.slidesPerGroup),c>n-1-2*t.loopedSlides&&(c-=n-2*t.loopedSlides),c>u-1&&(c-=u),c<0&&"bullets"!==t.params.paginationType&&(c=u+c)):c=void 0!==t.snapIndex?t.snapIndex:t.activeIndex||0,"bullets"===i.type&&t.pagination.bullets&&t.pagination.bullets.length>0){const n=t.pagination.bullets;let r,l,u;if(i.dynamicBullets&&(s=n.eq(0)[t.isHorizontal()?"outerWidth":"outerHeight"](!0),o.css(t.isHorizontal()?"width":"height",s*(i.dynamicMainBullets+4)+"px"),i.dynamicMainBullets>1&&void 0!==t.previousIndex&&(a+=c-(t.previousIndex-t.loopedSlides||0),a>i.dynamicMainBullets-1?a=i.dynamicMainBullets-1:a<0&&(a=0)),r=Math.max(c-a,0),l=r+(Math.min(n.length,i.dynamicMainBullets)-1),u=(l+r)/2),n.removeClass(["","-next","-next-next","-prev","-prev-prev","-main"].map((e=>`${i.bulletActiveClass}${e}`)).join(" ")),o.length>1)n.each((e=>{const t=g(e),n=t.index();n===c&&t.addClass(i.bulletActiveClass),i.dynamicBullets&&(n>=r&&n<=l&&t.addClass(`${i.bulletActiveClass}-main`),n===r&&p(t,"prev"),n===l&&p(t,"next"))}));else{const e=n.eq(c),o=e.index();if(e.addClass(i.bulletActiveClass),i.dynamicBullets){const e=n.eq(r),s=n.eq(l);for(let e=r;e<=l;e+=1)n.eq(e).addClass(`${i.bulletActiveClass}-main`);if(t.params.loop)if(o>=n.length){for(let e=i.dynamicMainBullets;e>=0;e-=1)n.eq(n.length-e).addClass(`${i.bulletActiveClass}-main`);n.eq(n.length-i.dynamicMainBullets-1).addClass(`${i.bulletActiveClass}-prev`)}else p(e,"prev"),p(s,"next");else p(e,"prev"),p(s,"next")}}if(i.dynamicBullets){const r=Math.min(n.length,i.dynamicMainBullets+4),o=(s*r-s)/2-u*s,a=e?"right":"left";n.css(t.isHorizontal()?a:"top",`${o}px`)}}if("fraction"===i.type&&(o.find(ne(i.currentClass)).text(i.formatFractionCurrent(c+1)),o.find(ne(i.totalClass)).text(i.formatFractionTotal(u))),"progressbar"===i.type){let e;e=i.progressbarOpposite?t.isHorizontal()?"vertical":"horizontal":t.isHorizontal()?"horizontal":"vertical";const n=(c+1)/u;let r=1,s=1;"horizontal"===e?r=n:s=n,o.find(ne(i.progressbarFillClass)).transform(`translate3d(0,0,0) scaleX(${r}) scaleY(${s})`).transition(t.params.speed)}"custom"===i.type&&i.renderCustom?(o.html(i.renderCustom(t,c+1,u)),r("paginationRender",o[0])):r("paginationUpdate",o[0]),t.params.watchOverflow&&t.enabled&&o[t.isLocked?"addClass":"removeClass"](i.lockClass)}function u(){const e=t.params.pagination;if(l())return;const i=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,n=t.pagination.$el;let o="";if("bullets"===e.type){let r=t.params.loop?Math.ceil((i-2*t.loopedSlides)/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&!t.params.loop&&r>i&&(r=i);for(let i=0;i<r;i+=1)e.renderBullet?o+=e.renderBullet.call(t,i,e.bulletClass):o+=`<${e.bulletElement} class="${e.bulletClass}"></${e.bulletElement}>`;n.html(o),t.pagination.bullets=n.find(ne(e.bulletClass))}"fraction"===e.type&&(o=e.renderFraction?e.renderFraction.call(t,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`,n.html(o)),"progressbar"===e.type&&(o=e.renderProgressbar?e.renderProgressbar.call(t,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`,n.html(o)),"custom"!==e.type&&r("paginationRender",t.pagination.$el[0])}function d(){t.params.pagination=te(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const e=t.params.pagination;if(!e.el)return;let i=g(e.el);0!==i.length&&(t.params.uniqueNavElements&&"string"==typeof e.el&&i.length>1&&(i=t.$el.find(e.el),i.length>1&&(i=i.filter((e=>g(e).parents(".swiper")[0]===t.el)))),"bullets"===e.type&&e.clickable&&i.addClass(e.clickableClass),i.addClass(e.modifierClass+e.type),i.addClass(t.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(i.addClass(`${e.modifierClass}${e.type}-dynamic`),a=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&i.addClass(e.progressbarOppositeClass),e.clickable&&i.on("click",ne(e.bulletClass),(function(e){e.preventDefault();let i=g(this).index()*t.params.slidesPerGroup;t.params.loop&&(i+=t.loopedSlides),t.slideTo(i)})),Object.assign(t.pagination,{$el:i,el:i[0]}),t.enabled||i.addClass(e.lockClass))}function h(){const e=t.params.pagination;if(l())return;const i=t.pagination.$el;i.removeClass(e.hiddenClass),i.removeClass(e.modifierClass+e.type),i.removeClass(t.isHorizontal()?e.horizontalClass:e.verticalClass),t.pagination.bullets&&t.pagination.bullets.removeClass&&t.pagination.bullets.removeClass(e.bulletActiveClass),e.clickable&&i.off("click",ne(e.bulletClass))}n("init",(()=>{!1===t.params.pagination.enabled?f():(d(),u(),c())})),n("activeIndexChange",(()=>{(t.params.loop||void 0===t.snapIndex)&&c()})),n("snapIndexChange",(()=>{t.params.loop||c()})),n("slidesLengthChange",(()=>{t.params.loop&&(u(),c())})),n("snapGridLengthChange",(()=>{t.params.loop||(u(),c())})),n("destroy",(()=>{h()})),n("enable disable",(()=>{const{$el:e}=t.pagination;e&&e[t.enabled?"removeClass":"addClass"](t.params.pagination.lockClass)})),n("lock unlock",(()=>{c()})),n("click",((e,i)=>{const n=i.target,{$el:o}=t.pagination;if(t.params.pagination.el&&t.params.pagination.hideOnClick&&o&&o.length>0&&!g(n).hasClass(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&n===t.navigation.nextEl||t.navigation.prevEl&&n===t.navigation.prevEl))return;const e=o.hasClass(t.params.pagination.hiddenClass);r(!0===e?"paginationShow":"paginationHide"),o.toggleClass(t.params.pagination.hiddenClass)}}));const f=()=>{t.$el.addClass(t.params.pagination.paginationDisabledClass),t.pagination.$el&&t.pagination.$el.addClass(t.params.pagination.paginationDisabledClass),h()};Object.assign(t.pagination,{enable:()=>{t.$el.removeClass(t.params.pagination.paginationDisabledClass),t.pagination.$el&&t.pagination.$el.removeClass(t.params.pagination.paginationDisabledClass),d(),u(),c()},disable:f,render:u,update:c,init:d,destroy:h})}function oe(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;const o=s();let a,l,p,c,u=!1,d=null,h=null;function f(){if(!t.params.scrollbar.el||!t.scrollbar.el)return;const{scrollbar:e,rtlTranslate:i,progress:n}=t,{$dragEl:r,$el:o}=e,s=t.params.scrollbar;let a=l,c=(p-l)*n;i?(c=-c,c>0?(a=l-c,c=0):-c+l>p&&(a=p+c)):c<0?(a=l+c,c=0):c+l>p&&(a=p-c),t.isHorizontal()?(r.transform(`translate3d(${c}px, 0, 0)`),r[0].style.width=`${a}px`):(r.transform(`translate3d(0px, ${c}px, 0)`),r[0].style.height=`${a}px`),s.hide&&(clearTimeout(d),o[0].style.opacity=1,d=setTimeout((()=>{o[0].style.opacity=0,o.transition(400)}),1e3))}function m(){if(!t.params.scrollbar.el||!t.scrollbar.el)return;const{scrollbar:e}=t,{$dragEl:i,$el:n}=e;i[0].style.width="",i[0].style.height="",p=t.isHorizontal()?n[0].offsetWidth:n[0].offsetHeight,c=t.size/(t.virtualSize+t.params.slidesOffsetBefore-(t.params.centeredSlides?t.snapGrid[0]:0)),l="auto"===t.params.scrollbar.dragSize?p*c:parseInt(t.params.scrollbar.dragSize,10),t.isHorizontal()?i[0].style.width=`${l}px`:i[0].style.height=`${l}px`,n[0].style.display=c>=1?"none":"",t.params.scrollbar.hide&&(n[0].style.opacity=0),t.params.watchOverflow&&t.enabled&&e.$el[t.isLocked?"addClass":"removeClass"](t.params.scrollbar.lockClass)}function v(e){return t.isHorizontal()?"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].clientX:e.clientX:"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].clientY:e.clientY}function b(e){const{scrollbar:i,rtlTranslate:n}=t,{$el:r}=i;let o;o=(v(e)-r.offset()[t.isHorizontal()?"left":"top"]-(null!==a?a:l/2))/(p-l),o=Math.max(Math.min(o,1),0),n&&(o=1-o);const s=t.minTranslate()+(t.maxTranslate()-t.minTranslate())*o;t.updateProgress(s),t.setTranslate(s),t.updateActiveIndex(),t.updateSlidesClasses()}function C(e){const i=t.params.scrollbar,{scrollbar:n,$wrapperEl:o}=t,{$el:s,$dragEl:l}=n;u=!0,a=e.target===l[0]||e.target===l?v(e)-e.target.getBoundingClientRect()[t.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),o.transition(100),l.transition(100),b(e),clearTimeout(h),s.transition(0),i.hide&&s.css("opacity",1),t.params.cssMode&&t.$wrapperEl.css("scroll-snap-type","none"),r("scrollbarDragStart",e)}function w(e){const{scrollbar:i,$wrapperEl:n}=t,{$el:o,$dragEl:s}=i;u&&(e.preventDefault?e.preventDefault():e.returnValue=!1,b(e),n.transition(0),o.transition(0),s.transition(0),r("scrollbarDragMove",e))}function S(e){const i=t.params.scrollbar,{scrollbar:n,$wrapperEl:o}=t,{$el:s}=n;u&&(u=!1,t.params.cssMode&&(t.$wrapperEl.css("scroll-snap-type",""),o.transition("")),i.hide&&(clearTimeout(h),h=y((()=>{s.css("opacity",0),s.transition(400)}),1e3)),r("scrollbarDragEnd",e),i.snapOnRelease&&t.slideToClosest())}function E(e){const{scrollbar:i,touchEventsTouch:n,touchEventsDesktop:r,params:s,support:a}=t,l=i.$el;if(!l)return;const p=l[0],c=!(!a.passiveListener||!s.passiveListeners)&&{passive:!1,capture:!1},u=!(!a.passiveListener||!s.passiveListeners)&&{passive:!0,capture:!1};if(!p)return;const d="on"===e?"addEventListener":"removeEventListener";a.touch?(p[d](n.start,C,c),p[d](n.move,w,c),p[d](n.end,S,u)):(p[d](r.start,C,c),o[d](r.move,w,c),o[d](r.end,S,u))}function x(){const{scrollbar:e,$el:i}=t;t.params.scrollbar=te(t,t.originalParams.scrollbar,t.params.scrollbar,{el:"swiper-scrollbar"});const n=t.params.scrollbar;if(!n.el)return;let r=g(n.el);t.params.uniqueNavElements&&"string"==typeof n.el&&r.length>1&&1===i.find(n.el).length&&(r=i.find(n.el)),r.addClass(t.isHorizontal()?n.horizontalClass:n.verticalClass);let o=r.find(`.${t.params.scrollbar.dragClass}`);0===o.length&&(o=g(`<div class="${t.params.scrollbar.dragClass}"></div>`),r.append(o)),Object.assign(e,{$el:r,el:r[0],$dragEl:o,dragEl:o[0]}),n.draggable&&t.params.scrollbar.el&&t.scrollbar.el&&E("on"),r&&r[t.enabled?"removeClass":"addClass"](t.params.scrollbar.lockClass)}function P(){const e=t.params.scrollbar,i=t.scrollbar.$el;i&&i.removeClass(t.isHorizontal()?e.horizontalClass:e.verticalClass),t.params.scrollbar.el&&t.scrollbar.el&&E("off")}i({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),t.scrollbar={el:null,dragEl:null,$el:null,$dragEl:null},n("init",(()=>{!1===t.params.scrollbar.enabled?_():(x(),m(),f())})),n("update resize observerUpdate lock unlock",(()=>{m()})),n("setTranslate",(()=>{f()})),n("setTransition",((e,i)=>{!function(e){t.params.scrollbar.el&&t.scrollbar.el&&t.scrollbar.$dragEl.transition(e)}(i)})),n("enable disable",(()=>{const{$el:e}=t.scrollbar;e&&e[t.enabled?"removeClass":"addClass"](t.params.scrollbar.lockClass)})),n("destroy",(()=>{P()}));const _=()=>{t.$el.addClass(t.params.scrollbar.scrollbarDisabledClass),t.scrollbar.$el&&t.scrollbar.$el.addClass(t.params.scrollbar.scrollbarDisabledClass),P()};Object.assign(t.scrollbar,{enable:()=>{t.$el.removeClass(t.params.scrollbar.scrollbarDisabledClass),t.scrollbar.$el&&t.scrollbar.$el.removeClass(t.params.scrollbar.scrollbarDisabledClass),x(),m(),f()},disable:_,updateSize:m,setTranslate:f,init:x,destroy:P})}function se(e){let{swiper:t,extendParams:i,on:n}=e;i({parallax:{enabled:!1}});const r=(e,i)=>{const{rtl:n}=t,r=g(e),o=n?-1:1,s=r.attr("data-swiper-parallax")||"0";let a=r.attr("data-swiper-parallax-x"),l=r.attr("data-swiper-parallax-y");const p=r.attr("data-swiper-parallax-scale"),c=r.attr("data-swiper-parallax-opacity");if(a||l?(a=a||"0",l=l||"0"):t.isHorizontal()?(a=s,l="0"):(l=s,a="0"),a=a.indexOf("%")>=0?parseInt(a,10)*i*o+"%":a*i*o+"px",l=l.indexOf("%")>=0?parseInt(l,10)*i+"%":l*i+"px",null!=c){const e=c-(c-1)*(1-Math.abs(i));r[0].style.opacity=e}if(null==p)r.transform(`translate3d(${a}, ${l}, 0px)`);else{const e=p-(p-1)*(1-Math.abs(i));r.transform(`translate3d(${a}, ${l}, 0px) scale(${e})`)}},o=()=>{const{$el:e,slides:i,progress:n,snapGrid:o}=t;e.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((e=>{r(e,n)})),i.each(((e,i)=>{let s=e.progress;t.params.slidesPerGroup>1&&"auto"!==t.params.slidesPerView&&(s+=Math.ceil(i/2)-n*(o.length-1)),s=Math.min(Math.max(s,-1),1),g(e).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((e=>{r(e,s)}))}))};n("beforeInit",(()=>{t.params.parallax.enabled&&(t.params.watchSlidesProgress=!0,t.originalParams.watchSlidesProgress=!0)})),n("init",(()=>{t.params.parallax.enabled&&o()})),n("setTranslate",(()=>{t.params.parallax.enabled&&o()})),n("setTransition",((e,i)=>{t.params.parallax.enabled&&function(e){void 0===e&&(e=t.params.speed);const{$el:i}=t;i.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each((t=>{const i=g(t);let n=parseInt(i.attr("data-swiper-parallax-duration"),10)||e;0===e&&(n=0),i.transition(n)}))}(i)}))}function ae(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;const o=l();i({zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),t.zoom={enabled:!1};let s,a,p,c=1,u=!1;const d={$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},h={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},f={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let m=1;function y(e){if(e.targetTouches.length<2)return 1;const t=e.targetTouches[0].pageX,i=e.targetTouches[0].pageY,n=e.targetTouches[1].pageX,r=e.targetTouches[1].pageY;return Math.sqrt((n-t)**2+(r-i)**2)}function v(e){const i=t.support,n=t.params.zoom;if(a=!1,p=!1,!i.gestures){if("touchstart"!==e.type||"touchstart"===e.type&&e.targetTouches.length<2)return;a=!0,d.scaleStart=y(e)}d.$slideEl&&d.$slideEl.length||(d.$slideEl=g(e.target).closest(`.${t.params.slideClass}`),0===d.$slideEl.length&&(d.$slideEl=t.slides.eq(t.activeIndex)),d.$imageEl=d.$slideEl.find(`.${n.containerClass}`).eq(0).find("picture, img, svg, canvas, .swiper-zoom-target").eq(0),d.$imageWrapEl=d.$imageEl.parent(`.${n.containerClass}`),d.maxRatio=d.$imageWrapEl.attr("data-swiper-zoom")||n.maxRatio,0!==d.$imageWrapEl.length)?(d.$imageEl&&d.$imageEl.transition(0),u=!0):d.$imageEl=void 0}function C(e){const i=t.support,n=t.params.zoom,r=t.zoom;if(!i.gestures){if("touchmove"!==e.type||"touchmove"===e.type&&e.targetTouches.length<2)return;p=!0,d.scaleMove=y(e)}d.$imageEl&&0!==d.$imageEl.length?(i.gestures?r.scale=e.scale*c:r.scale=d.scaleMove/d.scaleStart*c,r.scale>d.maxRatio&&(r.scale=d.maxRatio-1+(r.scale-d.maxRatio+1)**.5),r.scale<n.minRatio&&(r.scale=n.minRatio+1-(n.minRatio-r.scale+1)**.5),d.$imageEl.transform(`translate3d(0,0,0) scale(${r.scale})`)):"gesturechange"===e.type&&v(e)}function w(e){const i=t.device,n=t.support,r=t.params.zoom,o=t.zoom;if(!n.gestures){if(!a||!p)return;if("touchend"!==e.type||"touchend"===e.type&&e.changedTouches.length<2&&!i.android)return;a=!1,p=!1}d.$imageEl&&0!==d.$imageEl.length&&(o.scale=Math.max(Math.min(o.scale,d.maxRatio),r.minRatio),d.$imageEl.transition(t.params.speed).transform(`translate3d(0,0,0) scale(${o.scale})`),c=o.scale,u=!1,1===o.scale&&(d.$slideEl=void 0))}function S(e){const i=t.zoom;if(!d.$imageEl||0===d.$imageEl.length)return;if(t.allowClick=!1,!h.isTouched||!d.$slideEl)return;h.isMoved||(h.width=d.$imageEl[0].offsetWidth,h.height=d.$imageEl[0].offsetHeight,h.startX=b(d.$imageWrapEl[0],"x")||0,h.startY=b(d.$imageWrapEl[0],"y")||0,d.slideWidth=d.$slideEl[0].offsetWidth,d.slideHeight=d.$slideEl[0].offsetHeight,d.$imageWrapEl.transition(0));const n=h.width*i.scale,r=h.height*i.scale;if(!(n<d.slideWidth&&r<d.slideHeight)){if(h.minX=Math.min(d.slideWidth/2-n/2,0),h.maxX=-h.minX,h.minY=Math.min(d.slideHeight/2-r/2,0),h.maxY=-h.minY,h.touchesCurrent.x="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,h.touchesCurrent.y="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY,!h.isMoved&&!u){if(t.isHorizontal()&&(Math.floor(h.minX)===Math.floor(h.startX)&&h.touchesCurrent.x<h.touchesStart.x||Math.floor(h.maxX)===Math.floor(h.startX)&&h.touchesCurrent.x>h.touchesStart.x))return void(h.isTouched=!1);if(!t.isHorizontal()&&(Math.floor(h.minY)===Math.floor(h.startY)&&h.touchesCurrent.y<h.touchesStart.y||Math.floor(h.maxY)===Math.floor(h.startY)&&h.touchesCurrent.y>h.touchesStart.y))return void(h.isTouched=!1)}e.cancelable&&e.preventDefault(),e.stopPropagation(),h.isMoved=!0,h.currentX=h.touchesCurrent.x-h.touchesStart.x+h.startX,h.currentY=h.touchesCurrent.y-h.touchesStart.y+h.startY,h.currentX<h.minX&&(h.currentX=h.minX+1-(h.minX-h.currentX+1)**.8),h.currentX>h.maxX&&(h.currentX=h.maxX-1+(h.currentX-h.maxX+1)**.8),h.currentY<h.minY&&(h.currentY=h.minY+1-(h.minY-h.currentY+1)**.8),h.currentY>h.maxY&&(h.currentY=h.maxY-1+(h.currentY-h.maxY+1)**.8),f.prevPositionX||(f.prevPositionX=h.touchesCurrent.x),f.prevPositionY||(f.prevPositionY=h.touchesCurrent.y),f.prevTime||(f.prevTime=Date.now()),f.x=(h.touchesCurrent.x-f.prevPositionX)/(Date.now()-f.prevTime)/2,f.y=(h.touchesCurrent.y-f.prevPositionY)/(Date.now()-f.prevTime)/2,Math.abs(h.touchesCurrent.x-f.prevPositionX)<2&&(f.x=0),Math.abs(h.touchesCurrent.y-f.prevPositionY)<2&&(f.y=0),f.prevPositionX=h.touchesCurrent.x,f.prevPositionY=h.touchesCurrent.y,f.prevTime=Date.now(),d.$imageWrapEl.transform(`translate3d(${h.currentX}px, ${h.currentY}px,0)`)}}function E(){const e=t.zoom;d.$slideEl&&t.previousIndex!==t.activeIndex&&(d.$imageEl&&d.$imageEl.transform("translate3d(0,0,0) scale(1)"),d.$imageWrapEl&&d.$imageWrapEl.transform("translate3d(0,0,0)"),e.scale=1,c=1,d.$slideEl=void 0,d.$imageEl=void 0,d.$imageWrapEl=void 0)}function x(e){const i=t.zoom,n=t.params.zoom;if(d.$slideEl||(e&&e.target&&(d.$slideEl=g(e.target).closest(`.${t.params.slideClass}`)),d.$slideEl||(t.params.virtual&&t.params.virtual.enabled&&t.virtual?d.$slideEl=t.$wrapperEl.children(`.${t.params.slideActiveClass}`):d.$slideEl=t.slides.eq(t.activeIndex)),d.$imageEl=d.$slideEl.find(`.${n.containerClass}`).eq(0).find("picture, img, svg, canvas, .swiper-zoom-target").eq(0),d.$imageWrapEl=d.$imageEl.parent(`.${n.containerClass}`)),!d.$imageEl||0===d.$imageEl.length||!d.$imageWrapEl||0===d.$imageWrapEl.length)return;let r,s,a,l,p,u,f,m,y,v,b,C,w,S,E,x,P,_;t.params.cssMode&&(t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.touchAction="none"),d.$slideEl.addClass(`${n.zoomedSlideClass}`),void 0===h.touchesStart.x&&e?(r="touchend"===e.type?e.changedTouches[0].pageX:e.pageX,s="touchend"===e.type?e.changedTouches[0].pageY:e.pageY):(r=h.touchesStart.x,s=h.touchesStart.y),i.scale=d.$imageWrapEl.attr("data-swiper-zoom")||n.maxRatio,c=d.$imageWrapEl.attr("data-swiper-zoom")||n.maxRatio,e?(P=d.$slideEl[0].offsetWidth,_=d.$slideEl[0].offsetHeight,a=d.$slideEl.offset().left+o.scrollX,l=d.$slideEl.offset().top+o.scrollY,p=a+P/2-r,u=l+_/2-s,y=d.$imageEl[0].offsetWidth,v=d.$imageEl[0].offsetHeight,b=y*i.scale,C=v*i.scale,w=Math.min(P/2-b/2,0),S=Math.min(_/2-C/2,0),E=-w,x=-S,f=p*i.scale,m=u*i.scale,f<w&&(f=w),f>E&&(f=E),m<S&&(m=S),m>x&&(m=x)):(f=0,m=0),d.$imageWrapEl.transition(300).transform(`translate3d(${f}px, ${m}px,0)`),d.$imageEl.transition(300).transform(`translate3d(0,0,0) scale(${i.scale})`)}function P(){const e=t.zoom,i=t.params.zoom;d.$slideEl||(t.params.virtual&&t.params.virtual.enabled&&t.virtual?d.$slideEl=t.$wrapperEl.children(`.${t.params.slideActiveClass}`):d.$slideEl=t.slides.eq(t.activeIndex),d.$imageEl=d.$slideEl.find(`.${i.containerClass}`).eq(0).find("picture, img, svg, canvas, .swiper-zoom-target").eq(0),d.$imageWrapEl=d.$imageEl.parent(`.${i.containerClass}`)),d.$imageEl&&0!==d.$imageEl.length&&d.$imageWrapEl&&0!==d.$imageWrapEl.length&&(t.params.cssMode&&(t.wrapperEl.style.overflow="",t.wrapperEl.style.touchAction=""),e.scale=1,c=1,d.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"),d.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"),d.$slideEl.removeClass(`${i.zoomedSlideClass}`),d.$slideEl=void 0)}function _(e){const i=t.zoom;i.scale&&1!==i.scale?P():x(e)}function A(){const e=t.support;return{passiveListener:!("touchstart"!==t.touchEvents.start||!e.passiveListener||!t.params.passiveListeners)&&{passive:!0,capture:!1},activeListenerWithCapture:!e.passiveListener||{passive:!1,capture:!0}}}function T(){return`.${t.params.slideClass}`}function I(e){const{passiveListener:i}=A(),n=T();t.$wrapperEl[e]("gesturestart",n,v,i),t.$wrapperEl[e]("gesturechange",n,C,i),t.$wrapperEl[e]("gestureend",n,w,i)}function O(){s||(s=!0,I("on"))}function M(){s&&(s=!1,I("off"))}function V(){const e=t.zoom;if(e.enabled)return;e.enabled=!0;const i=t.support,{passiveListener:n,activeListenerWithCapture:r}=A(),o=T();i.gestures?(t.$wrapperEl.on(t.touchEvents.start,O,n),t.$wrapperEl.on(t.touchEvents.end,M,n)):"touchstart"===t.touchEvents.start&&(t.$wrapperEl.on(t.touchEvents.start,o,v,n),t.$wrapperEl.on(t.touchEvents.move,o,C,r),t.$wrapperEl.on(t.touchEvents.end,o,w,n),t.touchEvents.cancel&&t.$wrapperEl.on(t.touchEvents.cancel,o,w,n)),t.$wrapperEl.on(t.touchEvents.move,`.${t.params.zoom.containerClass}`,S,r)}function k(){const e=t.zoom;if(!e.enabled)return;const i=t.support;e.enabled=!1;const{passiveListener:n,activeListenerWithCapture:r}=A(),o=T();i.gestures?(t.$wrapperEl.off(t.touchEvents.start,O,n),t.$wrapperEl.off(t.touchEvents.end,M,n)):"touchstart"===t.touchEvents.start&&(t.$wrapperEl.off(t.touchEvents.start,o,v,n),t.$wrapperEl.off(t.touchEvents.move,o,C,r),t.$wrapperEl.off(t.touchEvents.end,o,w,n),t.touchEvents.cancel&&t.$wrapperEl.off(t.touchEvents.cancel,o,w,n)),t.$wrapperEl.off(t.touchEvents.move,`.${t.params.zoom.containerClass}`,S,r)}Object.defineProperty(t.zoom,"scale",{get:()=>m,set(e){if(m!==e){const t=d.$imageEl?d.$imageEl[0]:void 0,i=d.$slideEl?d.$slideEl[0]:void 0;r("zoomChange",e,t,i)}m=e}}),n("init",(()=>{t.params.zoom.enabled&&V()})),n("destroy",(()=>{k()})),n("touchStart",((e,i)=>{t.zoom.enabled&&function(e){const i=t.device;d.$imageEl&&0!==d.$imageEl.length&&(h.isTouched||(i.android&&e.cancelable&&e.preventDefault(),h.isTouched=!0,h.touchesStart.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,h.touchesStart.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY))}(i)})),n("touchEnd",((e,i)=>{t.zoom.enabled&&function(){const e=t.zoom;if(!d.$imageEl||0===d.$imageEl.length)return;if(!h.isTouched||!h.isMoved)return h.isTouched=!1,void(h.isMoved=!1);h.isTouched=!1,h.isMoved=!1;let i=300,n=300;const r=f.x*i,o=h.currentX+r,s=f.y*n,a=h.currentY+s;0!==f.x&&(i=Math.abs((o-h.currentX)/f.x)),0!==f.y&&(n=Math.abs((a-h.currentY)/f.y));const l=Math.max(i,n);h.currentX=o,h.currentY=a;const p=h.width*e.scale,c=h.height*e.scale;h.minX=Math.min(d.slideWidth/2-p/2,0),h.maxX=-h.minX,h.minY=Math.min(d.slideHeight/2-c/2,0),h.maxY=-h.minY,h.currentX=Math.max(Math.min(h.currentX,h.maxX),h.minX),h.currentY=Math.max(Math.min(h.currentY,h.maxY),h.minY),d.$imageWrapEl.transition(l).transform(`translate3d(${h.currentX}px, ${h.currentY}px,0)`)}()})),n("doubleTap",((e,i)=>{!t.animating&&t.params.zoom.enabled&&t.zoom.enabled&&t.params.zoom.toggle&&_(i)})),n("transitionEnd",(()=>{t.zoom.enabled&&t.params.zoom.enabled&&E()})),n("slideChange",(()=>{t.zoom.enabled&&t.params.zoom.enabled&&t.params.cssMode&&E()})),Object.assign(t.zoom,{enable:V,disable:k,in:x,out:P,toggle:_})}function le(e){let{swiper:t,extendParams:i,on:n,emit:r}=e;i({lazy:{checkInView:!1,enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,scrollingElement:"",elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}}),t.lazy={};let o=!1,s=!1;function a(e,i){void 0===i&&(i=!0);const n=t.params.lazy;if(void 0===e)return;if(0===t.slides.length)return;const o=t.virtual&&t.params.virtual.enabled?t.$wrapperEl.children(`.${t.params.slideClass}[data-swiper-slide-index="${e}"]`):t.slides.eq(e),s=o.find(`.${n.elementClass}:not(.${n.loadedClass}):not(.${n.loadingClass})`);!o.hasClass(n.elementClass)||o.hasClass(n.loadedClass)||o.hasClass(n.loadingClass)||s.push(o[0]),0!==s.length&&s.each((e=>{const s=g(e);s.addClass(n.loadingClass);const l=s.attr("data-background"),p=s.attr("data-src"),c=s.attr("data-srcset"),u=s.attr("data-sizes"),d=s.parent("picture");t.loadImage(s[0],p||l,c,u,!1,(()=>{if(null!=t&&t&&(!t||t.params)&&!t.destroyed){if(l?(s.css("background-image",`url("${l}")`),s.removeAttr("data-background")):(c&&(s.attr("srcset",c),s.removeAttr("data-srcset")),u&&(s.attr("sizes",u),s.removeAttr("data-sizes")),d.length&&d.children("source").each((e=>{const t=g(e);t.attr("data-srcset")&&(t.attr("srcset",t.attr("data-srcset")),t.removeAttr("data-srcset"))})),p&&(s.attr("src",p),s.removeAttr("data-src"))),s.addClass(n.loadedClass).removeClass(n.loadingClass),o.find(`.${n.preloaderClass}`).remove(),t.params.loop&&i){const e=o.attr("data-swiper-slide-index");o.hasClass(t.params.slideDuplicateClass)?a(t.$wrapperEl.children(`[data-swiper-slide-index="${e}"]:not(.${t.params.slideDuplicateClass})`).index(),!1):a(t.$wrapperEl.children(`.${t.params.slideDuplicateClass}[data-swiper-slide-index="${e}"]`).index(),!1)}r("lazyImageReady",o[0],s[0]),t.params.autoHeight&&t.updateAutoHeight()}})),r("lazyImageLoad",o[0],s[0])}))}function p(){const{$wrapperEl:e,params:i,slides:n,activeIndex:r}=t,o=t.virtual&&i.virtual.enabled,l=i.lazy;let p=i.slidesPerView;function c(t){if(o){if(e.children(`.${i.slideClass}[data-swiper-slide-index="${t}"]`).length)return!0}else if(n[t])return!0;return!1}function u(e){return o?g(e).attr("data-swiper-slide-index"):g(e).index()}if("auto"===p&&(p=0),s||(s=!0),t.params.watchSlidesProgress)e.children(`.${i.slideVisibleClass}`).each((e=>{a(o?g(e).attr("data-swiper-slide-index"):g(e).index())}));else if(p>1)for(let e=r;e<r+p;e+=1)c(e)&&a(e);else a(r);if(l.loadPrevNext)if(p>1||l.loadPrevNextAmount&&l.loadPrevNextAmount>1){const e=l.loadPrevNextAmount,t=Math.ceil(p),i=Math.min(r+t+Math.max(e,t),n.length),o=Math.max(r-Math.max(t,e),0);for(let e=r+t;e<i;e+=1)c(e)&&a(e);for(let e=o;e<r;e+=1)c(e)&&a(e)}else{const t=e.children(`.${i.slideNextClass}`);t.length>0&&a(u(t));const n=e.children(`.${i.slidePrevClass}`);n.length>0&&a(u(n))}}function c(){const e=l();if(!t||t.destroyed)return;const i=t.params.lazy.scrollingElement?g(t.params.lazy.scrollingElement):g(e),n=i[0]===e,r=n?e.innerWidth:i[0].offsetWidth,s=n?e.innerHeight:i[0].offsetHeight,a=t.$el.offset(),{rtlTranslate:u}=t;let d=!1;u&&(a.left-=t.$el[0].scrollLeft);const h=[[a.left,a.top],[a.left+t.width,a.top],[a.left,a.top+t.height],[a.left+t.width,a.top+t.height]];for(let e=0;e<h.length;e+=1){const t=h[e];if(t[0]>=0&&t[0]<=r&&t[1]>=0&&t[1]<=s){if(0===t[0]&&0===t[1])continue;d=!0}}const f=!("touchstart"!==t.touchEvents.start||!t.support.passiveListener||!t.params.passiveListeners)&&{passive:!0,capture:!1};d?(p(),i.off("scroll",c,f)):o||(o=!0,i.on("scroll",c,f))}n("beforeInit",(()=>{t.params.lazy.enabled&&t.params.preloadImages&&(t.params.preloadImages=!1)})),n("init",(()=>{t.params.lazy.enabled&&(t.params.lazy.checkInView?c():p())})),n("scroll",(()=>{t.params.freeMode&&t.params.freeMode.enabled&&!t.params.freeMode.sticky&&p()})),n("scrollbarDragMove resize _freeModeNoMomentumRelease",(()=>{t.params.lazy.enabled&&(t.params.lazy.checkInView?c():p())})),n("transitionStart",(()=>{t.params.lazy.enabled&&(t.params.lazy.loadOnTransitionStart||!t.params.lazy.loadOnTransitionStart&&!s)&&(t.params.lazy.checkInView?c():p())})),n("transitionEnd",(()=>{t.params.lazy.enabled&&!t.params.lazy.loadOnTransitionStart&&(t.params.lazy.checkInView?c():p())})),n("slideChange",(()=>{const{lazy:e,cssMode:i,watchSlidesProgress:n,touchReleaseOnEdges:r,resistanceRatio:o}=t.params;e.enabled&&(i||n&&(r||0===o))&&p()})),n("destroy",(()=>{t.$el&&t.$el.find(`.${t.params.lazy.loadingClass}`).removeClass(t.params.lazy.loadingClass)})),Object.assign(t.lazy,{load:p,loadInSlide:a})}function pe(e){let{swiper:t,extendParams:i,on:n}=e;function r(e,t){const i=function(){let e,t,i;return(n,r)=>{for(t=-1,e=n.length;e-t>1;)i=e+t>>1,n[i]<=r?t=i:e=i;return e}}();let n,r;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(r=i(this.x,e),n=r-1,(e-this.x[n])*(this.y[r]-this.y[n])/(this.x[r]-this.x[n])+this.y[n]):0},this}function o(){t.controller.control&&t.controller.spline&&(t.controller.spline=void 0,delete t.controller.spline)}i({controller:{control:void 0,inverse:!1,by:"slide"}}),t.controller={control:void 0},n("beforeInit",(()=>{t.controller.control=t.params.controller.control})),n("update",(()=>{o()})),n("resize",(()=>{o()})),n("observerUpdate",(()=>{o()})),n("setTranslate",((e,i,n)=>{t.controller.control&&t.controller.setTranslate(i,n)})),n("setTransition",((e,i,n)=>{t.controller.control&&t.controller.setTransition(i,n)})),Object.assign(t.controller,{setTranslate:function(e,i){const n=t.controller.control;let o,s;const a=t.constructor;function l(e){const i=t.rtlTranslate?-t.translate:t.translate;"slide"===t.params.controller.by&&(function(e){t.controller.spline||(t.controller.spline=t.params.loop?new r(t.slidesGrid,e.slidesGrid):new r(t.snapGrid,e.snapGrid))}(e),s=-t.controller.spline.interpolate(-i)),s&&"container"!==t.params.controller.by||(o=(e.maxTranslate()-e.minTranslate())/(t.maxTranslate()-t.minTranslate()),s=(i-t.minTranslate())*o+e.minTranslate()),t.params.controller.inverse&&(s=e.maxTranslate()-s),e.updateProgress(s),e.setTranslate(s,t),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(n))for(let e=0;e<n.length;e+=1)n[e]!==i&&n[e]instanceof a&&l(n[e]);else n instanceof a&&i!==n&&l(n)},setTransition:function(e,i){const n=t.constructor,r=t.controller.control;let o;function s(i){i.setTransition(e,t),0!==e&&(i.transitionStart(),i.params.autoHeight&&y((()=>{i.updateAutoHeight()})),i.$wrapperEl.transitionEnd((()=>{r&&(i.params.loop&&"slide"===t.params.controller.by&&i.loopFix(),i.transitionEnd())})))}if(Array.isArray(r))for(o=0;o<r.length;o+=1)r[o]!==i&&r[o]instanceof n&&s(r[o]);else r instanceof n&&i!==r&&s(r)}})}function ce(e){let{swiper:t,extendParams:i,on:n}=e;i({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null}});let r=null;function o(e){const t=r;0!==t.length&&(t.html(""),t.html(e))}function s(e){e.attr("tabIndex","0")}function a(e){e.attr("tabIndex","-1")}function l(e,t){e.attr("role",t)}function p(e,t){e.attr("aria-roledescription",t)}function c(e,t){e.attr("aria-label",t)}function u(e){e.attr("aria-disabled",!0)}function d(e){e.attr("aria-disabled",!1)}function h(e){if(13!==e.keyCode&&32!==e.keyCode)return;const i=t.params.a11y,n=g(e.target);t.navigation&&t.navigation.$nextEl&&n.is(t.navigation.$nextEl)&&(t.isEnd&&!t.params.loop||t.slideNext(),t.isEnd?o(i.lastSlideMessage):o(i.nextSlideMessage)),t.navigation&&t.navigation.$prevEl&&n.is(t.navigation.$prevEl)&&(t.isBeginning&&!t.params.loop||t.slidePrev(),t.isBeginning?o(i.firstSlideMessage):o(i.prevSlideMessage)),t.pagination&&n.is(ne(t.params.pagination.bulletClass))&&n[0].click()}function f(){return t.pagination&&t.pagination.bullets&&t.pagination.bullets.length}function m(){return f()&&t.params.pagination.clickable}const y=(e,t,i)=>{s(e),"BUTTON"!==e[0].tagName&&(l(e,"button"),e.on("keydown",h)),c(e,i),function(e,t){e.attr("aria-controls",t)}(e,t)},v=e=>{const i=e.target.closest(`.${t.params.slideClass}`);if(!i||!t.slides.includes(i))return;const n=t.slides.indexOf(i)===t.activeIndex,r=t.params.watchSlidesProgress&&t.visibleSlides&&t.visibleSlides.includes(i);n||r||t.slideTo(t.slides.indexOf(i),0)},b=()=>{const e=t.params.a11y;e.itemRoleDescriptionMessage&&p(g(t.slides),e.itemRoleDescriptionMessage),e.slideRole&&l(g(t.slides),e.slideRole);const i=t.params.loop?t.slides.filter((e=>!e.classList.contains(t.params.slideDuplicateClass))).length:t.slides.length;e.slideLabelMessage&&t.slides.each(((n,r)=>{const o=g(n),s=t.params.loop?parseInt(o.attr("data-swiper-slide-index"),10):r;c(o,e.slideLabelMessage.replace(/\{\{index\}\}/,s+1).replace(/\{\{slidesLength\}\}/,i))}))};n("beforeInit",(()=>{r=g(`<span class="${t.params.a11y.notificationClass}" aria-live="assertive" aria-atomic="true"></span>`)})),n("afterInit",(()=>{t.params.a11y.enabled&&(()=>{const e=t.params.a11y;t.$el.append(r);const i=t.$el;e.containerRoleDescriptionMessage&&p(i,e.containerRoleDescriptionMessage),e.containerMessage&&c(i,e.containerMessage);const n=t.$wrapperEl,o=e.id||n.attr("id")||`swiper-wrapper-${s=16,void 0===s&&(s=16),"x".repeat(s).replace(/x/g,(()=>Math.round(16*Math.random()).toString(16)))}`;var s;const a=t.params.autoplay&&t.params.autoplay.enabled?"off":"polite";var l;let u,d;l=o,n.attr("id",l),function(e,t){e.attr("aria-live",t)}(n,a),b(),t.navigation&&t.navigation.$nextEl&&(u=t.navigation.$nextEl),t.navigation&&t.navigation.$prevEl&&(d=t.navigation.$prevEl),u&&u.length&&y(u,o,e.nextSlideMessage),d&&d.length&&y(d,o,e.prevSlideMessage),m()&&t.pagination.$el.on("keydown",ne(t.params.pagination.bulletClass),h),t.$el.on("focus",v,!0)})()})),n("slidesLengthChange snapGridLengthChange slidesGridLengthChange",(()=>{t.params.a11y.enabled&&b()})),n("fromEdge toEdge afterInit lock unlock",(()=>{t.params.a11y.enabled&&function(){if(t.params.loop||t.params.rewind||!t.navigation)return;const{$nextEl:e,$prevEl:i}=t.navigation;i&&i.length>0&&(t.isBeginning?(u(i),a(i)):(d(i),s(i))),e&&e.length>0&&(t.isEnd?(u(e),a(e)):(d(e),s(e)))}()})),n("paginationUpdate",(()=>{t.params.a11y.enabled&&function(){const e=t.params.a11y;f()&&t.pagination.bullets.each((i=>{const n=g(i);t.params.pagination.clickable&&(s(n),t.params.pagination.renderBullet||(l(n,"button"),c(n,e.paginationBulletMessage.replace(/\{\{index\}\}/,n.index()+1)))),n.is(`.${t.params.pagination.bulletActiveClass}`)?n.attr("aria-current","true"):n.removeAttr("aria-current")}))}()})),n("destroy",(()=>{t.params.a11y.enabled&&function(){let e,i;r&&r.length>0&&r.remove(),t.navigation&&t.navigation.$nextEl&&(e=t.navigation.$nextEl),t.navigation&&t.navigation.$prevEl&&(i=t.navigation.$prevEl),e&&e.off("keydown",h),i&&i.off("keydown",h),m()&&t.pagination.$el.off("keydown",ne(t.params.pagination.bulletClass),h),t.$el.off("focus",v,!0)}()}))}function ue(e){let{swiper:t,extendParams:i,on:n}=e;i({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let r=!1,o={};const s=e=>e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),a=e=>{const t=l();let i;i=e?new URL(e):t.location;const n=i.pathname.slice(1).split("/").filter((e=>""!==e)),r=n.length;return{key:n[r-2],value:n[r-1]}},p=(e,i)=>{const n=l();if(!r||!t.params.history.enabled)return;let o;o=t.params.url?new URL(t.params.url):n.location;const a=t.slides.eq(i);let p=s(a.attr("data-history"));if(t.params.history.root.length>0){let i=t.params.history.root;"/"===i[i.length-1]&&(i=i.slice(0,i.length-1)),p=`${i}/${e}/${p}`}else o.pathname.includes(e)||(p=`${e}/${p}`);t.params.history.keepQuery&&(p+=o.search);const c=n.history.state;c&&c.value===p||(t.params.history.replaceState?n.history.replaceState({value:p},null,p):n.history.pushState({value:p},null,p))},c=(e,i,n)=>{if(i)for(let r=0,o=t.slides.length;r<o;r+=1){const o=t.slides.eq(r);if(s(o.attr("data-history"))===i&&!o.hasClass(t.params.slideDuplicateClass)){const i=o.index();t.slideTo(i,e,n)}}else t.slideTo(0,e,n)},u=()=>{o=a(t.params.url),c(t.params.speed,o.value,!1)};n("init",(()=>{t.params.history.enabled&&(()=>{const e=l();if(t.params.history){if(!e.history||!e.history.pushState)return t.params.history.enabled=!1,void(t.params.hashNavigation.enabled=!0);r=!0,o=a(t.params.url),(o.key||o.value)&&(c(0,o.value,t.params.runCallbacksOnInit),t.params.history.replaceState||e.addEventListener("popstate",u))}})()})),n("destroy",(()=>{t.params.history.enabled&&(()=>{const e=l();t.params.history.replaceState||e.removeEventListener("popstate",u)})()})),n("transitionEnd _freeModeNoMomentumRelease",(()=>{r&&p(t.params.history.key,t.activeIndex)})),n("slideChange",(()=>{r&&t.params.cssMode&&p(t.params.history.key,t.activeIndex)}))}function de(e){let{swiper:t,extendParams:i,emit:n,on:r}=e,o=!1;const a=s(),p=l();i({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}});const c=()=>{n("hashChange");const e=a.location.hash.replace("#","");if(e!==t.slides.eq(t.activeIndex).attr("data-hash")){const i=t.$wrapperEl.children(`.${t.params.slideClass}[data-hash="${e}"]`).index();if(void 0===i)return;t.slideTo(i)}},u=()=>{if(o&&t.params.hashNavigation.enabled)if(t.params.hashNavigation.replaceState&&p.history&&p.history.replaceState)p.history.replaceState(null,null,`#${t.slides.eq(t.activeIndex).attr("data-hash")}`||""),n("hashSet");else{const e=t.slides.eq(t.activeIndex),i=e.attr("data-hash")||e.attr("data-history");a.location.hash=i||"",n("hashSet")}};r("init",(()=>{t.params.hashNavigation.enabled&&(()=>{if(!t.params.hashNavigation.enabled||t.params.history&&t.params.history.enabled)return;o=!0;const e=a.location.hash.replace("#","");if(e){const i=0;for(let n=0,r=t.slides.length;n<r;n+=1){const r=t.slides.eq(n);if((r.attr("data-hash")||r.attr("data-history"))===e&&!r.hasClass(t.params.slideDuplicateClass)){const e=r.index();t.slideTo(e,i,t.params.runCallbacksOnInit,!0)}}}t.params.hashNavigation.watchState&&g(p).on("hashchange",c)})()})),r("destroy",(()=>{t.params.hashNavigation.enabled&&t.params.hashNavigation.watchState&&g(p).off("hashchange",c)})),r("transitionEnd _freeModeNoMomentumRelease",(()=>{o&&u()})),r("slideChange",(()=>{o&&t.params.cssMode&&u()}))}function he(e){let t,{swiper:i,extendParams:n,on:r,emit:o}=e;function a(){const e=i.slides.eq(i.activeIndex);let n=i.params.autoplay.delay;e.attr("data-swiper-autoplay")&&(n=e.attr("data-swiper-autoplay")||i.params.autoplay.delay),clearTimeout(t),t=y((()=>{let e;i.params.autoplay.reverseDirection?i.params.loop?(i.loopFix(),e=i.slidePrev(i.params.speed,!0,!0),o("autoplay")):i.isBeginning?i.params.autoplay.stopOnLastSlide?p():(e=i.slideTo(i.slides.length-1,i.params.speed,!0,!0),o("autoplay")):(e=i.slidePrev(i.params.speed,!0,!0),o("autoplay")):i.params.loop?(i.loopFix(),e=i.slideNext(i.params.speed,!0,!0),o("autoplay")):i.isEnd?i.params.autoplay.stopOnLastSlide?p():(e=i.slideTo(0,i.params.speed,!0,!0),o("autoplay")):(e=i.slideNext(i.params.speed,!0,!0),o("autoplay")),(i.params.cssMode&&i.autoplay.running||!1===e)&&a()}),n)}function l(){return void 0===t&&!i.autoplay.running&&(i.autoplay.running=!0,o("autoplayStart"),a(),!0)}function p(){return!!i.autoplay.running&&void 0!==t&&(t&&(clearTimeout(t),t=void 0),i.autoplay.running=!1,o("autoplayStop"),!0)}function c(e){i.autoplay.running&&(i.autoplay.paused||(t&&clearTimeout(t),i.autoplay.paused=!0,0!==e&&i.params.autoplay.waitForTransition?["transitionend","webkitTransitionEnd"].forEach((e=>{i.$wrapperEl[0].addEventListener(e,d)})):(i.autoplay.paused=!1,a())))}function u(){const e=s();"hidden"===e.visibilityState&&i.autoplay.running&&c(),"visible"===e.visibilityState&&i.autoplay.paused&&(a(),i.autoplay.paused=!1)}function d(e){i&&!i.destroyed&&i.$wrapperEl&&e.target===i.$wrapperEl[0]&&(["transitionend","webkitTransitionEnd"].forEach((e=>{i.$wrapperEl[0].removeEventListener(e,d)})),i.autoplay.paused=!1,i.autoplay.running?a():p())}function h(){i.params.autoplay.disableOnInteraction?p():(o("autoplayPause"),c()),["transitionend","webkitTransitionEnd"].forEach((e=>{i.$wrapperEl[0].removeEventListener(e,d)}))}function f(){i.params.autoplay.disableOnInteraction||(i.autoplay.paused=!1,o("autoplayResume"),a())}i.autoplay={running:!1,paused:!1},n({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}}),r("init",(()=>{i.params.autoplay.enabled&&(l(),s().addEventListener("visibilitychange",u),i.params.autoplay.pauseOnMouseEnter&&(i.$el.on("mouseenter",h),i.$el.on("mouseleave",f)))})),r("beforeTransitionStart",((e,t,n)=>{i.autoplay.running&&(n||!i.params.autoplay.disableOnInteraction?i.autoplay.pause(t):p())})),r("sliderFirstMove",(()=>{i.autoplay.running&&(i.params.autoplay.disableOnInteraction?p():c())})),r("touchEnd",(()=>{i.params.cssMode&&i.autoplay.paused&&!i.params.autoplay.disableOnInteraction&&a()})),r("destroy",(()=>{i.$el.off("mouseenter",h),i.$el.off("mouseleave",f),i.autoplay.running&&p(),s().removeEventListener("visibilitychange",u)})),Object.assign(i.autoplay,{pause:c,run:a,start:l,stop:p})}function fe(e){let{swiper:t,extendParams:i,on:n}=e;i({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let r=!1,o=!1;function s(){const e=t.thumbs.swiper;if(!e||e.destroyed)return;const i=e.clickedIndex,n=e.clickedSlide;if(n&&g(n).hasClass(t.params.thumbs.slideThumbActiveClass))return;if(null==i)return;let r;if(r=e.params.loop?parseInt(g(e.clickedSlide).attr("data-swiper-slide-index"),10):i,t.params.loop){let e=t.activeIndex;t.slides.eq(e).hasClass(t.params.slideDuplicateClass)&&(t.loopFix(),t._clientLeft=t.$wrapperEl[0].clientLeft,e=t.activeIndex);const i=t.slides.eq(e).prevAll(`[data-swiper-slide-index="${r}"]`).eq(0).index(),n=t.slides.eq(e).nextAll(`[data-swiper-slide-index="${r}"]`).eq(0).index();r=void 0===i?n:void 0===n?i:n-e<e-i?n:i}t.slideTo(r)}function a(){const{thumbs:e}=t.params;if(r)return!1;r=!0;const i=t.constructor;if(e.swiper instanceof i)t.thumbs.swiper=e.swiper,Object.assign(t.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(t.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1});else if(C(e.swiper)){const n=Object.assign({},e.swiper);Object.assign(n,{watchSlidesProgress:!0,slideToClickedSlide:!1}),t.thumbs.swiper=new i(n),o=!0}return t.thumbs.swiper.$el.addClass(t.params.thumbs.thumbsContainerClass),t.thumbs.swiper.on("tap",s),!0}function l(e){const i=t.thumbs.swiper;if(!i||i.destroyed)return;const n="auto"===i.params.slidesPerView?i.slidesPerViewDynamic():i.params.slidesPerView;let r=1;const o=t.params.thumbs.slideThumbActiveClass;if(t.params.slidesPerView>1&&!t.params.centeredSlides&&(r=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(r=1),r=Math.floor(r),i.slides.removeClass(o),i.params.loop||i.params.virtual&&i.params.virtual.enabled)for(let e=0;e<r;e+=1)i.$wrapperEl.children(`[data-swiper-slide-index="${t.realIndex+e}"]`).addClass(o);else for(let e=0;e<r;e+=1)i.slides.eq(t.realIndex+e).addClass(o);const s=t.params.thumbs.autoScrollOffset,a=s&&!i.params.loop;if(t.realIndex!==i.realIndex||a){let r,o,l=i.activeIndex;if(i.params.loop){i.slides.eq(l).hasClass(i.params.slideDuplicateClass)&&(i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft,l=i.activeIndex);const e=i.slides.eq(l).prevAll(`[data-swiper-slide-index="${t.realIndex}"]`).eq(0).index(),n=i.slides.eq(l).nextAll(`[data-swiper-slide-index="${t.realIndex}"]`).eq(0).index();r=void 0===e?n:void 0===n?e:n-l==l-e?i.params.slidesPerGroup>1?n:l:n-l<l-e?n:e,o=t.activeIndex>t.previousIndex?"next":"prev"}else r=t.realIndex,o=r>t.previousIndex?"next":"prev";a&&(r+="next"===o?s:-1*s),i.visibleSlidesIndexes&&i.visibleSlidesIndexes.indexOf(r)<0&&(i.params.centeredSlides?r=r>l?r-Math.floor(n/2)+1:r+Math.floor(n/2)-1:r>l&&i.params.slidesPerGroup,i.slideTo(r,e?0:void 0))}}t.thumbs={swiper:null},n("beforeInit",(()=>{const{thumbs:e}=t.params;e&&e.swiper&&(a(),l(!0))})),n("slideChange update resize observerUpdate",(()=>{l()})),n("setTransition",((e,i)=>{const n=t.thumbs.swiper;n&&!n.destroyed&&n.setTransition(i)})),n("beforeDestroy",(()=>{const e=t.thumbs.swiper;e&&!e.destroyed&&o&&e.destroy()})),Object.assign(t.thumbs,{init:a,update:l})}function me(e){let{swiper:t,extendParams:i,emit:n,once:r}=e;i({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(t,{freeMode:{onTouchStart:function(){const e=t.getTranslate();t.setTranslate(e),t.setTransition(0),t.touchEventsData.velocities.length=0,t.freeMode.onTouchEnd({currentPos:t.rtl?t.translate:-t.translate})},onTouchMove:function(){const{touchEventsData:e,touches:i}=t;0===e.velocities.length&&e.velocities.push({position:i[t.isHorizontal()?"startX":"startY"],time:e.touchStartTime}),e.velocities.push({position:i[t.isHorizontal()?"currentX":"currentY"],time:v()})},onTouchEnd:function(e){let{currentPos:i}=e;const{params:o,$wrapperEl:s,rtlTranslate:a,snapGrid:l,touchEventsData:p}=t,c=v()-p.touchStartTime;if(i<-t.minTranslate())t.slideTo(t.activeIndex);else if(i>-t.maxTranslate())t.slides.length<l.length?t.slideTo(l.length-1):t.slideTo(t.slides.length-1);else{if(o.freeMode.momentum){if(p.velocities.length>1){const e=p.velocities.pop(),i=p.velocities.pop(),n=e.position-i.position,r=e.time-i.time;t.velocity=n/r,t.velocity/=2,Math.abs(t.velocity)<o.freeMode.minimumVelocity&&(t.velocity=0),(r>150||v()-e.time>300)&&(t.velocity=0)}else t.velocity=0;t.velocity*=o.freeMode.momentumVelocityRatio,p.velocities.length=0;let e=1e3*o.freeMode.momentumRatio;const i=t.velocity*e;let c=t.translate+i;a&&(c=-c);let u,d=!1;const h=20*Math.abs(t.velocity)*o.freeMode.momentumBounceRatio;let f;if(c<t.maxTranslate())o.freeMode.momentumBounce?(c+t.maxTranslate()<-h&&(c=t.maxTranslate()-h),u=t.maxTranslate(),d=!0,p.allowMomentumBounce=!0):c=t.maxTranslate(),o.loop&&o.centeredSlides&&(f=!0);else if(c>t.minTranslate())o.freeMode.momentumBounce?(c-t.minTranslate()>h&&(c=t.minTranslate()+h),u=t.minTranslate(),d=!0,p.allowMomentumBounce=!0):c=t.minTranslate(),o.loop&&o.centeredSlides&&(f=!0);else if(o.freeMode.sticky){let e;for(let t=0;t<l.length;t+=1)if(l[t]>-c){e=t;break}c=Math.abs(l[e]-c)<Math.abs(l[e-1]-c)||"next"===t.swipeDirection?l[e]:l[e-1],c=-c}if(f&&r("transitionEnd",(()=>{t.loopFix()})),0!==t.velocity){if(e=a?Math.abs((-c-t.translate)/t.velocity):Math.abs((c-t.translate)/t.velocity),o.freeMode.sticky){const i=Math.abs((a?-c:c)-t.translate),n=t.slidesSizesGrid[t.activeIndex];e=i<n?o.speed:i<2*n?1.5*o.speed:2.5*o.speed}}else if(o.freeMode.sticky)return void t.slideToClosest();o.freeMode.momentumBounce&&d?(t.updateProgress(u),t.setTransition(e),t.setTranslate(c),t.transitionStart(!0,t.swipeDirection),t.animating=!0,s.transitionEnd((()=>{t&&!t.destroyed&&p.allowMomentumBounce&&(n("momentumBounce"),t.setTransition(o.speed),setTimeout((()=>{t.setTranslate(u),s.transitionEnd((()=>{t&&!t.destroyed&&t.transitionEnd()}))}),0))}))):t.velocity?(n("_freeModeNoMomentumRelease"),t.updateProgress(c),t.setTransition(e),t.setTranslate(c),t.transitionStart(!0,t.swipeDirection),t.animating||(t.animating=!0,s.transitionEnd((()=>{t&&!t.destroyed&&t.transitionEnd()})))):t.updateProgress(c),t.updateActiveIndex(),t.updateSlidesClasses()}else{if(o.freeMode.sticky)return void t.slideToClosest();o.freeMode&&n("_freeModeNoMomentumRelease")}(!o.freeMode.momentum||c>=o.longSwipesMs)&&(t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses())}}}})}function ge(e){let t,i,n,{swiper:r,extendParams:o}=e;o({grid:{rows:1,fill:"column"}}),r.grid={initSlides:e=>{const{slidesPerView:o}=r.params,{rows:s,fill:a}=r.params.grid;i=t/s,n=Math.floor(e/s),t=Math.floor(e/s)===e/s?e:Math.ceil(e/s)*s,"auto"!==o&&"row"===a&&(t=Math.max(t,o*s))},updateSlide:(e,o,s,a)=>{const{slidesPerGroup:l,spaceBetween:p}=r.params,{rows:c,fill:u}=r.params.grid;let d,h,f;if("row"===u&&l>1){const i=Math.floor(e/(l*c)),n=e-c*l*i,r=0===i?l:Math.min(Math.ceil((s-i*c*l)/c),l);f=Math.floor(n/r),h=n-f*r+i*l,d=h+f*t/c,o.css({"-webkit-order":d,order:d})}else"column"===u?(h=Math.floor(e/c),f=e-h*c,(h>n||h===n&&f===c-1)&&(f+=1,f>=c&&(f=0,h+=1))):(f=Math.floor(e/i),h=e-f*i);o.css(a("margin-top"),0!==f?p&&`${p}px`:"")},updateWrapperSize:(e,i,n)=>{const{spaceBetween:o,centeredSlides:s,roundLengths:a}=r.params,{rows:l}=r.params.grid;if(r.virtualSize=(e+o)*t,r.virtualSize=Math.ceil(r.virtualSize/l)-o,r.$wrapperEl.css({[n("width")]:`${r.virtualSize+o}px`}),s){i.splice(0,i.length);const e=[];for(let t=0;t<i.length;t+=1){let n=i[t];a&&(n=Math.floor(n)),i[t]<r.virtualSize+i[0]&&e.push(n)}i.push(...e)}}}}function ye(e){const t=this,{$wrapperEl:i,params:n}=t;if(n.loop&&t.loopDestroy(),"object"==typeof e&&"length"in e)for(let t=0;t<e.length;t+=1)e[t]&&i.append(e[t]);else i.append(e);n.loop&&t.loopCreate(),n.observer||t.update()}function ve(e){const t=this,{params:i,$wrapperEl:n,activeIndex:r}=t;i.loop&&t.loopDestroy();let o=r+1;if("object"==typeof e&&"length"in e){for(let t=0;t<e.length;t+=1)e[t]&&n.prepend(e[t]);o=r+e.length}else n.prepend(e);i.loop&&t.loopCreate(),i.observer||t.update(),t.slideTo(o,0,!1)}function be(e,t){const i=this,{$wrapperEl:n,params:r,activeIndex:o}=i;let s=o;r.loop&&(s-=i.loopedSlides,i.loopDestroy(),i.slides=n.children(`.${r.slideClass}`));const a=i.slides.length;if(e<=0)return void i.prependSlide(t);if(e>=a)return void i.appendSlide(t);let l=s>e?s+1:s;const p=[];for(let t=a-1;t>=e;t-=1){const e=i.slides.eq(t);e.remove(),p.unshift(e)}if("object"==typeof t&&"length"in t){for(let e=0;e<t.length;e+=1)t[e]&&n.append(t[e]);l=s>e?s+t.length:s}else n.append(t);for(let e=0;e<p.length;e+=1)n.append(p[e]);r.loop&&i.loopCreate(),r.observer||i.update(),r.loop?i.slideTo(l+i.loopedSlides,0,!1):i.slideTo(l,0,!1)}function Ce(e){const t=this,{params:i,$wrapperEl:n,activeIndex:r}=t;let o=r;i.loop&&(o-=t.loopedSlides,t.loopDestroy(),t.slides=n.children(`.${i.slideClass}`));let s,a=o;if("object"==typeof e&&"length"in e){for(let i=0;i<e.length;i+=1)s=e[i],t.slides[s]&&t.slides.eq(s).remove(),s<a&&(a-=1);a=Math.max(a,0)}else s=e,t.slides[s]&&t.slides.eq(s).remove(),s<a&&(a-=1),a=Math.max(a,0);i.loop&&t.loopCreate(),i.observer||t.update(),i.loop?t.slideTo(a+t.loopedSlides,0,!1):t.slideTo(a,0,!1)}function we(){const e=this,t=[];for(let i=0;i<e.slides.length;i+=1)t.push(i);e.removeSlide(t)}function Se(e){let{swiper:t}=e;Object.assign(t,{appendSlide:ye.bind(t),prependSlide:ve.bind(t),addSlide:be.bind(t),removeSlide:Ce.bind(t),removeAllSlides:we.bind(t)})}function Ee(e){const{effect:t,swiper:i,on:n,setTranslate:r,setTransition:o,overwriteParams:s,perspective:a,recreateShadows:l,getEffectParams:p}=e;let c;n("beforeInit",(()=>{if(i.params.effect!==t)return;i.classNames.push(`${i.params.containerModifierClass}${t}`),a&&a()&&i.classNames.push(`${i.params.containerModifierClass}3d`);const e=s?s():{};Object.assign(i.params,e),Object.assign(i.originalParams,e)})),n("setTranslate",(()=>{i.params.effect===t&&r()})),n("setTransition",((e,n)=>{i.params.effect===t&&o(n)})),n("transitionEnd",(()=>{if(i.params.effect===t&&l){if(!p||!p().slideShadows)return;i.slides.each((e=>{i.$(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").remove()})),l()}})),n("virtualUpdate",(()=>{i.params.effect===t&&(i.slides.length||(c=!0),requestAnimationFrame((()=>{c&&i.slides&&i.slides.length&&(r(),c=!1)})))}))}function xe(e,t){return e.transformEl?t.find(e.transformEl).css({"backface-visibility":"hidden","-webkit-backface-visibility":"hidden"}):t}function Pe(e){let{swiper:t,duration:i,transformEl:n,allSlides:r}=e;const{slides:o,activeIndex:s,$wrapperEl:a}=t;if(t.params.virtualTranslate&&0!==i){let e,i=!1;e=r?n?o.find(n):o:n?o.eq(s).find(n):o.eq(s),e.transitionEnd((()=>{if(i)return;if(!t||t.destroyed)return;i=!0,t.animating=!1;const e=["webkitTransitionEnd","transitionend"];for(let t=0;t<e.length;t+=1)a.trigger(e[t])}))}}function _e(e){let{swiper:t,extendParams:i,on:n}=e;i({fadeEffect:{crossFade:!1,transformEl:null}}),Ee({effect:"fade",swiper:t,on:n,setTranslate:()=>{const{slides:e}=t,i=t.params.fadeEffect;for(let n=0;n<e.length;n+=1){const e=t.slides.eq(n);let r=-e[0].swiperSlideOffset;t.params.virtualTranslate||(r-=t.translate);let o=0;t.isHorizontal()||(o=r,r=0);const s=t.params.fadeEffect.crossFade?Math.max(1-Math.abs(e[0].progress),0):1+Math.min(Math.max(e[0].progress,-1),0);xe(i,e).css({opacity:s}).transform(`translate3d(${r}px, ${o}px, 0px)`)}},setTransition:e=>{const{transformEl:i}=t.params.fadeEffect;(i?t.slides.find(i):t.slides).transition(e),Pe({swiper:t,duration:e,transformEl:i,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}function Ae(e){let{swiper:t,extendParams:i,on:n}=e;i({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const r=(e,t,i)=>{let n=i?e.find(".swiper-slide-shadow-left"):e.find(".swiper-slide-shadow-top"),r=i?e.find(".swiper-slide-shadow-right"):e.find(".swiper-slide-shadow-bottom");0===n.length&&(n=g(`<div class="swiper-slide-shadow-${i?"left":"top"}"></div>`),e.append(n)),0===r.length&&(r=g(`<div class="swiper-slide-shadow-${i?"right":"bottom"}"></div>`),e.append(r)),n.length&&(n[0].style.opacity=Math.max(-t,0)),r.length&&(r[0].style.opacity=Math.max(t,0))};Ee({effect:"cube",swiper:t,on:n,setTranslate:()=>{const{$el:e,$wrapperEl:i,slides:n,width:o,height:s,rtlTranslate:a,size:l,browser:p}=t,c=t.params.cubeEffect,u=t.isHorizontal(),d=t.virtual&&t.params.virtual.enabled;let h,f=0;c.shadow&&(u?(h=i.find(".swiper-cube-shadow"),0===h.length&&(h=g('<div class="swiper-cube-shadow"></div>'),i.append(h)),h.css({height:`${o}px`})):(h=e.find(".swiper-cube-shadow"),0===h.length&&(h=g('<div class="swiper-cube-shadow"></div>'),e.append(h))));for(let e=0;e<n.length;e+=1){const t=n.eq(e);let i=e;d&&(i=parseInt(t.attr("data-swiper-slide-index"),10));let o=90*i,s=Math.floor(o/360);a&&(o=-o,s=Math.floor(-o/360));const p=Math.max(Math.min(t[0].progress,1),-1);let h=0,m=0,g=0;i%4==0?(h=4*-s*l,g=0):(i-1)%4==0?(h=0,g=4*-s*l):(i-2)%4==0?(h=l+4*s*l,g=l):(i-3)%4==0&&(h=-l,g=3*l+4*l*s),a&&(h=-h),u||(m=h,h=0);const y=`rotateX(${u?0:-o}deg) rotateY(${u?o:0}deg) translate3d(${h}px, ${m}px, ${g}px)`;p<=1&&p>-1&&(f=90*i+90*p,a&&(f=90*-i-90*p)),t.transform(y),c.slideShadows&&r(t,p,u)}if(i.css({"-webkit-transform-origin":`50% 50% -${l/2}px`,"transform-origin":`50% 50% -${l/2}px`}),c.shadow)if(u)h.transform(`translate3d(0px, ${o/2+c.shadowOffset}px, ${-o/2}px) rotateX(90deg) rotateZ(0deg) scale(${c.shadowScale})`);else{const e=Math.abs(f)-90*Math.floor(Math.abs(f)/90),t=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),i=c.shadowScale,n=c.shadowScale/t,r=c.shadowOffset;h.transform(`scale3d(${i}, 1, ${n}) translate3d(0px, ${s/2+r}px, ${-s/2/n}px) rotateX(-90deg)`)}const m=p.isSafari||p.isWebView?-l/2:0;i.transform(`translate3d(0px,0,${m}px) rotateX(${t.isHorizontal()?0:f}deg) rotateY(${t.isHorizontal()?-f:0}deg)`),i[0].style.setProperty("--swiper-cube-translate-z",`${m}px`)},setTransition:e=>{const{$el:i,slides:n}=t;n.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),t.params.cubeEffect.shadow&&!t.isHorizontal()&&i.find(".swiper-cube-shadow").transition(e)},recreateShadows:()=>{const e=t.isHorizontal();t.slides.each((t=>{const i=Math.max(Math.min(t.progress,1),-1);r(g(t),i,e)}))},getEffectParams:()=>t.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})}function Te(e,t,i){const n="swiper-slide-shadow"+(i?`-${i}`:""),r=e.transformEl?t.find(e.transformEl):t;let o=r.children(`.${n}`);return o.length||(o=g(`<div class="swiper-slide-shadow${i?`-${i}`:""}"></div>`),r.append(o)),o}function Ie(e){let{swiper:t,extendParams:i,on:n}=e;i({flipEffect:{slideShadows:!0,limitRotation:!0,transformEl:null}});const r=(e,i,n)=>{let r=t.isHorizontal()?e.find(".swiper-slide-shadow-left"):e.find(".swiper-slide-shadow-top"),o=t.isHorizontal()?e.find(".swiper-slide-shadow-right"):e.find(".swiper-slide-shadow-bottom");0===r.length&&(r=Te(n,e,t.isHorizontal()?"left":"top")),0===o.length&&(o=Te(n,e,t.isHorizontal()?"right":"bottom")),r.length&&(r[0].style.opacity=Math.max(-i,0)),o.length&&(o[0].style.opacity=Math.max(i,0))};Ee({effect:"flip",swiper:t,on:n,setTranslate:()=>{const{slides:e,rtlTranslate:i}=t,n=t.params.flipEffect;for(let o=0;o<e.length;o+=1){const s=e.eq(o);let a=s[0].progress;t.params.flipEffect.limitRotation&&(a=Math.max(Math.min(s[0].progress,1),-1));const l=s[0].swiperSlideOffset;let p=-180*a,c=0,u=t.params.cssMode?-l-t.translate:-l,d=0;t.isHorizontal()?i&&(p=-p):(d=u,u=0,c=-p,p=0),s[0].style.zIndex=-Math.abs(Math.round(a))+e.length,n.slideShadows&&r(s,a,n);const h=`translate3d(${u}px, ${d}px, 0px) rotateX(${c}deg) rotateY(${p}deg)`;xe(n,s).transform(h)}},setTransition:e=>{const{transformEl:i}=t.params.flipEffect;(i?t.slides.find(i):t.slides).transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),Pe({swiper:t,duration:e,transformEl:i})},recreateShadows:()=>{const e=t.params.flipEffect;t.slides.each((i=>{const n=g(i);let o=n[0].progress;t.params.flipEffect.limitRotation&&(o=Math.max(Math.min(i.progress,1),-1)),r(n,o,e)}))},getEffectParams:()=>t.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}function Oe(e){let{swiper:t,extendParams:i,on:n}=e;i({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0,transformEl:null}}),Ee({effect:"coverflow",swiper:t,on:n,setTranslate:()=>{const{width:e,height:i,slides:n,slidesSizesGrid:r}=t,o=t.params.coverflowEffect,s=t.isHorizontal(),a=t.translate,l=s?e/2-a:i/2-a,p=s?o.rotate:-o.rotate,c=o.depth;for(let e=0,t=n.length;e<t;e+=1){const t=n.eq(e),i=r[e],a=(l-t[0].swiperSlideOffset-i/2)/i,u="function"==typeof o.modifier?o.modifier(a):a*o.modifier;let d=s?p*u:0,h=s?0:p*u,f=-c*Math.abs(u),m=o.stretch;"string"==typeof m&&-1!==m.indexOf("%")&&(m=parseFloat(o.stretch)/100*i);let g=s?0:m*u,y=s?m*u:0,v=1-(1-o.scale)*Math.abs(u);Math.abs(y)<.001&&(y=0),Math.abs(g)<.001&&(g=0),Math.abs(f)<.001&&(f=0),Math.abs(d)<.001&&(d=0),Math.abs(h)<.001&&(h=0),Math.abs(v)<.001&&(v=0);const b=`translate3d(${y}px,${g}px,${f}px)  rotateX(${h}deg) rotateY(${d}deg) scale(${v})`;if(xe(o,t).transform(b),t[0].style.zIndex=1-Math.abs(Math.round(u)),o.slideShadows){let e=s?t.find(".swiper-slide-shadow-left"):t.find(".swiper-slide-shadow-top"),i=s?t.find(".swiper-slide-shadow-right"):t.find(".swiper-slide-shadow-bottom");0===e.length&&(e=Te(o,t,s?"left":"top")),0===i.length&&(i=Te(o,t,s?"right":"bottom")),e.length&&(e[0].style.opacity=u>0?u:0),i.length&&(i[0].style.opacity=-u>0?-u:0)}}},setTransition:e=>{const{transformEl:i}=t.params.coverflowEffect;(i?t.slides.find(i):t.slides).transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}function Me(e){let{swiper:t,extendParams:i,on:n}=e;i({creativeEffect:{transformEl:null,limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});const r=e=>"string"==typeof e?e:`${e}px`;Ee({effect:"creative",swiper:t,on:n,setTranslate:()=>{const{slides:e,$wrapperEl:i,slidesSizesGrid:n}=t,o=t.params.creativeEffect,{progressMultiplier:s}=o,a=t.params.centeredSlides;if(a){const e=n[0]/2-t.params.slidesOffsetBefore||0;i.transform(`translateX(calc(50% - ${e}px))`)}for(let i=0;i<e.length;i+=1){const n=e.eq(i),l=n[0].progress,p=Math.min(Math.max(n[0].progress,-o.limitProgress),o.limitProgress);let c=p;a||(c=Math.min(Math.max(n[0].originalProgress,-o.limitProgress),o.limitProgress));const u=n[0].swiperSlideOffset,d=[t.params.cssMode?-u-t.translate:-u,0,0],h=[0,0,0];let f=!1;t.isHorizontal()||(d[1]=d[0],d[0]=0);let m={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};p<0?(m=o.next,f=!0):p>0&&(m=o.prev,f=!0),d.forEach(((e,t)=>{d[t]=`calc(${e}px + (${r(m.translate[t])} * ${Math.abs(p*s)}))`})),h.forEach(((e,t)=>{h[t]=m.rotate[t]*Math.abs(p*s)})),n[0].style.zIndex=-Math.abs(Math.round(l))+e.length;const g=d.join(", "),y=`rotateX(${h[0]}deg) rotateY(${h[1]}deg) rotateZ(${h[2]}deg)`,v=c<0?`scale(${1+(1-m.scale)*c*s})`:`scale(${1-(1-m.scale)*c*s})`,b=c<0?1+(1-m.opacity)*c*s:1-(1-m.opacity)*c*s,C=`translate3d(${g}) ${y} ${v}`;if(f&&m.shadow||!f){let e=n.children(".swiper-slide-shadow");if(0===e.length&&m.shadow&&(e=Te(o,n)),e.length){const t=o.shadowPerProgress?p*(1/o.limitProgress):p;e[0].style.opacity=Math.min(Math.max(Math.abs(t),0),1)}}const w=xe(o,n);w.transform(C).css({opacity:b}),m.origin&&w.css("transform-origin",m.origin)}},setTransition:e=>{const{transformEl:i}=t.params.creativeEffect;(i?t.slides.find(i):t.slides).transition(e).find(".swiper-slide-shadow").transition(e),Pe({swiper:t,duration:e,transformEl:i,allSlides:!0})},perspective:()=>t.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode})})}function Ve(e){let{swiper:t,extendParams:i,on:n}=e;i({cardsEffect:{slideShadows:!0,transformEl:null,rotate:!0}}),Ee({effect:"cards",swiper:t,on:n,setTranslate:()=>{const{slides:e,activeIndex:i}=t,n=t.params.cardsEffect,{startTranslate:r,isTouched:o}=t.touchEventsData,s=t.translate;for(let a=0;a<e.length;a+=1){const l=e.eq(a),p=l[0].progress,c=Math.min(Math.max(p,-4),4);let u=l[0].swiperSlideOffset;t.params.centeredSlides&&!t.params.cssMode&&t.$wrapperEl.transform(`translateX(${t.minTranslate()}px)`),t.params.centeredSlides&&t.params.cssMode&&(u-=e[0].swiperSlideOffset);let d=t.params.cssMode?-u-t.translate:-u,h=0;const f=-100*Math.abs(c);let m=1,g=-2*c,y=8-.75*Math.abs(c);const v=t.virtual&&t.params.virtual.enabled?t.virtual.from+a:a,b=(v===i||v===i-1)&&c>0&&c<1&&(o||t.params.cssMode)&&s<r,C=(v===i||v===i+1)&&c<0&&c>-1&&(o||t.params.cssMode)&&s>r;if(b||C){const e=(1-Math.abs((Math.abs(c)-.5)/.5))**.5;g+=-28*c*e,m+=-.5*e,y+=96*e,h=-25*e*Math.abs(c)+"%"}if(d=c<0?`calc(${d}px + (${y*Math.abs(c)}%))`:c>0?`calc(${d}px + (-${y*Math.abs(c)}%))`:`${d}px`,!t.isHorizontal()){const e=h;h=d,d=e}const w=c<0?""+(1+(1-m)*c):""+(1-(1-m)*c),S=`\n        translate3d(${d}, ${h}, ${f}px)\n        rotateZ(${n.rotate?g:0}deg)\n        scale(${w})\n      `;if(n.slideShadows){let e=l.find(".swiper-slide-shadow");0===e.length&&(e=Te(n,l)),e.length&&(e[0].style.opacity=Math.min(Math.max((Math.abs(c)-.5)/.5,0),1))}l[0].style.zIndex=-Math.abs(Math.round(p))+e.length,xe(n,l).transform(S)}},setTransition:e=>{const{transformEl:i}=t.params.cardsEffect;(i?t.slides.find(i):t.slides).transition(e).find(".swiper-slide-shadow").transition(e),Pe({swiper:t,duration:e,transformEl:i})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!t.params.cssMode})})}}},t={};function i(n){var r=t[n];if(void 0!==r)return r.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,i),o.exports}return i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i(402)})()}));
//# sourceMappingURL=adaptivecards.min.js.map