import { StrictStyleOptions } from 'botframework-webchat-api';
export default function createScrollToEndButtonStyle({ paddingRegular, primaryFont, scrollToEndButtonFontSize, transcriptOverlayButtonBackground, transcriptOverlayButtonBackgroundOnFocus, transcriptOverlayButtonBackgroundOnHover, transcriptOverlayButtonColor, transcriptOverlayButtonColorOnFocus, transcriptOverlayButtonColorOnHover }: StrictStyleOptions): {
    '&.webchat__scroll-to-end-button': {
        '@media screen and (forced-colors: active)': {
            borderWidth: string;
            outline: string;
        };
        appearance: string;
        backgroundColor: string;
        borderRadius: number;
        borderWidth: number;
        bottom: number;
        color: string;
        fontFamily: string;
        fontSize: string | number;
        outline: number;
        padding: number;
        position: string;
        zIndex: number;
        '&:hover': {
            backgroundColor: string;
            color: string;
        };
        '&:focus': {
            backgroundColor: string;
            color: string;
        };
        '&:not(.webchat__scroll-to-end-button--rtl)': {
            right: number;
        };
        '&.webchat__scroll-to-end-button--rtl': {
            left: number;
        };
    };
};
//# sourceMappingURL=ScrollToEndButton.d.ts.map