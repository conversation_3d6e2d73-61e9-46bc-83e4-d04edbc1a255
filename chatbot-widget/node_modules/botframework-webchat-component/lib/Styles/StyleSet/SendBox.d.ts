import { StrictStyleOptions } from 'botframework-webchat-api';
export default function createSendBoxStyle({ sendBoxBackground, sendBoxBorderBottom, sendBoxBorderLeft, sendBoxBorderRight, sendBoxBorderTop, sendBoxHeight }: StrictStyleOptions): {
    '&.webchat__send-box': {
        '& .webchat__send-box__button--align-bottom': {
            alignSelf: string;
        };
        '& .webchat__send-box__button--align-stretch': {
            alignSelf: string;
        };
        '& .webchat__send-box__button--align-top': {
            alignSelf: string;
        };
        '& .webchat__send-box__main': {
            alignItems: string;
            backgroundColor: string;
            borderBottom: string | number;
            borderLeft: string | number;
            borderRight: string | number;
            borderTop: string | number;
            minHeight: string | number;
        };
    };
};
//# sourceMappingURL=SendBox.d.ts.map