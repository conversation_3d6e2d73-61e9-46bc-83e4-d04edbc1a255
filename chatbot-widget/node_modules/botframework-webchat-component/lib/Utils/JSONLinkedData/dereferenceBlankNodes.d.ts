/**
 * Dereferences all unconnected blank nodes to their corresponding blank node. This is done by replacing all unconnected blank nodes in a graph and purposefully introduce cyclic dependencies to help querying the graph.
 *
 * This function will always return a new instance of all objects in the graph.
 *
 * This function assumes the graph conforms to JSON-LD, notably:
 *
 * - For nodes that share the same blank node identifier, one of them should be connected and the other must be unconnected
 * - If none of them are connected node, these unconnected blank node will not be replaced
 *
 * @see https://json-ld.github.io/json-ld.org/spec/latest/json-ld/#data-model-overview
 * @param graph A list of nodes in the graph.
 * @returns A structured clone of graph with unconnected blank nodes replaced by their corresponding blank node.
 */
export default function dereferenceBlankNodes<T extends object>(graph: T[]): readonly T[];
//# sourceMappingURL=dereferenceBlankNodes.d.ts.map