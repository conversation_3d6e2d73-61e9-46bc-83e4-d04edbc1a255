import React from 'react';
import { type LinkOptions } from './betterLinks';
type Plural = {
    zero?: string;
    one?: string;
    two?: string;
    few?: string;
    many?: string;
    other: string;
};
type PluralProps = Readonly<{
    stringIds: Plural;
    values: readonly [number, ...(number | string)[]] | undefined;
}>;
type SingularProps = Readonly<{
    stringIds: string;
    values?: readonly (number | string)[] | undefined;
}>;
type Props = Readonly<{
    className?: string | undefined;
    linkClassName?: string | undefined;
    onDecorateLink?: ((href: string, textContent: string) => LinkOptions | undefined) | undefined;
}> & (SingularProps | PluralProps);
declare const _default: React.MemoExoticComponent<(props: Props) => React.JSX.Element>;
export default _default;
//# sourceMappingURL=LocalizedString.d.ts.map