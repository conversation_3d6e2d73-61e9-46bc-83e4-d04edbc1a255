import type { FC, PropsWithChildren } from 'react';
type LiveRegionTwinComposerProps = PropsWithChildren<{
    /** Optional "aria-label" attribute for the live region twin container. */
    'aria-label'?: string;
    /** "aria-live" attribute for the live region twin container, defaults to `'polite'`. */
    'aria-live'?: 'assertive' | 'polite';
    /** Optional "aria-roledescription" attribute for the live region twin container. */
    'aria-roledescription'?: string;
    /** Optional "className" attribute for the live region twin container. */
    className?: string;
    /**
     * Static elements will fade out after this timeout value specified in milliseconds, defaults to `1000`.
     *
     * When lowering this value, make sure screen reader can continue to pick up new static elements before fading out.
     *
     * If this prop is updated, it will be reflected in next queueing elements.
     */
    fadeAfter?: number;
    /** Optional "role" attribute for the live region twin container. */
    role?: string;
    /** Optional "className" attribute for static text element. */
    textElementClassName?: string;
}>;
/**
 * Live region twin is an UI component for queueing texts or elements to the screen reader using
 * a container element with `aria-live` attribute set.
 *
 * After the text is rendered and queued, it will be removed to reduce burden on the DOM tree.
 * Currently, we assume the assistive technologies should pick up the text within 1 second of rendering.
 * This value is configurable.
 *
 * By default, the live region is visible. If is is not desirable, the caller can use `className` prop to
 * hide its visuals.
 */
declare const LiveRegionTwinComposer: FC<LiveRegionTwinComposerProps>;
export default LiveRegionTwinComposer;
//# sourceMappingURL=LiveRegionTwinComposer.d.ts.map