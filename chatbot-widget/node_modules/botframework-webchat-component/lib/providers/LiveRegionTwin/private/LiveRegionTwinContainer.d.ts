import type { VFC } from 'react';
type LiveRegionTwinContainerProps = {
    'aria-label'?: string;
    'aria-live': 'assertive' | 'polite';
    'aria-roledescription'?: string;
    className?: string;
    role?: string;
    textElementClassName?: string;
};
declare const LiveRegionTwinContainer: VFC<LiveRegionTwinContainerProps>;
export default LiveRegionTwinContainer;
//# sourceMappingURL=LiveRegionTwinContainer.d.ts.map