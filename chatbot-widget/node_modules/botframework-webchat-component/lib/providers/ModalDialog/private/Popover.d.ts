import React, { type PropsWithChildren } from 'react';
type Props = Readonly<PropsWithChildren<{
    'aria-label'?: string;
    'aria-labelledby'?: string;
    className?: string;
    onDismiss?: () => void;
}>>;
declare const ModalDialog: React.MemoExoticComponent<({ "aria-label": ariaLabel, "aria-labelledby": ariaLabelledBy, children, className, onDismiss }: Props) => React.JSX.Element>;
export default ModalDialog;
//# sourceMappingURL=Popover.d.ts.map