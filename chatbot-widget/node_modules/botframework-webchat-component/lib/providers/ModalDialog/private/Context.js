"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = require("react");
var defaultContextValue = {
  close: {
    get: function get() {
      throw new Error('close cannot be used outside of <ModalDialogComposer>.');
    }
  },
  showModal: {
    get: function get() {
      throw new Error('showModal cannot be used outside of <ModalDialogComposer>.');
    }
  }
};
var Context = /*#__PURE__*/(0, _react.createContext)(Object.create({}, defaultContextValue));
Context.displayName = 'ModalDialogComposer';
var _default = Context;
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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