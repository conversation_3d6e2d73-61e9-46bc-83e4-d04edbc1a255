"use strict";

function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _botframeworkWebchatApi = require("botframework-webchat-api");
var _classnames = _interopRequireDefault(require("classnames"));
var _react = _interopRequireWildcard(require("react"));
var _useStyleSet3 = _interopRequireDefault(require("../../../hooks/useStyleSet"));
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }
var useLocalizer = _botframeworkWebchatApi.hooks.useLocalizer;
var ModalDialog = /*#__PURE__*/(0, _react.memo)(function (_ref) {
  var ariaLabel = _ref['aria-label'],
    ariaLabelledBy = _ref['aria-labelledby'],
    children = _ref.children,
    className = _ref.className,
    onDismiss = _ref.onDismiss;
  var _useStyleSet = (0, _useStyleSet3.default)(),
    _useStyleSet2 = _slicedToArray(_useStyleSet, 1),
    modalDialogStyleSet = _useStyleSet2[0].modalDialog;
  var dialogRef = (0, _react.useRef)(null);
  var localize = useLocalizer();
  var closeButtonAlt = localize('KEYBOARD_HELP_CLOSE_BUTTON_ALT');
  var handleCloseButtonClick = (0, _react.useCallback)(function () {
    var _dialogRef$current;
    return (_dialogRef$current = dialogRef.current) === null || _dialogRef$current === void 0 ? void 0 : _dialogRef$current.close();
  }, [dialogRef]);
  (0, _react.useEffect)(function () {
    var _dialogRef$current2;
    return (_dialogRef$current2 = dialogRef.current) === null || _dialogRef$current2 === void 0 ? void 0 : _dialogRef$current2.showModal();
  }, [dialogRef]);
  return /*#__PURE__*/_react.default.createElement("dialog", {
    // When "aria-labelledby" is set, it must not set "aria-label".
    "aria-label": !ariaLabelledBy ? ariaLabel : undefined,
    "aria-labelledby": ariaLabelledBy,
    className: (0, _classnames.default)('webchat__modal-dialog', className, modalDialogStyleSet + ''),
    onClose: onDismiss,
    open: false,
    ref: dialogRef,
    role: "dialog"
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "webchat__modal-dialog__box"
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "webchat__modal-dialog__close-button-layout"
  }, /*#__PURE__*/_react.default.createElement("button", {
    "aria-label": closeButtonAlt,
    className: "webchat__modal-dialog__close-button",
    onClick: handleCloseButtonClick,
    type: "button"
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "webchat__modal-dialog__close-button-border"
  }, /*#__PURE__*/_react.default.createElement("svg", {
    className: "webchat__modal-dialog__close-button-image"
    // "focusable" attribute is only available in IE11 and "tabIndex={-1}" does not work.
    ,
    focusable: false,
    role: "presentation",
    viewBox: "0 0 2048 2048",
    xmlns: "http://www.w3.org/2000/svg"
  }, /*#__PURE__*/_react.default.createElement("path", {
    d: "M2048 136l-888 888 888 888-136 136-888-888-888 888L0 1912l888-888L0 136 136 0l888 888L1912 0l136 136z"
  }))))), /*#__PURE__*/_react.default.createElement("div", {
    className: "webchat__modal-dialog__body"
  }, children)));
});
ModalDialog.displayName = 'ModalDialog';
var _default = ModalDialog;
exports.default = _default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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