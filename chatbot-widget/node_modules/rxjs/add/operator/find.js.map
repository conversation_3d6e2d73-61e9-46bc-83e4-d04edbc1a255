{"version": 3, "file": "find.js", "sourceRoot": "", "sources": ["../../../src/add/operator/find.ts"], "names": [], "mappings": ";AACA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,qBAAqB,qBAAqB,CAAC,CAAA;AAE3C,uBAAU,CAAC,SAAS,CAAC,IAAI,GAAG,WAAI,CAAC", "sourcesContent": ["\nimport { Observable } from '../../Observable';\nimport { find } from '../../operator/find';\n\nObservable.prototype.find = find;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    find: typeof find;\n  }\n}"]}