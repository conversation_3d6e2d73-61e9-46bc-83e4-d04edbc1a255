{"version": 3, "file": "distinct.js", "sourceRoot": "", "sources": ["../../../src/add/operator/distinct.ts"], "names": [], "mappings": ";AAAA,2BAA2B,kBAAkB,CAAC,CAAA;AAC9C,yBAAyB,yBAAyB,CAAC,CAAA;AAEnD,uBAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,mBAAQ,CAAC", "sourcesContent": ["import { Observable } from '../../Observable';\nimport { distinct } from '../../operator/distinct';\n\nObservable.prototype.distinct = distinct;\n\ndeclare module '../../Observable' {\n  interface Observable<T> {\n    distinct: typeof distinct;\n  }\n}"]}