{"version": 3, "file": "sample.js", "sourceRoot": "", "sources": ["../../src/operator/sample.ts"], "names": [], "mappings": ";AACA,uBAAsC,qBAAqB,CAAC,CAAA;AAE5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCG;AACH,gBAA+C,QAAyB;IACtE,MAAM,CAAC,eAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAkB,CAAC;AACtD,CAAC;AAFe,cAAM,SAErB,CAAA", "sourcesContent": ["import { Observable } from '../Observable';\nimport { sample as higherOrder } from '../operators/sample';\n\n/**\n * Emits the most recently emitted value from the source Observable whenever\n * another Observable, the `notifier`, emits.\n *\n * <span class=\"informal\">It's like {@link sampleTime}, but samples whenever\n * the `notifier` Observable emits something.</span>\n *\n * <img src=\"./img/sample.png\" width=\"100%\">\n *\n * Whenever the `notifier` Observable emits a value or completes, `sample`\n * looks at the source Observable and emits whichever value it has most recently\n * emitted since the previous sampling, unless the source has not emitted\n * anything since the previous sampling. The `notifier` is subscribed to as soon\n * as the output Observable is subscribed.\n *\n * @example <caption>On every click, sample the most recent \"seconds\" timer</caption>\n * var seconds = Rx.Observable.interval(1000);\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var result = seconds.sample(clicks);\n * result.subscribe(x => console.log(x));\n *\n * @see {@link audit}\n * @see {@link debounce}\n * @see {@link sampleTime}\n * @see {@link throttle}\n *\n * @param {Observable<any>} notifier The Observable to use for sampling the\n * source Observable.\n * @return {Observable<T>} An Observable that emits the results of sampling the\n * values emitted by the source Observable whenever the notifier Observable\n * emits value or completes.\n * @method sample\n * @owner Observable\n */\nexport function sample<T>(this: Observable<T>, notifier: Observable<any>): Observable<T> {\n  return higherOrder(notifier)(this) as Observable<T>;\n}\n"]}