{"version": 3, "file": "combineAll.js", "sourceRoot": "", "sources": ["../../src/operator/combineAll.ts"], "names": [], "mappings": ";AAEA,2BAA0C,yBAAyB,CAAC,CAAA;AAEpE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG;AACH,oBAAsD,OAAsC;IAC1F,MAAM,CAAC,uBAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;AACpC,CAAC;AAFe,kBAAU,aAEzB,CAAA", "sourcesContent": ["\nimport { Observable } from '../Observable';\nimport { combineAll as higherOrder } from '../operators/combineAll';\n\n/**\n * Converts a higher-order Observable into a first-order Observable by waiting\n * for the outer Observable to complete, then applying {@link combineLatest}.\n *\n * <span class=\"informal\">Flattens an Observable-of-Observables by applying\n * {@link combineLatest} when the Observable-of-Observables completes.</span>\n *\n * <img src=\"./img/combineAll.png\" width=\"100%\">\n *\n * Takes an Observable of Observables, and collects all Observables from it.\n * Once the outer Observable completes, it subscribes to all collected\n * Observables and combines their values using the {@link combineLatest}\n * strategy, such that:\n * - Every time an inner Observable emits, the output Observable emits.\n * - When the returned observable emits, it emits all of the latest values by:\n *   - If a `project` function is provided, it is called with each recent value\n *     from each inner Observable in whatever order they arrived, and the result\n *     of the `project` function is what is emitted by the output Observable.\n *   - If there is no `project` function, an array of all of the most recent\n *     values is emitted by the output Observable.\n *\n * @example <caption>Map two click events to a finite interval Observable, then apply combineAll</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var higherOrder = clicks.map(ev =>\n *   Rx.Observable.interval(Math.random()*2000).take(3)\n * ).take(2);\n * var result = higherOrder.combineAll();\n * result.subscribe(x => console.log(x));\n *\n * @see {@link combineLatest}\n * @see {@link mergeAll}\n *\n * @param {function} [project] An optional function to map the most recent\n * values from each inner Observable into a new result. Takes each of the most\n * recent values from each collected inner Observable as arguments, in order.\n * @return {Observable} An Observable of projected results or arrays of recent\n * values.\n * @method combineAll\n * @owner Observable\n */\nexport function combineAll<T, R>(this: Observable<T>, project?: (...values: Array<any>) => R): Observable<R> {\n  return higherOrder(project)(this);\n}\n"]}