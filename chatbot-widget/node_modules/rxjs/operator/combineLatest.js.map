{"version": 3, "file": "combineLatest.js", "sourceRoot": "", "sources": ["../../src/operator/combineLatest.ts"], "names": [], "mappings": ";AACA,8BAA6C,4BAA4B,CAAC,CAAA;AAiB1E,mCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH;IAAyD,qBAE6B;SAF7B,WAE6B,CAF7B,sBAE6B,CAF7B,IAE6B;QAF7B,oCAE6B;;IACpF,MAAM,CAAC,6BAAW,eAAI,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3C,CAAC;AAJe,qBAAa,gBAI5B,CAAA", "sourcesContent": ["import { Observable, ObservableInput } from '../Observable';\nimport { combineLatest as higherOrder } from '../operators/combineLatest';\n\n/* tslint:disable:max-line-length */\nexport function combineLatest<T, R>(this: Observable<T>, project: (v1: T) => R): Observable<R>;\nexport function combineLatest<T, T2, R>(this: Observable<T>, v2: ObservableInput<T2>, project: (v1: T, v2: T2) => R): Observable<R>;\nexport function combineLatest<T, T2, T3, R>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, project: (v1: T, v2: T2, v3: T3) => R): Observable<R>;\nexport function combineLatest<T, T2, T3, T4, R>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, v4: ObservableInput<T4>, project: (v1: T, v2: T2, v3: T3, v4: T4) => R): Observable<R>;\nexport function combineLatest<T, T2, T3, T4, T5, R>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, v4: ObservableInput<T4>, v5: ObservableInput<T5>, project: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5) => R): Observable<R>;\nexport function combineLatest<T, T2, T3, T4, T5, T6, R>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, v4: ObservableInput<T4>, v5: ObservableInput<T5>, v6: ObservableInput<T6>, project: (v1: T, v2: T2, v3: T3, v4: T4, v5: T5, v6: T6) => R): Observable<R> ;\nexport function combineLatest<T, T2>(this: Observable<T>, v2: ObservableInput<T2>): Observable<[T, T2]>;\nexport function combineLatest<T, T2, T3>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>): Observable<[T, T2, T3]>;\nexport function combineLatest<T, T2, T3, T4>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, v4: ObservableInput<T4>): Observable<[T, T2, T3, T4]>;\nexport function combineLatest<T, T2, T3, T4, T5>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, v4: ObservableInput<T4>, v5: ObservableInput<T5>): Observable<[T, T2, T3, T4, T5]>;\nexport function combineLatest<T, T2, T3, T4, T5, T6>(this: Observable<T>, v2: ObservableInput<T2>, v3: ObservableInput<T3>, v4: ObservableInput<T4>, v5: ObservableInput<T5>, v6: ObservableInput<T6>): Observable<[T, T2, T3, T4, T5, T6]> ;\nexport function combineLatest<T, R>(this: Observable<T>, ...observables: Array<ObservableInput<T> | ((...values: Array<T>) => R)>): Observable<R>;\nexport function combineLatest<T, R>(this: Observable<T>, array: ObservableInput<T>[]): Observable<Array<T>>;\nexport function combineLatest<T, TOther, R>(this: Observable<T>, array: ObservableInput<TOther>[], project: (v1: T, ...values: Array<TOther>) => R): Observable<R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Combines multiple Observables to create an Observable whose values are\n * calculated from the latest values of each of its input Observables.\n *\n * <span class=\"informal\">Whenever any input Observable emits a value, it\n * computes a formula using the latest values from all the inputs, then emits\n * the output of that formula.</span>\n *\n * <img src=\"./img/combineLatest.png\" width=\"100%\">\n *\n * `combineLatest` combines the values from this Observable with values from\n * Observables passed as arguments. This is done by subscribing to each\n * Observable, in order, and collecting an array of each of the most recent\n * values any time any of the input Observables emits, then either taking that\n * array and passing it as arguments to an optional `project` function and\n * emitting the return value of that, or just emitting the array of recent\n * values directly if there is no `project` function.\n *\n * @example <caption>Dynamically calculate the Body-Mass Index from an Observable of weight and one for height</caption>\n * var weight = Rx.Observable.of(70, 72, 76, 79, 75);\n * var height = Rx.Observable.of(1.76, 1.77, 1.78);\n * var bmi = weight.combineLatest(height, (w, h) => w / (h * h));\n * bmi.subscribe(x => console.log('BMI is ' + x));\n *\n * // With output to console:\n * // BMI is 24.212293388429753\n * // BMI is 23.93948099205209\n * // BMI is 23.671253629592222\n *\n * @see {@link combineAll}\n * @see {@link merge}\n * @see {@link withLatestFrom}\n *\n * @param {ObservableInput} other An input Observable to combine with the source\n * Observable. More than one input Observables may be given as argument.\n * @param {function} [project] An optional function to project the values from\n * the combined latest values into a new value on the output Observable.\n * @return {Observable} An Observable of projected values from the most recent\n * values from each input Observable, or an array of the most recent values from\n * each input Observable.\n * @method combineLatest\n * @owner Observable\n */\nexport function combineLatest<T, R>(this: Observable<T>, ...observables: Array<ObservableInput<any> |\n                                                       Array<ObservableInput<any>> |\n                                                       ((...values: Array<any>) => R)>): Observable<R> {\n  return higherOrder(...observables)(this);\n}"]}