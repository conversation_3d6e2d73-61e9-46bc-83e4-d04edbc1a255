{"version": 3, "file": "window.js", "sourceRoot": "", "sources": ["../../src/operators/window.ts"], "names": [], "mappings": ";;;;;;AAEA,wBAAwB,YAAY,CAAC,CAAA;AAErC,gCAAgC,oBAAoB,CAAC,CAAA;AAErD,kCAAkC,2BAA2B,CAAC,CAAA;AAG9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,gBAA0B,gBAAiC;IACzD,MAAM,CAAC,gCAAgC,MAAqB;QAC1D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;AACJ,CAAC;AAJe,cAAM,SAIrB,CAAA;AAED;IAEE,wBAAoB,gBAAiC;QAAjC,qBAAgB,GAAhB,gBAAgB,CAAiB;IACrD,CAAC;IAED,6BAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC9D,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/B,gBAAgB,CAAC,GAAG,CAAC,qCAAiB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACnF,CAAC;QACD,MAAM,CAAC,kBAAkB,CAAC;IAC5B,CAAC;IACH,qBAAC;AAAD,CAAC,AAbD,IAaC;AAED;;;;GAIG;AACH;IAAkC,oCAAuB;IAIvD,0BAAY,WAAsC;QAChD,kBAAM,WAAW,CAAC,CAAC;QAHb,WAAM,GAAe,IAAI,iBAAO,EAAK,CAAC;QAI5C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,qCAAU,GAAV,UAAW,UAAa,EAAE,UAAe,EAC9B,UAAkB,EAAE,UAAkB,EACtC,QAAiC;QAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,sCAAW,GAAX,UAAY,KAAU,EAAE,QAAiC;QACvD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,yCAAc,GAAd,UAAe,QAAiC;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAES,gCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAES,iCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,oCAAS,GAAnB;QACE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED,oCAAoC,CAAC,uCAAY,GAAZ;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAEO,qCAAU,GAAlB;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;YACf,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;QACjD,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IACH,uBAAC;AAAD,CAAC,AAlDD,CAAkC,iCAAe,GAkDhD", "sourcesContent": ["import { Observable } from '../Observable';\nimport { OperatorFunction } from '../interfaces';\nimport { Subject } from '../Subject';\nimport { Subscriber } from '../Subscriber';\nimport { OuterSubscriber } from '../OuterSubscriber';\nimport { InnerSubscriber } from '../InnerSubscriber';\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { Operator } from '../Operator';\n\n/**\n * Branch out the source Observable values as a nested Observable whenever\n * `windowBoundaries` emits.\n *\n * <span class=\"informal\">It's like {@link buffer}, but emits a nested Observable\n * instead of an array.</span>\n *\n * <img src=\"./img/window.png\" width=\"100%\">\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable emits connected, non-overlapping\n * windows. It emits the current window and opens a new one whenever the\n * Observable `windowBoundaries` emits an item. Because each window is an\n * Observable, the output is a higher-order Observable.\n *\n * @example <caption>In every window of 1 second each, emit at most 2 click events</caption>\n * var clicks = Rx.Observable.fromEvent(document, 'click');\n * var interval = Rx.Observable.interval(1000);\n * var result = clicks.window(interval)\n *   .map(win => win.take(2)) // each window has at most 2 emissions\n *   .mergeAll(); // flatten the Observable-of-Observables\n * result.subscribe(x => console.log(x));\n *\n * @see {@link windowCount}\n * @see {@link windowTime}\n * @see {@link windowToggle}\n * @see {@link windowWhen}\n * @see {@link buffer}\n *\n * @param {Observable<any>} windowBoundaries An Observable that completes the\n * previous window and starts a new window.\n * @return {Observable<Observable<T>>} An Observable of windows, which are\n * Observables emitting values of the source Observable.\n * @method window\n * @owner Observable\n */\nexport function window<T>(windowBoundaries: Observable<any>): OperatorFunction<T, Observable<T>> {\n  return function windowOperatorFunction(source: Observable<T>) {\n    return source.lift(new WindowOperator(windowBoundaries));\n  };\n}\n\nclass WindowOperator<T> implements Operator<T, Observable<T>> {\n\n  constructor(private windowBoundaries: Observable<any>) {\n  }\n\n  call(subscriber: Subscriber<Observable<T>>, source: any): any {\n    const windowSubscriber = new WindowSubscriber(subscriber);\n    const sourceSubscription = source.subscribe(windowSubscriber);\n    if (!sourceSubscription.closed) {\n      windowSubscriber.add(subscribeToResult(windowSubscriber, this.windowBoundaries));\n    }\n    return sourceSubscription;\n  }\n}\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nclass WindowSubscriber<T> extends OuterSubscriber<T, any> {\n\n  private window: Subject<T> = new Subject<T>();\n\n  constructor(destination: Subscriber<Observable<T>>) {\n    super(destination);\n    destination.next(this.window);\n  }\n\n  notifyNext(outerValue: T, innerValue: any,\n             outerIndex: number, innerIndex: number,\n             innerSub: InnerSubscriber<T, any>): void {\n    this.openWindow();\n  }\n\n  notifyError(error: any, innerSub: InnerSubscriber<T, any>): void {\n    this._error(error);\n  }\n\n  notifyComplete(innerSub: InnerSubscriber<T, any>): void {\n    this._complete();\n  }\n\n  protected _next(value: T): void {\n    this.window.next(value);\n  }\n\n  protected _error(err: any): void {\n    this.window.error(err);\n    this.destination.error(err);\n  }\n\n  protected _complete(): void {\n    this.window.complete();\n    this.destination.complete();\n  }\n\n  /** @deprecated internal use only */ _unsubscribe() {\n    this.window = null;\n  }\n\n  private openWindow(): void  {\n    const prevWindow = this.window;\n    if (prevWindow) {\n      prevWindow.complete();\n    }\n    const destination = this.destination;\n    const newWindow = this.window = new Subject<T>();\n    destination.next(newWindow);\n  }\n}\n"]}