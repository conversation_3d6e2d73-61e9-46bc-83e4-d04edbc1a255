{"version": 3, "file": "timestamp.js", "sourceRoot": "", "sources": ["../../src/operators/timestamp.ts"], "names": [], "mappings": ";AAEA,sBAAsB,oBAAoB,CAAC,CAAA;AAE3C,oBAAoB,OAAO,CAAC,CAAA;AAE5B;;;;;GAKG;AACH,mBAA6B,SAA6B;IAA7B,yBAA6B,GAA7B,yBAA6B;IACxD,MAAM,CAAC,SAAG,CAAC,UAAC,KAAQ,IAAK,OAAA,IAAI,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,EAArC,CAAqC,CAAC,CAAC;IAChE,mFAAmF;AACrF,CAAC;AAHe,iBAAS,YAGxB,CAAA;AAED;IACE,mBAAmB,KAAQ,EAAS,SAAiB;QAAlC,UAAK,GAAL,KAAK,CAAG;QAAS,cAAS,GAAT,SAAS,CAAQ;IACrD,CAAC;IACH,gBAAC;AAAD,CAAC,AAHD,IAGC;AAHY,iBAAS,YAGrB,CAAA;AAAA,CAAC", "sourcesContent": ["\nimport { IScheduler } from '../Scheduler';\nimport { async } from '../scheduler/async';\nimport { OperatorFunction } from '../interfaces';\nimport { map } from './map';\n\n/**\n * @param scheduler\n * @return {Observable<Timestamp<any>>|WebSocketSubject<T>|Observable<T>}\n * @method timestamp\n * @owner Observable\n */\nexport function timestamp<T>(scheduler: IScheduler = async): OperatorFunction<T, Timestamp<T>> {\n  return map((value: T) => new Timestamp(value, scheduler.now()));\n  // return (source: Observable<T>) => source.lift(new TimestampOperator(scheduler));\n}\n\nexport class Timestamp<T> {\n  constructor(public value: T, public timestamp: number) {\n  }\n};\n"]}