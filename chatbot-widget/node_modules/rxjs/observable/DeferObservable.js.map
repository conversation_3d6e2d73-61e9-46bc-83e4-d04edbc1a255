{"version": 3, "file": "DeferObservable.js", "sourceRoot": "", "sources": ["../../src/observable/DeferObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,2BAAkD,eAAe,CAAC,CAAA;AAIlE,kCAAkC,2BAA2B,CAAC,CAAA;AAC9D,gCAAgC,oBAAoB,CAAC,CAAA;AACrD;;;;GAIG;AACH;IAAwC,mCAAa;IAoDnD,yBAAoB,iBAAwD;QAC1E,iBAAO,CAAC;QADU,sBAAiB,GAAjB,iBAAiB,CAAuC;IAE5E,CAAC;IApDD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6CG;IACI,sBAAM,GAAb,UAAiB,iBAAwD;QACvE,MAAM,CAAC,IAAI,eAAe,CAAC,iBAAiB,CAAC,CAAC;IAChD,CAAC;IAMD,oCAAoC,CAAC,oCAAU,GAAV,UAAW,UAAyB;QACvE,MAAM,CAAC,IAAI,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACjE,CAAC;IACH,sBAAC;AAAD,CAAC,AA3DD,CAAwC,uBAAU,GA2DjD;AA3DY,uBAAe,kBA2D3B,CAAA;AAED;IAAiC,mCAAqB;IACpD,yBAAY,WAA0B,EAClB,OAA8C;QAChE,kBAAM,WAAW,CAAC,CAAC;QADD,YAAO,GAAP,OAAO,CAAuC;QAEhE,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAEO,kCAAQ,GAAhB;QACE,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAE;QAAA,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,sCAAY,GAApB;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9B,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YACX,IAAI,CAAC,GAAG,CAAC,qCAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IACH,sBAAC;AAAD,CAAC,AArBD,CAAiC,iCAAe,GAqB/C", "sourcesContent": ["import { Observable, SubscribableOrPromise } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\n\nimport { subscribeToResult } from '../util/subscribeToResult';\nimport { OuterSubscriber } from '../OuterSubscriber';\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @extends {Ignored}\n * @hide true\n */\nexport class DeferObservable<T> extends Observable<T> {\n\n  /**\n   * Creates an Observable that, on subscribe, calls an Observable factory to\n   * make an Observable for each new Observer.\n   *\n   * <span class=\"informal\">Creates the Observable lazily, that is, only when it\n   * is subscribed.\n   * </span>\n   *\n   * <img src=\"./img/defer.png\" width=\"100%\">\n   *\n   * `defer` allows you to create the Observable only when the Observer\n   * subscribes, and create a fresh Observable for each Observer. It waits until\n   * an Observer subscribes to it, and then it generates an Observable,\n   * typically with an Observable factory function. It does this afresh for each\n   * subscriber, so although each subscriber may think it is subscribing to the\n   * same Observable, in fact each subscriber gets its own individual\n   * Observable.\n   *\n   * @example <caption>Subscribe to either an Observable of clicks or an Observable of interval, at random</caption>\n   * var clicksOrInterval = Rx.Observable.defer(function () {\n   *   if (Math.random() > 0.5) {\n   *     return Rx.Observable.fromEvent(document, 'click');\n   *   } else {\n   *     return Rx.Observable.interval(1000);\n   *   }\n   * });\n   * clicksOrInterval.subscribe(x => console.log(x));\n   *\n   * // Results in the following behavior:\n   * // If the result of Math.random() is greater than 0.5 it will listen\n   * // for clicks anywhere on the \"document\"; when document is clicked it\n   * // will log a MouseEvent object to the console. If the result is less\n   * // than 0.5 it will emit ascending numbers, one every second(1000ms).\n   *\n   * @see {@link create}\n   *\n   * @param {function(): SubscribableOrPromise} observableFactory The Observable\n   * factory function to invoke for each Observer that subscribes to the output\n   * Observable. May also return a Promise, which will be converted on the fly\n   * to an Observable.\n   * @return {Observable} An Observable whose Observers' subscriptions trigger\n   * an invocation of the given Observable factory function.\n   * @static true\n   * @name defer\n   * @owner Observable\n   */\n  static create<T>(observableFactory: () => SubscribableOrPromise<T> | void): Observable<T> {\n    return new DeferObservable(observableFactory);\n  }\n\n  constructor(private observableFactory: () => SubscribableOrPromise<T> | void) {\n    super();\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<T>): Subscription {\n    return new DeferSubscriber(subscriber, this.observableFactory);\n  }\n}\n\nclass DeferSubscriber<T> extends OuterSubscriber<T, T> {\n  constructor(destination: Subscriber<T>,\n              private factory: () => SubscribableOrPromise<T> | void) {\n    super(destination);\n    this.tryDefer();\n  }\n\n  private tryDefer(): void {\n    try {\n      this._callFactory();\n    } catch (err) {\n      this._error(err);\n    }\n  }\n\n  private _callFactory(): void {\n    const result = this.factory();\n    if (result) {\n      this.add(subscribeToResult(this, result));\n    }\n  }\n}\n"]}