{"version": 3, "file": "en-AG.js", "sourceRoot": "", "sources": ["en-AG.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,IAAI,EAAC,GAAG,EAAC,gBAAgB,EAAC,kBAAkB,EAAC,gBAAgB,EAAC,UAAU,CAAC,EAAC,CAAC,UAAU,EAAC,MAAM,EAAC,gBAAgB,EAAC,kBAAkB,EAAC,gBAAgB,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"mi\",\"n\",\"in the morning\",\"in the afternoon\",\"in the evening\",\"at night\"],[\"midnight\",\"noon\",\"in the morning\",\"in the afternoon\",\"in the evening\",\"at night\"],u],[[\"midnight\",\"noon\",\"morning\",\"afternoon\",\"evening\",\"night\"],u,u],[\"00:00\",\"12:00\",[\"06:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"21:00\"],[\"21:00\",\"06:00\"]]];\n"]}