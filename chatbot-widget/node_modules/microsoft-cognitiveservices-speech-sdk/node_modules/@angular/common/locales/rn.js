/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["rn", [["Z.MU.", "Z.MW."], u, u], u, [["S", "M", "T", "W", "T", "F", "S"], ["cu.", "mbe.", "kab.", "gtu.", "kan.", "gnu.", "gnd."], ["Ku w’indwi", "Ku wa mbere", "Ku wa kabiri", "Ku wa gatatu", "Ku wa kane", "Ku wa gatanu", "Ku wa gatandatu"], ["cu.", "mbe.", "kab.", "gtu.", "kan.", "gnu.", "gnd."]], u, [["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], ["Mut.", "Gas.", "Wer.", "Mat.", "Gic.", "Kam.", "Nya.", "Kan.", "Nze.", "Ukw.", "Ugu.", "Uku."], ["Nzero", "Ruhuhuma", "Ntwarante", "Ndamukiza", "Rusama", "Ruheshi", "Mukakaro", "Nyandagaro", "Nyakanga", "Gitugutu", "Munyonyo", "Kigarama"]], u, [["Mb.Y.", "Ny.Y"], u, ["Mbere ya Yezu", "Nyuma ya Yezu"]], 1, [6, 0], ["d/M/y", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00¤", "#E0"], "BIF", "FBu", "Ifaranga ry’Uburundi", { "BIF": ["FBu"], "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=rn.js.map