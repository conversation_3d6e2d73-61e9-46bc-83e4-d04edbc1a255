{"version": 3, "sources": ["src/sdk/SpeechRecognitionEventArgs.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,MAAM,WAAW,CAAC;AAE1E;;;GAGG;AACH,qBAAa,0BAA2B,SAAQ,oBAAoB;IAChE,OAAO,CAAC,UAAU,CAA0B;IAE5C;;;;;;OAMG;gBACgB,MAAM,EAAE,uBAAuB,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM;IAMvF;;;;;;OAMG;IACH,IAAW,MAAM,IAAI,uBAAuB,CAE3C;CACJ;AAED;;;GAGG;AAEH,qBAAa,kCAAmC,SAAQ,0BAA0B;CACjF", "file": "SpeechRecognitionEventArgs.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport { RecognitionEventArgs, SpeechRecognitionResult } from \"./Exports\";\r\n\r\n/**\r\n * Defines contents of speech recognizing/recognized event.\r\n * @class SpeechRecognitionEventArgs\r\n */\r\nexport class SpeechRecognitionEventArgs extends RecognitionEventArgs {\r\n    private privResult: SpeechRecognitionResult;\r\n\r\n    /**\r\n     * Creates and initializes an instance of this class.\r\n     * @constructor\r\n     * @param {SpeechRecognitionResult} result - The speech recognition result.\r\n     * @param {number} offset - The offset.\r\n     * @param {string} sessionId - The session id.\r\n     */\r\n    public constructor(result: SpeechRecognitionResult, offset?: number, sessionId?: string) {\r\n        super(offset, sessionId);\r\n\r\n        this.privResult = result;\r\n    }\r\n\r\n    /**\r\n     * Specifies the recognition result.\r\n     * @member SpeechRecognitionEventArgs.prototype.result\r\n     * @function\r\n     * @public\r\n     * @returns {SpeechRecognitionResult} the recognition result.\r\n     */\r\n    public get result(): SpeechRecognitionResult {\r\n        return this.privResult;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines contents of conversation transcribed/transcribing event.\r\n * @class ConversationTranscriptionEventArgs\r\n */\r\n// tslint:disable-next-line:max-classes-per-file\r\nexport class ConversationTranscriptionEventArgs extends SpeechRecognitionEventArgs {\r\n}\r\n"]}