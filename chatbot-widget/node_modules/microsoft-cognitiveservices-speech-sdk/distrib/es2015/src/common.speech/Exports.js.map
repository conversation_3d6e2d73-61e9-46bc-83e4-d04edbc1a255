{"version": 3, "sources": ["src/common.speech/Exports.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAElC,4CAA4C;AAC5C,EAAE;AACF,cAAc,0CAA0C,CAAC;AACzD,cAAc,gCAAgC,CAAC;AAC/C,cAAc,mBAAmB,CAAC;AAClC,cAAc,sBAAsB,CAAC;AACrC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,2BAA2B,CAAC;AAC1C,cAAc,qBAAqB,CAAC;AACpC,cAAc,yBAAyB,CAAC;AACxC,cAAc,oBAAoB,CAAC;AACnC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,6BAA6B,CAAC;AAC5C,cAAc,2BAA2B,CAAC;AAC1C,cAAc,gCAAgC,CAAC;AAC/C,cAAc,gCAAgC,CAAC;AAC/C,cAAc,oCAAoC,CAAC;AACnD,cAAc,mBAAmB,CAAC;AAClC,cAAc,yBAAyB,CAAC;AACxC,cAAc,2CAA2C,CAAC;AAC1D,cAAc,yCAAyC,CAAC;AACxD,cAAc,qCAAqC,CAAC;AACpD,cAAc,gCAAgC,CAAC;AAC/C,cAAc,kCAAkC,CAAC;AACjD,cAAc,oCAAoC,CAAC;AACnD,cAAc,iCAAiC,CAAC;AAChD,cAAc,2BAA2B,CAAC;AAC1C,cAAc,kCAAkC,CAAC;AACjD,cAAc,wCAAwC,CAAC;AACvD,cAAc,sCAAsC,CAAC;AACrD,cAAc,iBAAiB,CAAC;AAChC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,kCAAkC,CAAC;AACjD,cAAc,kBAAkB,CAAC;AACjC,cAAc,iBAAiB,CAAC;AAChC,cAAc,yBAAyB,CAAC;AACxC,cAAc,4BAA4B,CAAC;AAC3C,cAAc,wBAAwB,CAAC;AACvC,cAAc,eAAe,CAAC;AAC9B,cAAc,yBAAyB,CAAC;AACxC,cAAc,0CAA0C,CAAC;AACzD,cAAc,iBAAiB,CAAC;AAChC,cAAc,wBAAwB,CAAC;AACvC,cAAc,qBAAqB,CAAC;AACpC,cAAc,oBAAoB,CAAC;AACnC,cAAc,4BAA4B,CAAC;AAC3C,cAAc,2BAA2B,CAAC;AAE1C,MAAM,CAAC,MAAM,wBAAwB,GAAW,cAAc,CAAC;AAC/D,MAAM,CAAC,MAAM,iCAAiC,GAAW,uBAAuB,CAAC;AACjF,MAAM,CAAC,MAAM,6BAA6B,GAAW,mBAAmB,CAAC;AACzE,MAAM,CAAC,MAAM,0BAA0B,GAAW,gBAAgB,CAAC;AACnE,MAAM,CAAC,MAAM,4CAA4C,GAAW,WAAW,CAAC", "file": "Exports.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\n// Make sure not to export internal modules.\r\n//\r\nexport * from \"./CognitiveSubscriptionKeyAuthentication\";\r\nexport * from \"./CognitiveTokenAuthentication\";\r\nexport * from \"./IAuthentication\";\r\nexport * from \"./IConnectionFactory\";\r\nexport * from \"./ISynthesisConnectionFactory\";\r\nexport * from \"./IntentConnectionFactory\";\r\nexport * from \"./RecognitionEvents\";\r\nexport * from \"./ServiceRecognizerBase\";\r\nexport * from \"./RecognizerConfig\";\r\nexport * from \"./SpeechServiceInterfaces\";\r\nexport * from \"./WebsocketMessageFormatter\";\r\nexport * from \"./SpeechConnectionFactory\";\r\nexport * from \"./TranscriberConnectionFactory\";\r\nexport * from \"./TranslationConnectionFactory\";\r\nexport * from \"./SpeechSynthesisConnectionFactory\";\r\nexport * from \"./EnumTranslation\";\r\nexport * from \"./ServiceMessages/Enums\";\r\nexport * from \"./ServiceMessages/TranslationSynthesisEnd\";\r\nexport * from \"./ServiceMessages/TranslationHypothesis\";\r\nexport * from \"./ServiceMessages/TranslationPhrase\";\r\nexport * from \"./TranslationServiceRecognizer\";\r\nexport * from \"./ServiceMessages/SpeechDetected\";\r\nexport * from \"./ServiceMessages/SpeechHypothesis\";\r\nexport * from \"./ServiceMessages/SpeechKeyword\";\r\nexport * from \"./SpeechServiceRecognizer\";\r\nexport * from \"./TranscriptionServiceRecognizer\";\r\nexport * from \"./ServiceMessages/DetailedSpeechPhrase\";\r\nexport * from \"./ServiceMessages/SimpleSpeechPhrase\";\r\nexport * from \"./AddedLmIntent\";\r\nexport * from \"./IntentServiceRecognizer\";\r\nexport * from \"./ServiceMessages/IntentResponse\";\r\nexport * from \"./RequestSession\";\r\nexport * from \"./SpeechContext\";\r\nexport * from \"./DynamicGrammarBuilder\";\r\nexport * from \"./DynamicGrammarInterfaces\";\r\nexport * from \"./DialogServiceAdapter\";\r\nexport * from \"./AgentConfig\";\r\nexport * from \"./Transcription/Exports\";\r\nexport * from \"./ServiceMessages/SynthesisAudioMetadata\";\r\nexport * from \"./SynthesisTurn\";\r\nexport * from \"./SynthesisAdapterBase\";\r\nexport * from \"./SynthesizerConfig\";\r\nexport * from \"./SynthesisContext\";\r\nexport * from \"./SpeakerRecognitionConfig\";\r\nexport * from \"./SpeakerIdMessageAdapter\";\r\n\r\nexport const OutputFormatPropertyName: string = \"OutputFormat\";\r\nexport const CancellationErrorCodePropertyName: string = \"CancellationErrorCode\";\r\nexport const ServicePropertiesPropertyName: string = \"ServiceProperties\";\r\nexport const ForceDictationPropertyName: string = \"ForceDictation\";\r\nexport const AutoDetectSourceLanguagesOpenRangeOptionName: string = \"OpenRange\";\r\n"]}