// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT license.
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
import { marshalPromiseToCallbacks } from "../../common/Exports";
import { Contracts } from "../../sdk/Contracts";
import { PropertyId, Recognizer, } from "../../sdk/Exports";
import { RecognitionMode, RecognizerConfig, TranscriberConnectionFactory, TranscriptionServiceRecognizer, } from "../Exports";
export class TranscriberRecognizer extends Recognizer {
    /**
     * TranscriberRecognizer constructor.
     * @constructor
     * @param {AudioConfig} audioConfig - An optional audio configuration associated with the recognizer
     */
    constructor(speechTranslationConfig, audioConfig) {
        const speechTranslationConfigImpl = speechTranslationConfig;
        Contracts.throwIfNull(speechTranslationConfigImpl, "speechTranslationConfig");
        Contracts.throwIfNullOrWhitespace(speechTranslationConfigImpl.speechRecognitionLanguage, PropertyId[PropertyId.SpeechServiceConnection_RecoLanguage]);
        super(audioConfig, speechTranslationConfigImpl.properties, new TranscriberConnectionFactory());
        this.privDisposedRecognizer = false;
    }
    getConversationInfo() {
        Contracts.throwIfNullOrUndefined(this.privConversation, "Conversation");
        return this.privConversation.conversationInfo;
    }
    get authorizationToken() {
        return this.properties.getProperty(PropertyId.SpeechServiceAuthorization_Token);
    }
    set authorizationToken(token) {
        Contracts.throwIfNullOrWhitespace(token, "token");
        this.properties.setProperty(PropertyId.SpeechServiceAuthorization_Token, token);
    }
    set conversation(c) {
        Contracts.throwIfNullOrUndefined(c, "Conversation");
        this.privConversation = c;
    }
    get speechRecognitionLanguage() {
        Contracts.throwIfDisposed(this.privDisposedRecognizer);
        return this.properties.getProperty(PropertyId.SpeechServiceConnection_RecoLanguage);
    }
    get properties() {
        return this.privProperties;
    }
    startContinuousRecognitionAsync(cb, err) {
        marshalPromiseToCallbacks(this.startContinuousRecognitionAsyncImpl(RecognitionMode.Conversation), cb, err);
    }
    stopContinuousRecognitionAsync(cb, err) {
        marshalPromiseToCallbacks(this.stopContinuousRecognitionAsyncImpl(), cb, err);
    }
    close() {
        return __awaiter(this, void 0, void 0, function* () {
            Contracts.throwIfDisposed(this.privDisposedRecognizer);
            yield this.dispose(true);
        });
    }
    // Push async join/leave conversation message via serviceRecognizer
    pushConversationEvent(conversationInfo, command) {
        return __awaiter(this, void 0, void 0, function* () {
            const reco = (this.privReco);
            Contracts.throwIfNullOrUndefined(reco, "serviceRecognizer");
            yield reco.sendSpeechEventAsync(conversationInfo, command);
        });
    }
    connectCallbacks(transcriber) {
        this.canceled = (s, e) => {
            if (!!transcriber.canceled) {
                transcriber.canceled(transcriber, e);
            }
        };
        this.recognizing = (s, e) => {
            if (!!transcriber.transcribing) {
                transcriber.transcribing(transcriber, e);
            }
        };
        this.recognized = (s, e) => {
            if (!!transcriber.transcribed) {
                transcriber.transcribed(transcriber, e);
            }
        };
        this.sessionStarted = (s, e) => {
            if (!!transcriber.sessionStarted) {
                transcriber.sessionStarted(transcriber, e);
            }
        };
        this.sessionStopped = (s, e) => {
            if (!!transcriber.sessionStopped) {
                transcriber.sessionStopped(transcriber, e);
            }
        };
    }
    disconnectCallbacks() {
        this.canceled = undefined;
        this.recognizing = undefined;
        this.recognized = undefined;
        this.sessionStarted = undefined;
        this.sessionStopped = undefined;
    }
    /**
     * Disposes any resources held by the object.
     * @member ConversationTranscriber.prototype.dispose
     * @function
     * @public
     * @param {boolean} disposing - true if disposing the object.
     */
    dispose(disposing) {
        const _super = Object.create(null, {
            dispose: { get: () => super.dispose }
        });
        return __awaiter(this, void 0, void 0, function* () {
            if (this.privDisposedRecognizer) {
                return;
            }
            if (disposing) {
                this.privDisposedRecognizer = true;
                yield this.implRecognizerStop();
            }
            yield _super.dispose.call(this, disposing);
        });
    }
    createRecognizerConfig(speechConfig) {
        return new RecognizerConfig(speechConfig, this.properties);
    }
    createServiceRecognizer(authentication, connectionFactory, audioConfig, recognizerConfig) {
        const configImpl = audioConfig;
        return new TranscriptionServiceRecognizer(authentication, connectionFactory, configImpl, recognizerConfig, this);
    }
}

//# sourceMappingURL=TranscriberRecognizer.js.map
