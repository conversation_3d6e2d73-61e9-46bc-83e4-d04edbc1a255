{"version": 3, "sources": ["src/common.speech/Transcription/Exports.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAC9E,OAAO,EAAE,6BAA6B,EAAE,MAAM,oCAAoC,CAAC;AACnF,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EACH,wCAAwC,EACxC,iBAAiB,EACjB,gBAAgB,EAChB,6BAA6B,EAC7B,oBAAoB,EACpB,yBAAyB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAEH,kCAAkC,EAClC,kCAAkC,EAGlC,oBAAoB,EAAC,MAAM,oCAAoC,CAAC", "file": "Exports.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nexport { ConversationManager } from \"./ConversationManager\";\r\nexport { ConversationConnectionConfig } from \"./ConversationConnectionConfig\";\r\nexport { ConversationRecognizerFactory } from \"./ConversationTranslatorRecognizer\";\r\nexport { TranscriberRecognizer } from \"./TranscriberRecognizer\";\r\nexport {\r\n    ConversationReceivedTranslationEventArgs,\r\n    LockRoomEventArgs,\r\n    MuteAllEventArgs,\r\n    ParticipantAttributeEventArgs,\r\n    ParticipantEventArgs,\r\n    ParticipantsListEventArgs } from \"./ConversationTranslatorEventArgs\";\r\nexport {\r\n    ConversationRecognizer,\r\n    ConversationTranslatorCommandTypes,\r\n    ConversationTranslatorMessageTypes,\r\n    IInternalConversation,\r\n    IInternalParticipant,\r\n    InternalParticipants} from \"./ConversationTranslatorInterfaces\";\r\n"]}