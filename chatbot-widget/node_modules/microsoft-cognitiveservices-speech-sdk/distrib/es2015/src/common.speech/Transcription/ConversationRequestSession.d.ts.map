{"version": 3, "sources": ["src/common.speech/Transcription/ConversationRequestSession.ts"], "names": [], "mappings": "AASA;;;GAGG;AACH,qBAAa,0BAA0B;IACnC,OAAO,CAAC,cAAc,CAAkB;IACxC,OAAO,CAAC,eAAe,CAA2C;IAClE,OAAO,CAAC,aAAa,CAAS;IAC9B,OAAO,CAAC,6BAA6B,CAAiB;IACtD,OAAO,CAAC,aAAa,CAAS;gBAElB,SAAS,EAAE,MAAM;IAM7B,IAAW,SAAS,IAAI,MAAM,CAE7B;IAED,IAAW,SAAS,IAAI,MAAM,CAE7B;IAED,IAAW,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC,CAE5C;IAEM,oBAAoB,qBAAsB,MAAM,gBAAgB,MAAM,KAAG,IAAI,CAEnF;IAEM,eAAe,YAAa,OAAO,UAAU,MAAM,KAAG,IAAI,CAIhE;IAEM,8BAA8B,eAAgB,MAAM,WAAW,MAAM,KAAG,IAAI,CAMlF;IAEM,wBAAwB,0BAA2B,OAAO,KAAG,IAAI,CAMvE;IAEY,OAAO,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAUnD,OAAO,CAAC,UAAU,CAEjB;CAEJ", "file": "ConversationRequestSession.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    createNoDashGuid,\r\n    Deferred,\r\n    IDetachable,\r\n} from \"../../common/Exports\";\r\n\r\n/**\r\n * Placeholder class for the Conversation Request Session. Based off RequestSession.\r\n * TODO: define what telemetry is required.\r\n */\r\nexport class ConversationRequestSession {\r\n    private privIsDisposed: boolean = false;\r\n    private privDetachables: IDetachable[] = new Array<IDetachable>();\r\n    private privRequestId: string;\r\n    private privRequestCompletionDeferral: Deferred<void>;\r\n    private privSessionId: string;\r\n\r\n    constructor(sessionId: string) {\r\n        this.privSessionId = sessionId;\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privRequestCompletionDeferral = new Deferred<void>();\r\n    }\r\n\r\n    public get sessionId(): string {\r\n        return this.privSessionId;\r\n    }\r\n\r\n    public get requestId(): string {\r\n        return this.privRequestId;\r\n    }\r\n\r\n    public get completionPromise(): Promise<void> {\r\n        return this.privRequestCompletionDeferral.promise;\r\n    }\r\n\r\n    public onPreConnectionStart = (authFetchEventId: string, connectionId: string): void => {\r\n        this.privSessionId = connectionId;\r\n    }\r\n\r\n    public onAuthCompleted = (isError: boolean, error?: string): void => {\r\n        if (isError) {\r\n            this.onComplete();\r\n        }\r\n    }\r\n\r\n    public onConnectionEstablishCompleted = (statusCode: number, reason?: string): void => {\r\n        if (statusCode === 200) {\r\n            return;\r\n        } else if (statusCode === 403) {\r\n            this.onComplete();\r\n        }\r\n    }\r\n\r\n    public onServiceTurnEndResponse = (continuousRecognition: boolean): void => {\r\n        if (!continuousRecognition) {\r\n            this.onComplete();\r\n        } else {\r\n            this.privRequestId = createNoDashGuid();\r\n        }\r\n    }\r\n\r\n    public async dispose(error?: string): Promise<void> {\r\n        if (!this.privIsDisposed) {\r\n            // we should have completed by now. If we did not its an unknown error.\r\n            this.privIsDisposed = true;\r\n            for (const detachable of this.privDetachables) {\r\n                await detachable.detach();\r\n            }\r\n        }\r\n    }\r\n\r\n    private onComplete = (): void => {\r\n        //\r\n    }\r\n\r\n}\r\n"]}