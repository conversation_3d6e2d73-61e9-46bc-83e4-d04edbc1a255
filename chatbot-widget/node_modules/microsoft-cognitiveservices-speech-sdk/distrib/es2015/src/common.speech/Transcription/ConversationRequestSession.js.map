{"version": 3, "sources": ["src/common.speech/Transcription/ConversationRequestSession.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;AAElC,OAAO,EACH,gBAAgB,EAChB,QAAQ,GAEX,MAAM,sBAAsB,CAAC;AAE9B;;;GAGG;AACH,MAAM,OAAO,0BAA0B;IAOnC,YAAY,SAAiB;QANrB,mBAAc,GAAY,KAAK,CAAC;QAChC,oBAAe,GAAkB,IAAI,KAAK,EAAe,CAAC;QAuB3D,yBAAoB,GAAG,CAAC,gBAAwB,EAAE,YAAoB,EAAQ,EAAE;YACnF,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QACtC,CAAC,CAAA;QAEM,oBAAe,GAAG,CAAC,OAAgB,EAAE,KAAc,EAAQ,EAAE;YAChE,IAAI,OAAO,EAAE;gBACT,IAAI,CAAC,UAAU,EAAE,CAAC;aACrB;QACL,CAAC,CAAA;QAEM,mCAA8B,GAAG,CAAC,UAAkB,EAAE,MAAe,EAAQ,EAAE;YAClF,IAAI,UAAU,KAAK,GAAG,EAAE;gBACpB,OAAO;aACV;iBAAM,IAAI,UAAU,KAAK,GAAG,EAAE;gBAC3B,IAAI,CAAC,UAAU,EAAE,CAAC;aACrB;QACL,CAAC,CAAA;QAEM,6BAAwB,GAAG,CAAC,qBAA8B,EAAQ,EAAE;YACvE,IAAI,CAAC,qBAAqB,EAAE;gBACxB,IAAI,CAAC,UAAU,EAAE,CAAC;aACrB;iBAAM;gBACH,IAAI,CAAC,aAAa,GAAG,gBAAgB,EAAE,CAAC;aAC3C;QACL,CAAC,CAAA;QAYO,eAAU,GAAG,GAAS,EAAE;YAC5B,EAAE;QACN,CAAC,CAAA;QAvDG,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,gBAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,6BAA6B,GAAG,IAAI,QAAQ,EAAQ,CAAC;IAC9D,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;IACtD,CAAC;IA4BY,OAAO,CAAC,KAAc;;YAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACtB,uEAAuE;gBACvE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE;oBAC3C,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;iBAC7B;aACJ;QACL,CAAC;KAAA;CAMJ", "file": "ConversationRequestSession.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    createNoDashGuid,\r\n    Deferred,\r\n    IDetachable,\r\n} from \"../../common/Exports\";\r\n\r\n/**\r\n * Placeholder class for the Conversation Request Session. Based off RequestSession.\r\n * TODO: define what telemetry is required.\r\n */\r\nexport class ConversationRequestSession {\r\n    private privIsDisposed: boolean = false;\r\n    private privDetachables: IDetachable[] = new Array<IDetachable>();\r\n    private privRequestId: string;\r\n    private privRequestCompletionDeferral: Deferred<void>;\r\n    private privSessionId: string;\r\n\r\n    constructor(sessionId: string) {\r\n        this.privSessionId = sessionId;\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privRequestCompletionDeferral = new Deferred<void>();\r\n    }\r\n\r\n    public get sessionId(): string {\r\n        return this.privSessionId;\r\n    }\r\n\r\n    public get requestId(): string {\r\n        return this.privRequestId;\r\n    }\r\n\r\n    public get completionPromise(): Promise<void> {\r\n        return this.privRequestCompletionDeferral.promise;\r\n    }\r\n\r\n    public onPreConnectionStart = (authFetchEventId: string, connectionId: string): void => {\r\n        this.privSessionId = connectionId;\r\n    }\r\n\r\n    public onAuthCompleted = (isError: boolean, error?: string): void => {\r\n        if (isError) {\r\n            this.onComplete();\r\n        }\r\n    }\r\n\r\n    public onConnectionEstablishCompleted = (statusCode: number, reason?: string): void => {\r\n        if (statusCode === 200) {\r\n            return;\r\n        } else if (statusCode === 403) {\r\n            this.onComplete();\r\n        }\r\n    }\r\n\r\n    public onServiceTurnEndResponse = (continuousRecognition: boolean): void => {\r\n        if (!continuousRecognition) {\r\n            this.onComplete();\r\n        } else {\r\n            this.privRequestId = createNoDashGuid();\r\n        }\r\n    }\r\n\r\n    public async dispose(error?: string): Promise<void> {\r\n        if (!this.privIsDisposed) {\r\n            // we should have completed by now. If we did not its an unknown error.\r\n            this.privIsDisposed = true;\r\n            for (const detachable of this.privDetachables) {\r\n                await detachable.detach();\r\n            }\r\n        }\r\n    }\r\n\r\n    private onComplete = (): void => {\r\n        //\r\n    }\r\n\r\n}\r\n"]}