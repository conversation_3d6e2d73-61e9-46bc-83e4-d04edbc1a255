{"version": 3, "sources": ["src/common.speech/SynthesizerConfig.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAU,mBAAmB,EAAC,MAAM,WAAW,CAAC;AAEvD,oBAAY,oBAAoB;IAC5B,QAAQ,IAAA;IACR,MAAM,IAAA;CACT;AAED,qBAAa,iBAAiB;IAC1B,OAAO,CAAC,wBAAwB,CAAuD;IACvF,OAAO,CAAC,uBAAuB,CAAsB;IACrD,OAAO,CAAC,cAAc,CAAqB;gBAGvC,mBAAmB,EAAE,mBAAmB,EACxC,UAAU,EAAE,kBAAkB;IAKlC,IAAW,UAAU,IAAI,kBAAkB,CAE1C;IAED,IAAW,oBAAoB,IAAI,oBAAoB,CAEtD;IAED,IAAW,oBAAoB,CAAC,KAAK,EAAE,oBAAoB,EAE1D;IAED,IAAW,mBAAmB,IAAI,mBAAmB,CAEpD;CACJ", "file": "SynthesizerConfig.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport { PropertyCollection } from \"../sdk/Exports\";\r\nimport {Context, SpeechServiceConfig} from \"./Exports\";\r\n\r\nexport enum SynthesisServiceType {\r\n    Standard,\r\n    Custom,\r\n}\r\n\r\nexport class SynthesizerConfig {\r\n    private privSynthesisServiceType: SynthesisServiceType = SynthesisServiceType.Standard;\r\n    private privSpeechServiceConfig: SpeechServiceConfig;\r\n    private privParameters: PropertyCollection;\r\n\r\n    constructor(\r\n        speechServiceConfig: SpeechServiceConfig,\r\n        parameters: PropertyCollection) {\r\n        this.privSpeechServiceConfig = speechServiceConfig ? speechServiceConfig : new SpeechServiceConfig(new Context(null));\r\n        this.privParameters = parameters;\r\n    }\r\n\r\n    public get parameters(): PropertyCollection {\r\n        return this.privParameters;\r\n    }\r\n\r\n    public get synthesisServiceType(): SynthesisServiceType {\r\n        return this.privSynthesisServiceType;\r\n    }\r\n\r\n    public set synthesisServiceType(value: SynthesisServiceType) {\r\n        this.privSynthesisServiceType = value;\r\n    }\r\n\r\n    public get SpeechServiceConfig(): SpeechServiceConfig {\r\n        return this.privSpeechServiceConfig;\r\n    }\r\n}\r\n"]}