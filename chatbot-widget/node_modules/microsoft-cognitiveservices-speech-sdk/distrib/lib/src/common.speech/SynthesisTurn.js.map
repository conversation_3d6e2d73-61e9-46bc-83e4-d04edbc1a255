{"version": 3, "sources": ["src/common.speech/SynthesisTurn.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,6CAI2B;AAE3B,oEAA2E;AAE3E,+DAA8D;AAC9D,qDAK2B;AAgB3B;IA+DI;QAAA,iBAMC;QA1BO,mBAAc,GAAY,KAAK,CAAC;QAEhC,uBAAkB,GAAY,KAAK,CAAC;QACpC,yBAAoB,GAAY,KAAK,CAAC;QACtC,sBAAiB,GAAW,CAAC,CAAC;QAI9B,eAAU,GAAY,KAAK,CAAC;QAK5B,mBAAc,GAAW,CAAC,CAAC;QAC3B,4BAAuB,GAAW,CAAC,CAAC;QA8DrC,yBAAoB,GAAG,UAAC,gBAAwB,EAAE,YAAoB;YACzE,KAAI,CAAC,oBAAoB,GAAG,gBAAgB,CAAC;YAC7C,KAAI,CAAC,OAAO,CAAC,IAAI,mDAAiC,CAAC,KAAI,CAAC,aAAa,EAAE,KAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACvG,CAAC,CAAA;QAEM,oBAAe,GAAG,UAAC,OAAgB,EAAE,KAAc;YACtD,IAAI,OAAO,EAAE;gBACT,KAAI,CAAC,UAAU,EAAE,CAAC;aACrB;QACL,CAAC,CAAA;QAEM,mCAA8B,GAAG,UAAC,UAAkB,EAAE,MAAe;YACxE,IAAI,UAAU,KAAK,GAAG,EAAE;gBACpB,KAAI,CAAC,OAAO,CAAC,IAAI,uCAAqB,CAAC,KAAI,CAAC,SAAS,EAAE,KAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;gBACnF,KAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;gBAC3B,OAAO;aACV;iBAAM,IAAI,UAAU,KAAK,GAAG,EAAE;gBAC3B,KAAI,CAAC,UAAU,EAAE,CAAC;aACrB;QACL,CAAC,CAAA;QAEM,6BAAwB,GAAG,UAAC,YAAoB;YACnD,IAAM,QAAQ,GAAuB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC9D,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;QAC5C,CAAC,CAAA;QAEM,6BAAwB,GAAG;YAC9B,KAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,KAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAChC,KAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC,CAAA;QAEM,+BAA0B,GAAG;YAChC,IAAI,CAAC,CAAC,KAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,KAAI,CAAC,UAAU,EAAE;gBAC9C,0DAA0D;gBAC1D,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,gDAAgD,CAAC,CAAC;gBAC/E,2EAA2E;gBAC3E,6BAA6B;gBAC7B,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,cAAQ,CAAC,CAAC,CAAC;aACzD;YACD,KAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,KAAI,CAAC,gBAAgB,GAAG,IAAI,kBAAQ,EAAQ,CAAC;QACjD,CAAC,CAAA;QAsBM,YAAO,GAAG,UAAC,KAAc;YAC5B,IAAI,CAAC,KAAI,CAAC,cAAc,EAAE;gBACtB,uEAAuE;gBACvE,KAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aAC9B;QACL,CAAC,CAAA;QAgBS,YAAO,GAAG,UAAC,KAA2B;YAC5C,gBAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAA;QAgBO,eAAU,GAAG;YACjB,IAAI,KAAI,CAAC,kBAAkB,EAAE;gBACzB,KAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBAChC,KAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACjC,KAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;gBACnC,KAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,KAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE;oBAC7C,KAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;oBACtC,KAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;iBAC7C;aACJ;QACL,CAAC,CAAA;QAzKG,IAAI,CAAC,aAAa,GAAG,0BAAgB,EAAE,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,IAAI,kBAAQ,EAAQ,CAAC;QAE7C,mCAAmC;QACnC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;IACpC,CAAC;IAnED,sBAAW,oCAAS;aAApB;YACI,OAAO,IAAI,CAAC,aAAa,CAAC;QAC9B,CAAC;;;OAAA;IAED,sBAAW,mCAAQ;aAAnB;YACI,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B,CAAC;aAED,UAAoB,KAAa;YAC7B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9B,CAAC;;;OAJA;IAMD,sBAAW,4CAAiB;aAA5B;YACI,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACtC,CAAC;aAED,UAA6B,MAA6B;YACtD,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC;QACxC,CAAC;;;OAJA;IAMD,sBAAW,gDAAqB;aAAhC;YACI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QACzC,CAAC;;;OAAA;IAED,sBAAW,2CAAgB;aAA3B;YACI,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACrC,CAAC;;;OAAA;IAED,sBAAW,yCAAc;aAAzB;YACI,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC,CAAC;;;OAAA;IAED,sBAAW,4CAAiB;aAA5B;YACI,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC;;;OAAA;IAGD,sBAAW,wCAAa;QADxB,gDAAgD;aAChD;YACI,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC,CAAC;;;OAAA;IA8BY,2CAAmB,GAAhC;;;;;wBACI,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;4BAC1B,sBAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAC;yBAClD;wBACD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;4BAC5B,sBAAO,IAAI,EAAC;yBACf;wBACD,qBAAM,IAAI,CAAC,sBAAsB,EAAE,EAAA;;wBAAnC,SAAmC,CAAC;wBACpC,sBAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAC;;;;KAClD;IAEY,qDAA6B,GAA1C;;;;;;wBACI,IAAI,CAAC,CAAC,IAAI,CAAC,2BAA2B,EAAE;4BACpC,sBAAO,IAAI,CAAC,2BAA2B,EAAC;yBAC3C;wBACD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;4BAC5B,sBAAO,IAAI,EAAC;yBACf;6BACG,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAhC,wBAAgC;wBACL,qBAAM,IAAI,CAAC,mBAAmB,EAAE,EAAA;;wBAArD,KAAK,GAAgB,SAAgC;wBAC3D,IAAI,CAAC,2BAA2B,GAAG,2CAAoB,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBACjG,sBAAO,IAAI,CAAC,2BAA2B,EAAC;4BAExC,sBAAO,IAAI,CAAC,mBAAmB,EAAE,EAAC;;;;KAEzC;IAEM,yCAAiB,GAAxB,UAAyB,SAAiB,EAAE,OAAe,EAAE,MAAe,EAAE,gBAAoC;QAC9G,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,6CAAyB,EAAE,CAAC;QAC7D,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC;QACrC,IAAI,gBAAgB,KAAK,SAAS,EAAE;YAChC,IAAI,CAAC,wBAAwB,GAAG,gBAAgB,CAAC;YACjD,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC;SACrE;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,yCAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7I,CAAC;IA8CM,4CAAoB,GAA3B,UAA4B,IAAiB;QACzC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU,CAAC;YAC1C,IAAI,IAAI,CAAC,wBAAwB,KAAK,SAAS,EAAE;gBAC7C,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAC7C;SACJ;IACL,CAAC;IAEM,2CAAmB,GAA1B,UAA2B,IAAY;QACnC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEM,gDAAwB,GAA/B,UAAgC,QAA4B;QACxD,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YAC5C,IAAI,CAAC,0BAA0B,IAAI,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;SACnE;IACL,CAAC;IASM,0CAAkB,GAAzB;QACI,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,kDAA0B,GAAjC;QACI,IAAM,SAAS,GAAW,IAAI,CAAC,0BAA0B,CAAC;QAC1D,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC;QACrC,OAAO,SAAS,CAAC;IACrB,CAAC;IAMO,wCAAgB,GAAxB,UAAyB,IAAY;QACjC,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACnF,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC;aACpE;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE;oBACjH,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;iBAC/B;aACJ;SACJ;IACL,CAAC;IAea,8CAAsB,GAApC;;;;;;6BACQ,IAAI,CAAC,oBAAoB,EAAzB,wBAAyB;wBACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;;wBAEzD,qBAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAA;;wBAA7D,SAA6D,CAAC;;;;wBAE9D,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;;;;;;KAGvD;IACL,oBAAC;AAAD,CArPA,AAqPC,IAAA;AArPY,sCAAa", "file": "SynthesisTurn.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    createNoDashGuid,\r\n    Deferred,\r\n    Events, IAudioDestination\r\n} from \"../common/Exports\";\r\nimport { AudioOutputFormatImpl } from \"../sdk/Audio/AudioOutputFormat\";\r\nimport { PullAudioOutputStreamImpl } from \"../sdk/Audio/AudioOutputStream\";\r\nimport { ISynthesisMetadata } from \"./ServiceMessages/SynthesisAudioMetadata\";\r\nimport { SynthesisAdapterBase } from \"./SynthesisAdapterBase\";\r\nimport {\r\n    ConnectingToSynthesisServiceEvent,\r\n    SpeechSynthesisEvent,\r\n    SynthesisStartedEvent,\r\n    SynthesisTriggeredEvent,\r\n} from \"./SynthesisEvents\";\r\n\r\nexport interface ISynthesisResponseContext {\r\n    serviceTag: string;\r\n}\r\n\r\nexport interface ISynthesisResponseAudio {\r\n    type: string;\r\n    streamId: string;\r\n}\r\n\r\nexport interface ISynthesisResponse {\r\n    context: ISynthesisResponseContext;\r\n    audio: ISynthesisResponseAudio;\r\n}\r\n\r\nexport class SynthesisTurn {\r\n\r\n    public get requestId(): string {\r\n        return this.privRequestId;\r\n    }\r\n\r\n    public get streamId(): string {\r\n        return this.privStreamId;\r\n    }\r\n\r\n    public set streamId(value: string) {\r\n        this.privStreamId = value;\r\n    }\r\n\r\n    public get audioOutputFormat(): AudioOutputFormatImpl {\r\n        return this.privAudioOutputFormat;\r\n    }\r\n\r\n    public set audioOutputFormat(format: AudioOutputFormatImpl) {\r\n        this.privAudioOutputFormat = format;\r\n    }\r\n\r\n    public get turnCompletionPromise(): Promise<void> {\r\n        return this.privTurnDeferral.promise;\r\n    }\r\n\r\n    public get isSynthesisEnded(): boolean {\r\n        return this.privIsSynthesisEnded;\r\n    }\r\n\r\n    public get isSynthesizing(): boolean {\r\n        return this.privIsSynthesizing;\r\n    }\r\n\r\n    public get currentTextOffset(): number {\r\n        return this.privTextOffset;\r\n    }\r\n\r\n    // The number of bytes received for current turn\r\n    public get bytesReceived(): number {\r\n        return this.privBytesReceived;\r\n    }\r\n\r\n    private privIsDisposed: boolean = false;\r\n    private privAuthFetchEventId: string;\r\n    private privIsSynthesizing: boolean = false;\r\n    private privIsSynthesisEnded: boolean = false;\r\n    private privBytesReceived: number = 0;\r\n    private privRequestId: string;\r\n    private privStreamId: string;\r\n    private privTurnDeferral: Deferred<void>;\r\n    private privInTurn: boolean = false;\r\n    private privAudioOutputFormat: AudioOutputFormatImpl;\r\n    private privAudioOutputStream: PullAudioOutputStreamImpl;\r\n    private privReceivedAudio: ArrayBuffer;\r\n    private privReceivedAudioWithHeader: ArrayBuffer;\r\n    private privTextOffset: number = 0;\r\n    private privNextSearchTextIndex: number = 0;\r\n    private privPartialVisemeAnimation: string;\r\n    private privRawText: string;\r\n    private privIsSSML: boolean;\r\n    private privTurnAudioDestination: IAudioDestination;\r\n\r\n    constructor() {\r\n        this.privRequestId = createNoDashGuid();\r\n        this.privTurnDeferral = new Deferred<void>();\r\n\r\n        // We're not in a turn, so resolve.\r\n        this.privTurnDeferral.resolve();\r\n    }\r\n\r\n    public async getAllReceivedAudio(): Promise<ArrayBuffer> {\r\n        if (!!this.privReceivedAudio) {\r\n            return Promise.resolve(this.privReceivedAudio);\r\n        }\r\n        if (!this.privIsSynthesisEnded) {\r\n            return null;\r\n        }\r\n        await this.readAllAudioFromStream();\r\n        return Promise.resolve(this.privReceivedAudio);\r\n    }\r\n\r\n    public async getAllReceivedAudioWithHeader(): Promise<ArrayBuffer> {\r\n        if (!!this.privReceivedAudioWithHeader) {\r\n            return this.privReceivedAudioWithHeader;\r\n        }\r\n        if (!this.privIsSynthesisEnded) {\r\n            return null;\r\n        }\r\n        if (this.audioOutputFormat.hasHeader) {\r\n            const audio: ArrayBuffer = await this.getAllReceivedAudio();\r\n            this.privReceivedAudioWithHeader = SynthesisAdapterBase.addHeader(audio, this.audioOutputFormat);\r\n            return this.privReceivedAudioWithHeader;\r\n        } else {\r\n            return this.getAllReceivedAudio();\r\n        }\r\n    }\r\n\r\n    public startNewSynthesis(requestId: string, rawText: string, isSSML: boolean, audioDestination?: IAudioDestination): void {\r\n        this.privIsSynthesisEnded = false;\r\n        this.privIsSynthesizing = true;\r\n        this.privRequestId = requestId;\r\n        this.privRawText = rawText;\r\n        this.privIsSSML = isSSML;\r\n        this.privAudioOutputStream = new PullAudioOutputStreamImpl();\r\n        this.privAudioOutputStream.format = this.privAudioOutputFormat;\r\n        this.privReceivedAudio = null;\r\n        this.privReceivedAudioWithHeader = null;\r\n        this.privBytesReceived = 0;\r\n        this.privTextOffset = 0;\r\n        this.privNextSearchTextIndex = 0;\r\n        this.privPartialVisemeAnimation = \"\";\r\n        if (audioDestination !== undefined) {\r\n            this.privTurnAudioDestination = audioDestination;\r\n            this.privTurnAudioDestination.format = this.privAudioOutputFormat;\r\n        }\r\n        this.onEvent(new SynthesisTriggeredEvent(this.requestId, undefined, audioDestination === undefined ? undefined : audioDestination.id()));\r\n    }\r\n\r\n    public onPreConnectionStart = (authFetchEventId: string, connectionId: string): void => {\r\n        this.privAuthFetchEventId = authFetchEventId;\r\n        this.onEvent(new ConnectingToSynthesisServiceEvent(this.privRequestId, this.privAuthFetchEventId));\r\n    }\r\n\r\n    public onAuthCompleted = (isError: boolean, error?: string): void => {\r\n        if (isError) {\r\n            this.onComplete();\r\n        }\r\n    }\r\n\r\n    public onConnectionEstablishCompleted = (statusCode: number, reason?: string): void => {\r\n        if (statusCode === 200) {\r\n            this.onEvent(new SynthesisStartedEvent(this.requestId, this.privAuthFetchEventId));\r\n            this.privBytesReceived = 0;\r\n            return;\r\n        } else if (statusCode === 403) {\r\n            this.onComplete();\r\n        }\r\n    }\r\n\r\n    public onServiceResponseMessage = (responseJson: string): void => {\r\n        const response: ISynthesisResponse = JSON.parse(responseJson);\r\n        this.streamId = response.audio.streamId;\r\n    }\r\n\r\n    public onServiceTurnEndResponse = (): void => {\r\n        this.privInTurn = false;\r\n        this.privTurnDeferral.resolve();\r\n        this.onComplete();\r\n    }\r\n\r\n    public onServiceTurnStartResponse = (): void => {\r\n        if (!!this.privTurnDeferral && !!this.privInTurn) {\r\n            // What? How are we starting a turn with another not done?\r\n            this.privTurnDeferral.reject(\"Another turn started before current completed.\");\r\n            // Avoid UnhandledPromiseRejection if privTurnDeferral is not being awaited\r\n            /* tslint:disable:no-empty */\r\n            this.privTurnDeferral.promise.then().catch(() => { });\r\n        }\r\n        this.privInTurn = true;\r\n        this.privTurnDeferral = new Deferred<void>();\r\n    }\r\n\r\n    public onAudioChunkReceived(data: ArrayBuffer): void {\r\n        if (this.isSynthesizing) {\r\n            this.privAudioOutputStream.write(data);\r\n            this.privBytesReceived += data.byteLength;\r\n            if (this.privTurnAudioDestination !== undefined) {\r\n                this.privTurnAudioDestination.write(data);\r\n            }\r\n        }\r\n    }\r\n\r\n    public onWordBoundaryEvent(text: string): void {\r\n        this.updateTextOffset(text);\r\n    }\r\n\r\n    public onVisemeMetadataReceived(metadata: ISynthesisMetadata): void {\r\n        if (metadata.Data.AnimationChunk !== undefined) {\r\n            this.privPartialVisemeAnimation += metadata.Data.AnimationChunk;\r\n        }\r\n    }\r\n\r\n    public dispose = (error?: string): void => {\r\n        if (!this.privIsDisposed) {\r\n            // we should have completed by now. If we did not its an unknown error.\r\n            this.privIsDisposed = true;\r\n        }\r\n    }\r\n\r\n    public onStopSynthesizing(): void {\r\n        this.onComplete();\r\n    }\r\n\r\n    /**\r\n     * Gets the viseme animation string (merged from animation chunk), and clears the internal\r\n     * partial animation.\r\n     */\r\n    public getAndClearVisemeAnimation(): string {\r\n        const animation: string = this.privPartialVisemeAnimation;\r\n        this.privPartialVisemeAnimation = \"\";\r\n        return animation;\r\n    }\r\n\r\n    protected onEvent = (event: SpeechSynthesisEvent): void => {\r\n        Events.instance.onEvent(event);\r\n    }\r\n\r\n    private updateTextOffset(text: string): void {\r\n        if (this.privTextOffset >= 0) {\r\n            this.privTextOffset = this.privRawText.indexOf(text, this.privNextSearchTextIndex);\r\n            if (this.privTextOffset >= 0) {\r\n                this.privNextSearchTextIndex = this.privTextOffset + text.length;\r\n            }\r\n            if (this.privIsSSML) {\r\n                if (this.privRawText.indexOf(\"<\", this.privTextOffset + 1) > this.privRawText.indexOf(\">\", this.privTextOffset + 1)) {\r\n                    this.updateTextOffset(text);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private onComplete = (): void => {\r\n        if (this.privIsSynthesizing) {\r\n            this.privIsSynthesizing = false;\r\n            this.privIsSynthesisEnded = true;\r\n            this.privAudioOutputStream.close();\r\n            this.privInTurn = false;\r\n            if (this.privTurnAudioDestination !== undefined) {\r\n                this.privTurnAudioDestination.close();\r\n                this.privTurnAudioDestination = undefined;\r\n            }\r\n        }\r\n    }\r\n\r\n    private async readAllAudioFromStream(): Promise<void> {\r\n        if (this.privIsSynthesisEnded) {\r\n            this.privReceivedAudio = new ArrayBuffer(this.bytesReceived);\r\n            try {\r\n                await this.privAudioOutputStream.read(this.privReceivedAudio);\r\n            } catch (e) {\r\n                this.privReceivedAudio = new ArrayBuffer(0);\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}