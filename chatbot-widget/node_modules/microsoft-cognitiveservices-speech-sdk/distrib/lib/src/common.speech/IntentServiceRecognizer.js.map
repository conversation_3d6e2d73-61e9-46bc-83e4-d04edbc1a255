{"version": 3, "sources": ["src/common.speech/IntentServiceRecognizer.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,6CAG2B;AAC3B,0CAWwB;AACxB,qCAQmB;AAMnB,gDAAgD;AAChD;IAA6C,2CAAqB;IAO9D,iCACI,cAA+B,EAC/B,iBAAqC,EACrC,WAAyB,EACzB,gBAAkC,EAClC,UAA4B;QALhC,YAMI,kBAAM,cAAc,EAAE,iBAAiB,EAAE,WAAW,EAAE,gBAAgB,EAAE,UAAU,CAAC,SAGtF;QAFG,KAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC;QACvC,KAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;;IACpC,CAAC;IAEM,4CAAU,GAAjB,UAAkB,YAA8C,EAAE,cAA6B;QAC3F,IAAI,CAAC,kBAAkB,GAAG,YAAY,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAEe,6DAA2B,GAA3C,UAA4C,iBAA0C;;;;;gBAI9E,SAAS,GAAY,KAAK,CAAC;gBAEzB,WAAW,GAAuB,IAAI,4BAAkB,EAAE,CAAC;gBACjE,IAAI,iBAAiB,CAAC,WAAW,KAAK,qBAAW,CAAC,IAAI,EAAE;oBACpD,WAAW,CAAC,WAAW,CAAC,oBAAU,CAAC,gCAAgC,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAC;iBACpG;gBAED,QAAQ,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;oBAC1C,KAAK,mBAAmB;wBACd,gBAAgB,GAAqB,0BAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAEjG,MAAM,GAAG,IAAI,iCAAuB,CAChC,SAAS,EACT,IAAI,CAAC,kBAAkB,CAAC,SAAS,EACjC,sBAAY,CAAC,iBAAiB,EAC9B,gBAAgB,CAAC,IAAI,EACrB,gBAAgB,CAAC,QAAQ,EACzB,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EACxE,gBAAgB,CAAC,QAAQ,EACzB,gBAAgB,CAAC,2BAA2B,EAC5C,SAAS,EACT,iBAAiB,CAAC,QAAQ,EAC1B,WAAW,CAAC,CAAC;wBAEjB,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBAEpD,EAAE,GAAG,IAAI,oCAA0B,CAAC,MAAM,EAAE,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;wBAEzJ,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE;4BACzC,IAAI;gCACA,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;gCACrE,6BAA6B;6BAChC;4BAAC,OAAO,KAAK,EAAE;gCACZ,+CAA+C;gCAC/C,kBAAkB;6BACrB;yBACJ;wBACD,SAAS,GAAG,IAAI,CAAC;wBACjB,MAAM;oBACV,KAAK,eAAe;wBACV,MAAM,GAAuB,4BAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAC3F,MAAM,GAAG,IAAI,iCAAuB,CAChC,SAAS,EACT,IAAI,CAAC,kBAAkB,CAAC,SAAS,EACjC,yBAAe,CAAC,8BAA8B,CAAC,MAAM,CAAC,iBAAiB,CAAC,EACxE,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAC9D,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,2BAA2B,EAClC,SAAS,EACT,iBAAiB,CAAC,QAAQ,EAC1B,WAAW,CAAC,CAAC;wBAEjB,EAAE,GAAG,IAAI,oCAA0B,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;wBAExF,SAAS,GAAe;4BAC1B,IAAI,CAAC,CAAC,KAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE;gCACxC,IAAI;oCACA,KAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,KAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;oCACpE,6BAA6B;iCAChC;gCAAC,OAAO,KAAK,EAAE;oCACZ,+CAA+C;oCAC/C,kBAAkB;iCACrB;6BACJ;4BAED,4BAA4B;4BAC5B,IAAI,CAAC,CAAC,KAAI,CAAC,mBAAmB,EAAE;gCAC5B,IAAI;oCACA,KAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;iCACpC;gCAAC,OAAO,CAAC,EAAE;oCACR,IAAI,CAAC,CAAC,KAAI,CAAC,iBAAiB,EAAE;wCAC1B,KAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;qCAC7B;iCACJ;gCACD,kCAAkC;gCAClC,0CAA0C;gCAC1C,oBAAoB;gCACpB,KAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;gCACrC,KAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;6BACtC;wBACL,CAAC,CAAC;wBAEF,6FAA6F;wBAC7F,yFAAyF;wBACzF,IAAI,KAAK,KAAK,IAAI,CAAC,kBAAkB,IAAI,sBAAY,CAAC,OAAO,KAAK,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE;4BAChF,uBAAuB;4BACvB,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;4BAC3E,SAAS,EAAE,CAAC;yBACf;6BAAM;4BACH,kFAAkF;4BAClF,kBAAkB;4BAClB,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;yBACnC;wBACD,SAAS,GAAG,IAAI,CAAC;wBACjB,MAAM;oBACV,KAAK,UAAU;wBACX,qBAAqB;wBACrB,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC;wBAChC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;wBAEvC,IAAI,SAAS,KAAK,EAAE,EAAE;4BAClB,IAAI,EAAE,KAAK,iBAAiB,CAAC,QAAQ,EAAE;gCACnC,gEAAgE;gCAChE,6BAA6B;gCAC7B,sBAAO;6BACV;4BAED,kCAAkC;4BAClC,EAAE,GAAG,IAAI,oCAA0B,CAAC,IAAI,iCAAuB,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;yBACrH;wBAEK,cAAc,GAAmB,wBAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAKvF,WAAW,GAAkB,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;wBAEjG,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,EAAE;4BACvC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC;yBACzC;wBAED,IAAI,IAAI,KAAK,cAAc,IAAI,WAAW,KAAK,SAAS,EAAE;4BAChD,QAAQ,GAAG,WAAW,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC;4BACpH,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;4BAE9B,IAAI,SAAS,KAAK,QAAQ,EAAE;gCACxB,MAAM,GAAG,sBAAY,CAAC,gBAAgB,CAAC;6BAC1C;4BAGK,UAAU,GAAG,CAAC,SAAS,KAAK,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gCACrD,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,4BAAkB,EAAE,CAAC;4BAEpD,UAAU,CAAC,WAAW,CAAC,oBAAU,CAAC,+CAA+C,EAAE,iBAAiB,CAAC,QAAQ,CAAC,CAAC;4BAE/G,EAAE,GAAG,IAAI,oCAA0B,CAC/B,IAAI,iCAAuB,CACvB,QAAQ,EACR,EAAE,CAAC,MAAM,CAAC,QAAQ,EAClB,MAAM,EACN,EAAE,CAAC,MAAM,CAAC,IAAI,EACd,EAAE,CAAC,MAAM,CAAC,QAAQ,EAClB,EAAE,CAAC,MAAM,CAAC,MAAM,EAChB,SAAS,EACT,SAAS,EACT,EAAE,CAAC,MAAM,CAAC,YAAY,EACtB,EAAE,CAAC,MAAM,CAAC,IAAI,EACd,UAAU,CAAC,EACf,EAAE,CAAC,MAAM,EACT,EAAE,CAAC,SAAS,CAAC,CAAC;yBACrB;wBACD,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAE3E,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE;4BACxC,IAAI;gCACA,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;gCACpE,6BAA6B;6BAChC;4BAAC,OAAO,KAAK,EAAE;gCACZ,+CAA+C;gCAC/C,kBAAkB;6BACrB;yBACJ;wBAED,4BAA4B;wBAC5B,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;4BAC5B,IAAI;gCACA,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;6BACvC;4BAAC,OAAO,CAAC,EAAE;gCACR,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;oCAC1B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;iCAC7B;6BACJ;4BACD,kCAAkC;4BAClC,0CAA0C;4BAC1C,oBAAoB;4BACpB,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;4BACrC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;yBACtC;wBACD,SAAS,GAAG,IAAI,CAAC;wBACjB,MAAM;oBACV;wBACI,MAAM;iBACb;gBACD,sBAAO,SAAS,EAAC;;;KACpB;IAED,uBAAuB;IACb,mDAAiB,GAA3B,UACI,SAAiB,EACjB,SAAiB,EACjB,kBAAsC,EACtC,SAAgC,EAChC,KAAa;QAEb,IAAM,UAAU,GAAuB,IAAI,4BAAkB,EAAE,CAAC;QAChE,UAAU,CAAC,WAAW,CAAC,2CAAiC,EAAE,+BAAqB,CAAC,SAAS,CAAC,CAAC,CAAC;QAE5F,IAAI,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE;YAEtC,IAAM,WAAW,GAAuC,IAAI,4CAAkC,CAC1F,kBAAkB,EAClB,KAAK,EACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAC,CAAC;YACf,IAAI;gBACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;gBAC3E,6BAA6B;aAChC;YAAC,WAAM,GAAG;SACd;QAED,IAAI,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAM,MAAM,GAA4B,IAAI,iCAAuB,CAC/D,SAAS,EAAE,YAAY;YACvB,SAAS,EACT,sBAAY,CAAC,QAAQ,EACrB,SAAS,EAAE,OAAO;YAClB,SAAS,EAAE,WAAW;YACtB,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,WAAW;YACtB,SAAS,EAAE,8BAA8B;YACzC,KAAK,EACL,SAAS,EAAE,OAAO;YAClB,UAAU,CAAC,CAAC;YAChB,IAAI;gBACA,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACjC,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;gBACrC,6BAA6B;aAChC;YAAC,WAAM,GAAG;SACd;IACL,CAAC;IACL,8BAAC;AAAD,CAvQA,AAuQC,CAvQ4C,+BAAqB,GAuQjE;AAvQY,0DAAuB", "file": "IntentServiceRecognizer.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    IAudioSource,\r\n    MessageType,\r\n} from \"../common/Exports\";\r\nimport {\r\n    CancellationErrorCode,\r\n    CancellationReason,\r\n    IntentRecognitionCanceledEventArgs,\r\n    IntentRecognitionEventArgs,\r\n    IntentRecognitionResult,\r\n    IntentRecognizer,\r\n    PropertyCollection,\r\n    PropertyId,\r\n    ResultReason,\r\n    SpeechRecognitionResult,\r\n} from \"../sdk/Exports\";\r\nimport {\r\n    AddedLmIntent,\r\n    CancellationErrorCodePropertyName,\r\n    EnumTranslation,\r\n    IntentResponse,\r\n    ServiceRecognizerBase,\r\n    SimpleSpeechPhrase,\r\n    SpeechHypothesis,\r\n} from \"./Exports\";\r\nimport { IAuthentication } from \"./IAuthentication\";\r\nimport { IConnectionFactory } from \"./IConnectionFactory\";\r\nimport { RecognizerConfig } from \"./RecognizerConfig\";\r\nimport { SpeechConnectionMessage } from \"./SpeechConnectionMessage.Internal\";\r\n\r\n// tslint:disable-next-line:max-classes-per-file\r\nexport class IntentServiceRecognizer extends ServiceRecognizerBase {\r\n    private privIntentRecognizer: IntentRecognizer;\r\n    private privAddedLmIntents: { [id: string]: AddedLmIntent; };\r\n    private privIntentDataSent: boolean;\r\n    private privUmbrellaIntent: AddedLmIntent;\r\n    private privPendingIntentArgs: IntentRecognitionEventArgs;\r\n\r\n    public constructor(\r\n        authentication: IAuthentication,\r\n        connectionFactory: IConnectionFactory,\r\n        audioSource: IAudioSource,\r\n        recognizerConfig: RecognizerConfig,\r\n        recognizer: IntentRecognizer) {\r\n        super(authentication, connectionFactory, audioSource, recognizerConfig, recognizer);\r\n        this.privIntentRecognizer = recognizer;\r\n        this.privIntentDataSent = false;\r\n    }\r\n\r\n    public setIntents(addedIntents: { [id: string]: AddedLmIntent; }, umbrellaIntent: AddedLmIntent): void {\r\n        this.privAddedLmIntents = addedIntents;\r\n        this.privUmbrellaIntent = umbrellaIntent;\r\n        this.privIntentDataSent = true;\r\n    }\r\n\r\n    protected async processTypeSpecificMessages(connectionMessage: SpeechConnectionMessage): Promise<boolean> {\r\n\r\n        let result: IntentRecognitionResult;\r\n        let ev: IntentRecognitionEventArgs;\r\n        let processed: boolean = false;\r\n\r\n        const resultProps: PropertyCollection = new PropertyCollection();\r\n        if (connectionMessage.messageType === MessageType.Text) {\r\n            resultProps.setProperty(PropertyId.SpeechServiceResponse_JsonResult, connectionMessage.textBody);\r\n        }\r\n\r\n        switch (connectionMessage.path.toLowerCase()) {\r\n            case \"speech.hypothesis\":\r\n                const speechHypothesis: SpeechHypothesis = SpeechHypothesis.fromJSON(connectionMessage.textBody);\r\n\r\n                result = new IntentRecognitionResult(\r\n                    undefined,\r\n                    this.privRequestSession.requestId,\r\n                    ResultReason.RecognizingIntent,\r\n                    speechHypothesis.Text,\r\n                    speechHypothesis.Duration,\r\n                    speechHypothesis.Offset + this.privRequestSession.currentTurnAudioOffset,\r\n                    speechHypothesis.Language,\r\n                    speechHypothesis.LanguageDetectionConfidence,\r\n                    undefined,\r\n                    connectionMessage.textBody,\r\n                    resultProps);\r\n\r\n                this.privRequestSession.onHypothesis(result.offset);\r\n\r\n                ev = new IntentRecognitionEventArgs(result, speechHypothesis.Offset + this.privRequestSession.currentTurnAudioOffset, this.privRequestSession.sessionId);\r\n\r\n                if (!!this.privIntentRecognizer.recognizing) {\r\n                    try {\r\n                        this.privIntentRecognizer.recognizing(this.privIntentRecognizer, ev);\r\n                        /* tslint:disable:no-empty */\r\n                    } catch (error) {\r\n                        // Not going to let errors in the event handler\r\n                        // trip things up.\r\n                    }\r\n                }\r\n                processed = true;\r\n                break;\r\n            case \"speech.phrase\":\r\n                const simple: SimpleSpeechPhrase = SimpleSpeechPhrase.fromJSON(connectionMessage.textBody);\r\n                result = new IntentRecognitionResult(\r\n                    undefined,\r\n                    this.privRequestSession.requestId,\r\n                    EnumTranslation.implTranslateRecognitionResult(simple.RecognitionStatus),\r\n                    simple.DisplayText,\r\n                    simple.Duration,\r\n                    simple.Offset + this.privRequestSession.currentTurnAudioOffset,\r\n                    simple.Language,\r\n                    simple.LanguageDetectionConfidence,\r\n                    undefined,\r\n                    connectionMessage.textBody,\r\n                    resultProps);\r\n\r\n                ev = new IntentRecognitionEventArgs(result, result.offset, this.privRequestSession.sessionId);\r\n\r\n                const sendEvent: () => void = () => {\r\n                    if (!!this.privIntentRecognizer.recognized) {\r\n                        try {\r\n                            this.privIntentRecognizer.recognized(this.privIntentRecognizer, ev);\r\n                            /* tslint:disable:no-empty */\r\n                        } catch (error) {\r\n                            // Not going to let errors in the event handler\r\n                            // trip things up.\r\n                        }\r\n                    }\r\n\r\n                    // report result to promise.\r\n                    if (!!this.privSuccessCallback) {\r\n                        try {\r\n                            this.privSuccessCallback(result);\r\n                        } catch (e) {\r\n                            if (!!this.privErrorCallback) {\r\n                                this.privErrorCallback(e);\r\n                            }\r\n                        }\r\n                        // Only invoke the call back once.\r\n                        // and if it's successful don't invoke the\r\n                        // error after that.\r\n                        this.privSuccessCallback = undefined;\r\n                        this.privErrorCallback = undefined;\r\n                    }\r\n                };\r\n\r\n                // If intent data was sent, the terminal result for this recognizer is an intent being found.\r\n                // If no intent data was sent, the terminal event is speech recognition being successful.\r\n                if (false === this.privIntentDataSent || ResultReason.NoMatch === ev.result.reason) {\r\n                    // Advance the buffers.\r\n                    this.privRequestSession.onPhraseRecognized(ev.offset + ev.result.duration);\r\n                    sendEvent();\r\n                } else {\r\n                    // Squirrel away the args, when the response event arrives it will build upon them\r\n                    // and then return\r\n                    this.privPendingIntentArgs = ev;\r\n                }\r\n                processed = true;\r\n                break;\r\n            case \"response\":\r\n                // Response from LUIS\r\n                ev = this.privPendingIntentArgs;\r\n                this.privPendingIntentArgs = undefined;\r\n\r\n                if (undefined === ev) {\r\n                    if (\"\" === connectionMessage.textBody) {\r\n                        // This condition happens if there is nothing but silence in the\r\n                        // audio sent to the service.\r\n                        return;\r\n                    }\r\n\r\n                    // Odd... Not sure this can happen\r\n                    ev = new IntentRecognitionEventArgs(new IntentRecognitionResult(), 0 /*TODO*/, this.privRequestSession.sessionId);\r\n                }\r\n\r\n                const intentResponse: IntentResponse = IntentResponse.fromJSON(connectionMessage.textBody);\r\n\r\n                // If LUIS didn't return anything, send the existing event, else\r\n                // modify it to show the match.\r\n                // See if the intent found is in the list of intents asked for.\r\n                let addedIntent: AddedLmIntent = this.privAddedLmIntents[intentResponse.topScoringIntent.intent];\r\n\r\n                if (this.privUmbrellaIntent !== undefined) {\r\n                    addedIntent = this.privUmbrellaIntent;\r\n                }\r\n\r\n                if (null !== intentResponse && addedIntent !== undefined) {\r\n                    const intentId = addedIntent.intentName === undefined ? intentResponse.topScoringIntent.intent : addedIntent.intentName;\r\n                    let reason = ev.result.reason;\r\n\r\n                    if (undefined !== intentId) {\r\n                        reason = ResultReason.RecognizedIntent;\r\n                    }\r\n\r\n                    // make sure, properties is set.\r\n                    const properties = (undefined !== ev.result.properties) ?\r\n                        ev.result.properties : new PropertyCollection();\r\n\r\n                    properties.setProperty(PropertyId.LanguageUnderstandingServiceResponse_JsonResult, connectionMessage.textBody);\r\n\r\n                    ev = new IntentRecognitionEventArgs(\r\n                        new IntentRecognitionResult(\r\n                            intentId,\r\n                            ev.result.resultId,\r\n                            reason,\r\n                            ev.result.text,\r\n                            ev.result.duration,\r\n                            ev.result.offset,\r\n                            undefined,\r\n                            undefined,\r\n                            ev.result.errorDetails,\r\n                            ev.result.json,\r\n                            properties),\r\n                        ev.offset,\r\n                        ev.sessionId);\r\n                }\r\n                this.privRequestSession.onPhraseRecognized(ev.offset + ev.result.duration);\r\n\r\n                if (!!this.privIntentRecognizer.recognized) {\r\n                    try {\r\n                        this.privIntentRecognizer.recognized(this.privIntentRecognizer, ev);\r\n                        /* tslint:disable:no-empty */\r\n                    } catch (error) {\r\n                        // Not going to let errors in the event handler\r\n                        // trip things up.\r\n                    }\r\n                }\r\n\r\n                // report result to promise.\r\n                if (!!this.privSuccessCallback) {\r\n                    try {\r\n                        this.privSuccessCallback(ev.result);\r\n                    } catch (e) {\r\n                        if (!!this.privErrorCallback) {\r\n                            this.privErrorCallback(e);\r\n                        }\r\n                    }\r\n                    // Only invoke the call back once.\r\n                    // and if it's successful don't invoke the\r\n                    // error after that.\r\n                    this.privSuccessCallback = undefined;\r\n                    this.privErrorCallback = undefined;\r\n                }\r\n                processed = true;\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n        return processed;\r\n    }\r\n\r\n    // Cancels recognition.\r\n    protected cancelRecognition(\r\n        sessionId: string,\r\n        requestId: string,\r\n        cancellationReason: CancellationReason,\r\n        errorCode: CancellationErrorCode,\r\n        error: string): void {\r\n\r\n        const properties: PropertyCollection = new PropertyCollection();\r\n        properties.setProperty(CancellationErrorCodePropertyName, CancellationErrorCode[errorCode]);\r\n\r\n        if (!!this.privIntentRecognizer.canceled) {\r\n\r\n            const cancelEvent: IntentRecognitionCanceledEventArgs = new IntentRecognitionCanceledEventArgs(\r\n                cancellationReason,\r\n                error,\r\n                errorCode,\r\n                undefined,\r\n                undefined,\r\n                sessionId);\r\n            try {\r\n                this.privIntentRecognizer.canceled(this.privIntentRecognizer, cancelEvent);\r\n                /* tslint:disable:no-empty */\r\n            } catch { }\r\n        }\r\n\r\n        if (!!this.privSuccessCallback) {\r\n            const result: IntentRecognitionResult = new IntentRecognitionResult(\r\n                undefined, // Intent Id\r\n                requestId,\r\n                ResultReason.Canceled,\r\n                undefined, // Text\r\n                undefined, // Duration\r\n                undefined, // Offset\r\n                undefined, // Language\r\n                undefined, // LanguageDetectionConfidence\r\n                error,\r\n                undefined, // Json\r\n                properties);\r\n            try {\r\n                this.privSuccessCallback(result);\r\n                this.privSuccessCallback = undefined;\r\n                /* tslint:disable:no-empty */\r\n            } catch { }\r\n        }\r\n    }\r\n}\r\n"]}