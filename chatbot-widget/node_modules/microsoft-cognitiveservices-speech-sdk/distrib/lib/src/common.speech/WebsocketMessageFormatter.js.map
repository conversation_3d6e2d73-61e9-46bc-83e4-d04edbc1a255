{"version": 3, "sources": ["src/common.speech/WebsocketMessageFormatter.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,6CAO2B;AAE3B,IAAM,IAAI,GAAW,MAAM,CAAC;AAE5B;IAAA;QAAA,iBA8IC;QA5IU,wBAAmB,GAAG,UAAC,OAA4B;YACtD,IAAM,QAAQ,GAAG,IAAI,kBAAQ,EAAqB,CAAC;YAEnD,IAAI;gBACA,IAAI,OAAO,CAAC,WAAW,KAAK,qBAAW,CAAC,IAAI,EAAE;oBAC1C,IAAM,WAAW,GAAW,OAAO,CAAC,WAAW,CAAC;oBAChD,IAAI,OAAO,GAA8B,EAAE,CAAC;oBAC5C,IAAI,IAAI,GAAW,IAAI,CAAC;oBAExB,IAAI,WAAW,EAAE;wBACb,IAAM,eAAe,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;wBACtD,IAAI,eAAe,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;4BAC/C,OAAO,GAAG,KAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;gCAC5B,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;6BAC7B;yBACJ;qBACJ;oBAED,QAAQ,CAAC,OAAO,CAAC,IAAI,2BAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3F;qBAAM,IAAI,OAAO,CAAC,WAAW,KAAK,qBAAW,CAAC,MAAM,EAAE;oBACnD,IAAM,aAAa,GAAgB,OAAO,CAAC,aAAa,CAAC;oBACzD,IAAI,OAAO,GAA8B,EAAE,CAAC;oBAC5C,IAAI,IAAI,GAAgB,IAAI,CAAC;oBAE7B,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,UAAU,GAAG,CAAC,EAAE;wBAChD,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;qBAC5E;oBAED,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC;oBAC7C,IAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAE1C,IAAI,aAAa,CAAC,UAAU,GAAG,YAAY,GAAG,CAAC,EAAE;wBAC7C,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;qBAC7E;oBAED,IAAI,aAAa,GAAG,EAAE,CAAC;oBACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE;wBACnC,aAAa,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;qBACnE;oBAED,OAAO,GAAG,KAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;oBAE3C,IAAI,aAAa,CAAC,UAAU,GAAG,YAAY,GAAG,CAAC,EAAE;wBAC7C,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;qBAChD;oBAED,QAAQ,CAAC,OAAO,CAAC,IAAI,2BAAiB,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3F;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,QAAQ,CAAC,MAAM,CAAC,0CAAwC,CAAG,CAAC,CAAC;aAChE;YAED,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC5B,CAAC,CAAA;QAEM,0BAAqB,GAAG,UAAC,OAA0B;YACtD,IAAM,QAAQ,GAAG,IAAI,kBAAQ,EAAuB,CAAC;YAErD,IAAI;gBACA,IAAI,OAAO,CAAC,WAAW,KAAK,qBAAW,CAAC,IAAI,EAAE;oBAC1C,IAAM,OAAO,GAAG,KAAG,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI,IAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAE,CAAC;oBAEjG,QAAQ,CAAC,OAAO,CAAC,IAAI,6BAAmB,CAAC,qBAAW,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;iBAEpF;qBAAM,IAAI,OAAO,CAAC,WAAW,KAAK,qBAAW,CAAC,MAAM,EAAE;oBACnD,IAAM,aAAa,GAAG,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAChD,IAAM,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC;oBAEnC,IAAM,YAAY,GAAG,KAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;oBAC7D,IAAM,eAAe,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;oBACpD,IAAM,YAAY,GAAG,eAAe,CAAC,UAAU,CAAC;oBAEhD,IAAM,gBAAgB,GAAG,IAAI,SAAS,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9F,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;oBACnD,gBAAgB,CAAC,CAAC,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;oBAC1C,gBAAgB,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;oBAEzC,IAAI,OAAO,EAAE;wBACT,IAAM,aAAa,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;wBAC7C,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC;qBACzD;oBAED,IAAM,OAAO,GAAgB,gBAAgB,CAAC,MAAM,CAAC;oBAErD,QAAQ,CAAC,OAAO,CAAC,IAAI,6BAAmB,CAAC,qBAAW,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;iBACtF;aACJ;YAAC,OAAO,CAAC,EAAE;gBACR,QAAQ,CAAC,MAAM,CAAC,mCAAiC,CAAG,CAAC,CAAC;aACzD;YAED,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC5B,CAAC,CAAA;QAEO,gBAAW,GAAG,UAAC,OAA0B;YAC7C,IAAI,aAAa,GAAW,EAAE,CAAC;YAE/B,IAAI,OAAO,CAAC,OAAO,EAAE;gBACjB,KAAK,IAAM,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;oBAClC,IAAI,MAAM,EAAE;wBACR,aAAa,IAAO,MAAM,UAAK,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAM,CAAC;qBACnE;iBACJ;aACJ;YAED,OAAO,aAAa,CAAC;QACzB,CAAC,CAAA;QAEO,iBAAY,GAAG,UAAC,aAAqB;YACzC,IAAM,OAAO,GAA8B,EAAE,CAAC;YAE9C,IAAI,aAAa,EAAE;gBACf,IAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBACvD,IAAI,OAAO,EAAE;oBACT,KAAqB,UAAa,EAAb,+BAAa,EAAb,2BAAa,EAAb,IAAa,EAAE;wBAA/B,IAAM,MAAM,sBAAA;wBACb,IAAI,MAAM,EAAE;4BACR,IAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;4BAC3C,IAAM,UAAU,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;4BACvG,IAAM,WAAW,GACb,cAAc,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;gCACxD,MAAM,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gCAC1C,EAAE,CAAC;4BAEX,OAAO,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC;yBACrC;qBACJ;iBACJ;aACJ;YAED,OAAO,OAAO,CAAC;QACnB,CAAC,CAAA;QAEO,wBAAmB,GAAG,UAAC,GAAW;YACtC,IAAM,MAAM,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3C,IAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACvC;YACD,OAAO,MAAM,CAAC;QAClB,CAAC,CAAA;IACL,CAAC;IAAD,gCAAC;AAAD,CA9IA,AA8IC,IAAA;AA9IY,8DAAyB", "file": "WebsocketMessageFormatter.js", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport {\r\n    ConnectionMessage,\r\n    Deferred,\r\n    IStringDictionary,\r\n    IWebsocketMessageFormatter,\r\n    MessageType,\r\n    RawWebsocketMessage,\r\n} from \"../common/Exports\";\r\n\r\nconst CRLF: string = \"\\r\\n\";\r\n\r\nexport class WebsocketMessageFormatter implements IWebsocketMessageFormatter {\r\n\r\n    public toConnectionMessage = (message: RawWebsocketMessage): Promise<ConnectionMessage> => {\r\n        const deferral = new Deferred<ConnectionMessage>();\r\n\r\n        try {\r\n            if (message.messageType === MessageType.Text) {\r\n                const textMessage: string = message.textContent;\r\n                let headers: IStringDictionary<string> = {};\r\n                let body: string = null;\r\n\r\n                if (textMessage) {\r\n                    const headerBodySplit = textMessage.split(\"\\r\\n\\r\\n\");\r\n                    if (headerBodySplit && headerBodySplit.length > 0) {\r\n                        headers = this.parseHeaders(headerBodySplit[0]);\r\n                        if (headerBodySplit.length > 1) {\r\n                            body = headerBodySplit[1];\r\n                        }\r\n                    }\r\n                }\r\n\r\n                deferral.resolve(new ConnectionMessage(message.messageType, body, headers, message.id));\r\n            } else if (message.messageType === MessageType.Binary) {\r\n                const binaryMessage: ArrayBuffer = message.binaryContent;\r\n                let headers: IStringDictionary<string> = {};\r\n                let body: ArrayBuffer = null;\r\n\r\n                if (!binaryMessage || binaryMessage.byteLength < 2) {\r\n                    throw new Error(\"Invalid binary message format. Header length missing.\");\r\n                }\r\n\r\n                const dataView = new DataView(binaryMessage);\r\n                const headerLength = dataView.getInt16(0);\r\n\r\n                if (binaryMessage.byteLength < headerLength + 2) {\r\n                    throw new Error(\"Invalid binary message format. Header content missing.\");\r\n                }\r\n\r\n                let headersString = \"\";\r\n                for (let i = 0; i < headerLength; i++) {\r\n                    headersString += String.fromCharCode((dataView).getInt8(i + 2));\r\n                }\r\n\r\n                headers = this.parseHeaders(headersString);\r\n\r\n                if (binaryMessage.byteLength > headerLength + 2) {\r\n                    body = binaryMessage.slice(2 + headerLength);\r\n                }\r\n\r\n                deferral.resolve(new ConnectionMessage(message.messageType, body, headers, message.id));\r\n            }\r\n        } catch (e) {\r\n            deferral.reject(`Error formatting the message. Error: ${e}`);\r\n        }\r\n\r\n        return deferral.promise;\r\n    }\r\n\r\n    public fromConnectionMessage = (message: ConnectionMessage): Promise<RawWebsocketMessage> => {\r\n        const deferral = new Deferred<RawWebsocketMessage>();\r\n\r\n        try {\r\n            if (message.messageType === MessageType.Text) {\r\n                const payload = `${this.makeHeaders(message)}${CRLF}${message.textBody ? message.textBody : \"\"}`;\r\n\r\n                deferral.resolve(new RawWebsocketMessage(MessageType.Text, payload, message.id));\r\n\r\n            } else if (message.messageType === MessageType.Binary) {\r\n                const headersString = this.makeHeaders(message);\r\n                const content = message.binaryBody;\r\n\r\n                const headerBuffer = this.stringToArrayBuffer(headersString);\r\n                const headerInt8Array = new Int8Array(headerBuffer);\r\n                const headerLength = headerInt8Array.byteLength;\r\n\r\n                const payloadInt8Array = new Int8Array(2 + headerLength + (content ? content.byteLength : 0));\r\n                payloadInt8Array[0] = ((headerLength >> 8) & 0xff);\r\n                payloadInt8Array[1] = headerLength & 0xff;\r\n                payloadInt8Array.set(headerInt8Array, 2);\r\n\r\n                if (content) {\r\n                    const bodyInt8Array = new Int8Array(content);\r\n                    payloadInt8Array.set(bodyInt8Array, 2 + headerLength);\r\n                }\r\n\r\n                const payload: ArrayBuffer = payloadInt8Array.buffer;\r\n\r\n                deferral.resolve(new RawWebsocketMessage(MessageType.Binary, payload, message.id));\r\n            }\r\n        } catch (e) {\r\n            deferral.reject(`Error formatting the message. ${e}`);\r\n        }\r\n\r\n        return deferral.promise;\r\n    }\r\n\r\n    private makeHeaders = (message: ConnectionMessage): string => {\r\n        let headersString: string = \"\";\r\n\r\n        if (message.headers) {\r\n            for (const header in message.headers) {\r\n                if (header) {\r\n                    headersString += `${header}: ${message.headers[header]}${CRLF}`;\r\n                }\r\n            }\r\n        }\r\n\r\n        return headersString;\r\n    }\r\n\r\n    private parseHeaders = (headersString: string): IStringDictionary<string> => {\r\n        const headers: IStringDictionary<string> = {};\r\n\r\n        if (headersString) {\r\n            const headerMatches = headersString.match(/[^\\r\\n]+/g);\r\n            if (headers) {\r\n                for (const header of headerMatches) {\r\n                    if (header) {\r\n                        const separatorIndex = header.indexOf(\":\");\r\n                        const headerName = separatorIndex > 0 ? header.substr(0, separatorIndex).trim().toLowerCase() : header;\r\n                        const headerValue =\r\n                            separatorIndex > 0 && header.length > (separatorIndex + 1) ?\r\n                                header.substr(separatorIndex + 1).trim() :\r\n                                \"\";\r\n\r\n                        headers[headerName] = headerValue;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return headers;\r\n    }\r\n\r\n    private stringToArrayBuffer = (str: string): ArrayBuffer => {\r\n        const buffer = new ArrayBuffer(str.length);\r\n        const view = new DataView(buffer);\r\n        for (let i = 0; i < str.length; i++) {\r\n            view.setUint8(i, str.charCodeAt(i));\r\n        }\r\n        return buffer;\r\n    }\r\n}\r\n"]}