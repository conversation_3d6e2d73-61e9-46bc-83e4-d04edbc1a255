{"version": 3, "sources": ["src/sdk/IntentRecognitionResult.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,kBAAkB,EAAE,YAAY,EAAE,uBAAuB,EAAE,MAAM,WAAW,CAAC;AAEtF;;;GAGG;AACH,qBAAa,uBAAwB,SAAQ,uBAAuB;IAChE,OAAO,CAAC,YAAY,CAAS;IAE7B;;;;;;;;;;;;;;OAcG;gBACS,QAAQ,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,EAAE,MAAM,EAC1E,QAAQ,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE,2BAA2B,CAAC,EAAE,MAAM,EAC3F,YAAY,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,kBAAkB;IAMjF;;;;;;OAMG;IACH,IAAW,QAAQ,IAAI,MAAM,CAE5B;CACJ", "file": "IntentRecognitionResult.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n\r\nimport { PropertyCollection, ResultReason, SpeechRecognitionResult } from \"./Exports\";\r\n\r\n/**\r\n * Intent recognition result.\r\n * @class\r\n */\r\nexport class IntentRecognitionResult extends SpeechRecognitionResult {\r\n    private privIntentId: string;\r\n\r\n    /**\r\n     * Creates and initializes an instance of this class.\r\n     * @constructor\r\n     * @param intentId - The intent id.\r\n     * @param resultId - The result id.\r\n     * @param reason - The reason.\r\n     * @param text - The recognized text.\r\n     * @param duration - The duration.\r\n     * @param offset - The offset into the stream.\r\n     * @param language - Primary Language detected, if provided.\r\n     * @param languageDetectionConfidence - Primary Language confidence (\"Unknown,\" \"Low,\" \"Medium,\" \"High\"...), if provided.\r\n     * @param errorDetails - Error details, if provided.\r\n     * @param json - Additional Json, if provided.\r\n     * @param properties - Additional properties, if provided.\r\n     */\r\n    constructor(intentId?: string, resultId?: string, reason?: ResultReason, text?: string,\r\n                duration?: number, offset?: number, language?: string, languageDetectionConfidence?: string,\r\n                errorDetails?: string, json?: string, properties?: PropertyCollection) {\r\n        super(resultId, reason, text, duration, offset, language, languageDetectionConfidence, undefined, errorDetails, json, properties);\r\n\r\n        this.privIntentId = intentId;\r\n    }\r\n\r\n    /**\r\n     * A String that represents the intent identifier being recognized.\r\n     * @member IntentRecognitionResult.prototype.intentId\r\n     * @function\r\n     * @public\r\n     * @returns {string} A String that represents the intent identifier being recognized.\r\n     */\r\n    public get intentId(): string {\r\n        return this.privIntentId;\r\n    }\r\n}\r\n"]}