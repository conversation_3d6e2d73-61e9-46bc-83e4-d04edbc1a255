{"version": 3, "sources": ["src/sdk/Transcription/IParticipant.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAEhD;;;GAGG;AACH,MAAM,WAAW,KAAK;IAClB,yBAAyB;IACzB,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;CAC3B;AAED,qBAAa,IAAK,YAAW,KAAK;IAC9B,OAAO,CAAC,UAAU,CAAS;gBAEf,MAAM,EAAE,MAAM;IAI1B,IAAW,MAAM,IAAI,MAAM,CAE1B;CACJ;AAED,MAAM,WAAW,cAAc;IAC3B,OAAO,EAAE,MAAM,CAAC;IAChB,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,wBAAwB;IACrC,iDAAiD;IACjD,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC;IACpB,mDAAmD;IACnD,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC;IACnC,wCAAwC;IACxC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC;CAC1B;AAED;;;GAGG;AACH,MAAM,WAAW,YAAa,SAAQ,wBAAwB;IAC1D,wFAAwF;IACxF,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB;;;OAGG;IACH,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IAC7B,wDAAwD;IACxD,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC;IACzB,qDAAqD;IACrD,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;IAC1B,yEAAyE;IACzE,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC;IAC7B,8CAA8C;IAC9C,QAAQ,CAAC,UAAU,EAAE,kBAAkB,CAAC;CAC3C;AAGD,qBAAa,WAAY,YAAW,YAAY;IAC5C,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,eAAe,CAAS;IAChC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,UAAU,CAAU;IAC5B,OAAO,CAAC,WAAW,CAAU;IAC7B,OAAO,CAAC,cAAc,CAAU;IAChC,OAAO,CAAC,qBAAqB,CAAS;IACtC,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,cAAc,CAAqB;gBAE/B,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM;WAYhJ,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,YAAY;IAI7E,IAAW,MAAM,IAAI,MAAM,CAE1B;IAED,IAAW,WAAW,IAAI,MAAM,CAE/B;IAED,IAAW,EAAE,IAAI,MAAM,CAEtB;IAED,IAAW,iBAAiB,IAAI,MAAM,CAErC;IAED,IAAW,MAAM,IAAI,OAAO,CAE3B;IAED,IAAW,OAAO,IAAI,OAAO,CAE5B;IAED,IAAW,UAAU,IAAI,OAAO,CAE/B;IAED,IAAW,KAAK,IAAI,MAAM,CAEzB;IAED,IAAW,UAAU,IAAI,kBAAkB,CAE1C;CACJ", "file": "IParticipant.d.ts", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved.\r\n// Licensed under the MIT license.\r\n// Multi-device Conversation is a Preview feature.\r\n\r\nimport { PropertyCollection } from \"../Exports\";\r\n\r\n/**\r\n * Represents a user in a conversation.\r\n * Added in version 1.4.0\r\n */\r\nexport interface IUser {\r\n    /** Gets the user's ID */\r\n    readonly userId: string;\r\n}\r\n\r\nexport class User implements IUser {\r\n    private privUserId: string;\r\n\r\n    constructor(userId: string) {\r\n        this.privUserId = userId;\r\n    }\r\n\r\n    public get userId(): string {\r\n        return this.privUserId;\r\n    }\r\n}\r\n\r\nexport interface VoiceSignature {\r\n    Version: number;\r\n    Tag: string;\r\n    Data: string;\r\n}\r\n\r\nexport interface TranscriptionParticipant {\r\n    /** The unique identifier for the participant. */\r\n    readonly id: string;\r\n    /** The participant's preferred spoken language. */\r\n    readonly preferredLanguage: string;\r\n    /** The participant's voice signature */\r\n    readonly voice: string;\r\n}\r\n\r\n/**\r\n * Represents a participant in a conversation.\r\n * Added in version 1.4.0\r\n */\r\nexport interface IParticipant extends TranscriptionParticipant {\r\n    /** Gets the colour of the user's avatar as an HTML hex string (e.g. FF0000 for red). */\r\n    readonly avatar: string;\r\n    /**\r\n     * The participant's display name. Please note that there may be more than one participant\r\n     * with the same name. You can use <see cref=\"Id\"/> property to tell them apart.\r\n     */\r\n    readonly displayName: string;\r\n    /** Gets whether or not this participant is the host. */\r\n    readonly isHost: boolean;\r\n    /** Gets whether or not this participant is muted. */\r\n    readonly isMuted: boolean;\r\n    /** Gets whether or not the participant is using Text To Speech (TTS). */\r\n    readonly isUsingTts: boolean;\r\n    /** Contains properties of the participant. */\r\n    readonly properties: PropertyCollection;\r\n}\r\n\r\n// tslint:disable-next-line: max-classes-per-file\r\nexport class Participant implements IParticipant {\r\n    private privAvatar: string;\r\n    private privDisplayName: string;\r\n    private privId: string;\r\n    private privIsHost: boolean;\r\n    private privIsMuted: boolean;\r\n    private privIsUsingTts: boolean;\r\n    private privPreferredLanguage: string;\r\n    private privVoice: string;\r\n    private privProperties: PropertyCollection;\r\n\r\n    constructor(id: string, avatar: string, displayName: string, isHost: boolean, isMuted: boolean, isUsingTts: boolean, preferredLanguage: string, voice?: string) {\r\n        this.privId = id;\r\n        this.privAvatar = avatar;\r\n        this.privDisplayName = displayName;\r\n        this.privIsHost = isHost;\r\n        this.privIsMuted = isMuted;\r\n        this.privIsUsingTts = isUsingTts;\r\n        this.privPreferredLanguage = preferredLanguage;\r\n        this.privVoice = voice;\r\n        this.privProperties = new PropertyCollection();\r\n    }\r\n\r\n    public static From(id: string, language: string, voice: string): IParticipant {\r\n        return new Participant(id, \"\", id, false, false, false, language, voice);\r\n    }\r\n\r\n    public get avatar(): string {\r\n        return this.privAvatar;\r\n    }\r\n\r\n    public get displayName(): string {\r\n        return this.privDisplayName;\r\n    }\r\n\r\n    public get id(): string {\r\n        return this.privId;\r\n    }\r\n\r\n    public get preferredLanguage(): string {\r\n        return this.privPreferredLanguage;\r\n    }\r\n\r\n    public get isHost(): boolean {\r\n        return this.privIsHost;\r\n    }\r\n\r\n    public get isMuted(): boolean {\r\n        return this.privIsMuted;\r\n    }\r\n\r\n    public get isUsingTts(): boolean {\r\n        return this.privIsUsingTts;\r\n    }\r\n\r\n    public get voice(): string {\r\n        return this.privVoice;\r\n    }\r\n\r\n    public get properties(): PropertyCollection {\r\n        return this.privProperties;\r\n    }\r\n}\r\n"]}