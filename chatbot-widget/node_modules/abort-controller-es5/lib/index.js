"use strict";

var _Reflect$construct = require("@babel/runtime-corejs3/core-js-stable/reflect/construct");
var _interopRequireDefault = require("@babel/runtime-corejs3/helpers/interopRequireDefault")["default"];
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/possibleConstructorReturn"));
var _isNativeReflectConstruct2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/isNativeReflectConstruct"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/inherits"));
var _createForOfIteratorHelper2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/createForOfIteratorHelper"));
var _typeof2 = _interopRequireDefault(require("@babel/runtime-corejs3/helpers/typeof"));
var _create = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/create"));
var _defineProperty = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/define-property"));
var _getOwnPropertyDescriptor = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor"));
var _getOwnPropertyNames = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/get-own-property-names"));
var _getPrototypeOf3 = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/get-prototype-of"));
var _weakMap = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/weak-map"));
var _now = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/date/now"));
var _keys = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/keys"));
var _setPrototypeOf = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/set-prototype-of"));
var _map = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/map"));
var _isArray = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/array/is-array"));
var _defineProperties = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/object/define-properties"));
var _symbol = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/symbol"));
var _toStringTag = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/symbol/to-string-tag"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2["default"])(o), (0, _possibleConstructorReturn2["default"])(t, (0, _isNativeReflectConstruct2["default"])() ? _Reflect$construct(o, e || [], (0, _getPrototypeOf2["default"])(t).constructor) : o.apply(t, e)); }
var __create = _create["default"];
var __defProp = _defineProperty["default"];
var __getOwnPropDesc = _getOwnPropertyDescriptor["default"];
var __getOwnPropNames = _getOwnPropertyNames["default"];
var __getProtoOf = _getPrototypeOf3["default"];
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = function __commonJS(cb, mod) {
  return function __require() {
    return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
      exports: {}
    }).exports, mod), mod.exports;
  };
};
var __copyProps = function __copyProps(to, from, except, desc) {
  if (from && (0, _typeof2["default"])(from) === "object" || typeof from === "function") {
    var _iterator = (0, _createForOfIteratorHelper2["default"])(__getOwnPropNames(from)),
      _step;
    try {
      var _loop = function _loop() {
        var key = _step.value;
        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
          get: function get() {
            return from[key];
          },
          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
      };
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        _loop();
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
  }
  return to;
};
var __toESM = function __toESM(mod, isNodeMode, target) {
  return target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
    value: mod,
    enumerable: true
  }) : target, mod);
};

// ../abort-controller/node_modules/event-target-shim/dist/event-target-shim.js
var require_event_target_shim = __commonJS({
  "../abort-controller/node_modules/event-target-shim/dist/event-target-shim.js": function _abortController_node_modules_eventTargetShim_dist_eventTargetShimJs(exports2, module2) {
    "use strict";

    (0, _defineProperty["default"])(exports2, "__esModule", {
      value: true
    });
    var privateData = /* @__PURE__ */new _weakMap["default"]();
    var wrappers = /* @__PURE__ */new _weakMap["default"]();
    function pd(event) {
      var retv = privateData.get(event);
      console.assert(retv != null, "'this' is expected an Event object, but got", event);
      return retv;
    }
    function setCancelFlag(data) {
      if (data.passiveListener != null) {
        if (typeof console !== "undefined" && typeof console.error === "function") {
          console.error("Unable to preventDefault inside passive event listener invocation.", data.passiveListener);
        }
        return;
      }
      if (!data.event.cancelable) {
        return;
      }
      data.canceled = true;
      if (typeof data.event.preventDefault === "function") {
        data.event.preventDefault();
      }
    }
    function Event(eventTarget, event) {
      privateData.set(this, {
        eventTarget: eventTarget,
        event: event,
        eventPhase: 2,
        currentTarget: eventTarget,
        canceled: false,
        stopped: false,
        immediateStopped: false,
        passiveListener: null,
        timeStamp: event.timeStamp || (0, _now["default"])()
      });
      (0, _defineProperty["default"])(this, "isTrusted", {
        value: false,
        enumerable: true
      });
      var keys = (0, _keys["default"])(event);
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        if (!(key in this)) {
          (0, _defineProperty["default"])(this, key, defineRedirectDescriptor(key));
        }
      }
    }
    Event.prototype = {
      get type() {
        return pd(this).event.type;
      },
      get target() {
        return pd(this).eventTarget;
      },
      get currentTarget() {
        return pd(this).currentTarget;
      },
      composedPath: function composedPath() {
        var currentTarget = pd(this).currentTarget;
        if (currentTarget == null) {
          return [];
        }
        return [currentTarget];
      },
      get NONE() {
        return 0;
      },
      get CAPTURING_PHASE() {
        return 1;
      },
      get AT_TARGET() {
        return 2;
      },
      get BUBBLING_PHASE() {
        return 3;
      },
      get eventPhase() {
        return pd(this).eventPhase;
      },
      stopPropagation: function stopPropagation() {
        var data = pd(this);
        data.stopped = true;
        if (typeof data.event.stopPropagation === "function") {
          data.event.stopPropagation();
        }
      },
      stopImmediatePropagation: function stopImmediatePropagation() {
        var data = pd(this);
        data.stopped = true;
        data.immediateStopped = true;
        if (typeof data.event.stopImmediatePropagation === "function") {
          data.event.stopImmediatePropagation();
        }
      },
      get bubbles() {
        return Boolean(pd(this).event.bubbles);
      },
      get cancelable() {
        return Boolean(pd(this).event.cancelable);
      },
      preventDefault: function preventDefault() {
        setCancelFlag(pd(this));
      },
      get defaultPrevented() {
        return pd(this).canceled;
      },
      get composed() {
        return Boolean(pd(this).event.composed);
      },
      get timeStamp() {
        return pd(this).timeStamp;
      },
      get srcElement() {
        return pd(this).eventTarget;
      },
      get cancelBubble() {
        return pd(this).stopped;
      },
      set cancelBubble(value) {
        if (!value) {
          return;
        }
        var data = pd(this);
        data.stopped = true;
        if (typeof data.event.cancelBubble === "boolean") {
          data.event.cancelBubble = true;
        }
      },
      get returnValue() {
        return !pd(this).canceled;
      },
      set returnValue(value) {
        if (!value) {
          setCancelFlag(pd(this));
        }
      },
      initEvent: function initEvent() {}
    };
    (0, _defineProperty["default"])(Event.prototype, "constructor", {
      value: Event,
      configurable: true,
      writable: true
    });
    if (typeof window !== "undefined" && typeof window.Event !== "undefined") {
      (0, _setPrototypeOf["default"])(Event.prototype, window.Event.prototype);
      wrappers.set(window.Event.prototype, Event);
    }
    function defineRedirectDescriptor(key) {
      return {
        get: function get() {
          return pd(this).event[key];
        },
        set: function set(value) {
          pd(this).event[key] = value;
        },
        configurable: true,
        enumerable: true
      };
    }
    function defineCallDescriptor(key) {
      return {
        value: function value() {
          var event = pd(this).event;
          return event[key].apply(event, arguments);
        },
        configurable: true,
        enumerable: true
      };
    }
    function defineWrapper(BaseEvent, proto) {
      var keys = (0, _keys["default"])(proto);
      if (keys.length === 0) {
        return BaseEvent;
      }
      function CustomEvent(eventTarget, event) {
        BaseEvent.call(this, eventTarget, event);
      }
      CustomEvent.prototype = (0, _create["default"])(BaseEvent.prototype, {
        constructor: {
          value: CustomEvent,
          configurable: true,
          writable: true
        }
      });
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        if (!(key in BaseEvent.prototype)) {
          var descriptor = (0, _getOwnPropertyDescriptor["default"])(proto, key);
          var isFunc = typeof descriptor.value === "function";
          (0, _defineProperty["default"])(CustomEvent.prototype, key, isFunc ? defineCallDescriptor(key) : defineRedirectDescriptor(key));
        }
      }
      return CustomEvent;
    }
    function getWrapper(proto) {
      if (proto == null || proto === Object.prototype) {
        return Event;
      }
      var wrapper = wrappers.get(proto);
      if (wrapper == null) {
        wrapper = defineWrapper(getWrapper((0, _getPrototypeOf3["default"])(proto)), proto);
        wrappers.set(proto, wrapper);
      }
      return wrapper;
    }
    function wrapEvent(eventTarget, event) {
      var Wrapper = getWrapper((0, _getPrototypeOf3["default"])(event));
      return new Wrapper(eventTarget, event);
    }
    function isStopped(event) {
      return pd(event).immediateStopped;
    }
    function setEventPhase(event, eventPhase) {
      pd(event).eventPhase = eventPhase;
    }
    function setCurrentTarget(event, currentTarget) {
      pd(event).currentTarget = currentTarget;
    }
    function setPassiveListener(event, passiveListener) {
      pd(event).passiveListener = passiveListener;
    }
    var listenersMap = /* @__PURE__ */new _weakMap["default"]();
    var CAPTURE = 1;
    var BUBBLE = 2;
    var ATTRIBUTE = 3;
    function isObject(x) {
      return x !== null && (0, _typeof2["default"])(x) === "object";
    }
    function getListeners(eventTarget) {
      var listeners = listenersMap.get(eventTarget);
      if (listeners == null) {
        throw new TypeError("'this' is expected an EventTarget object, but got another value.");
      }
      return listeners;
    }
    function defineEventAttributeDescriptor(eventName) {
      return {
        get: function get() {
          var listeners = getListeners(this);
          var node = listeners.get(eventName);
          while (node != null) {
            if (node.listenerType === ATTRIBUTE) {
              return node.listener;
            }
            node = node.next;
          }
          return null;
        },
        set: function set(listener) {
          if (typeof listener !== "function" && !isObject(listener)) {
            listener = null;
          }
          var listeners = getListeners(this);
          var prev = null;
          var node = listeners.get(eventName);
          while (node != null) {
            if (node.listenerType === ATTRIBUTE) {
              if (prev !== null) {
                prev.next = node.next;
              } else if (node.next !== null) {
                listeners.set(eventName, node.next);
              } else {
                listeners["delete"](eventName);
              }
            } else {
              prev = node;
            }
            node = node.next;
          }
          if (listener !== null) {
            var newNode = {
              listener: listener,
              listenerType: ATTRIBUTE,
              passive: false,
              once: false,
              next: null
            };
            if (prev === null) {
              listeners.set(eventName, newNode);
            } else {
              prev.next = newNode;
            }
          }
        },
        configurable: true,
        enumerable: true
      };
    }
    function defineEventAttribute(eventTargetPrototype, eventName) {
      (0, _defineProperty["default"])(eventTargetPrototype, "on".concat(eventName), defineEventAttributeDescriptor(eventName));
    }
    function defineCustomEventTarget(eventNames) {
      function CustomEventTarget() {
        EventTarget.call(this);
      }
      CustomEventTarget.prototype = (0, _create["default"])(EventTarget.prototype, {
        constructor: {
          value: CustomEventTarget,
          configurable: true,
          writable: true
        }
      });
      for (var i = 0; i < eventNames.length; ++i) {
        defineEventAttribute(CustomEventTarget.prototype, eventNames[i]);
      }
      return CustomEventTarget;
    }
    function EventTarget() {
      if (this instanceof EventTarget) {
        listenersMap.set(this, /* @__PURE__ */new _map["default"]());
        return;
      }
      if (arguments.length === 1 && (0, _isArray["default"])(arguments[0])) {
        return defineCustomEventTarget(arguments[0]);
      }
      if (arguments.length > 0) {
        var types = new Array(arguments.length);
        for (var i = 0; i < arguments.length; ++i) {
          types[i] = arguments[i];
        }
        return defineCustomEventTarget(types);
      }
      throw new TypeError("Cannot call a class as a function");
    }
    EventTarget.prototype = {
      addEventListener: function addEventListener(eventName, listener, options) {
        if (listener == null) {
          return;
        }
        if (typeof listener !== "function" && !isObject(listener)) {
          throw new TypeError("'listener' should be a function or an object.");
        }
        var listeners = getListeners(this);
        var optionsIsObj = isObject(options);
        var capture = optionsIsObj ? Boolean(options.capture) : Boolean(options);
        var listenerType = capture ? CAPTURE : BUBBLE;
        var newNode = {
          listener: listener,
          listenerType: listenerType,
          passive: optionsIsObj && Boolean(options.passive),
          once: optionsIsObj && Boolean(options.once),
          next: null
        };
        var node = listeners.get(eventName);
        if (node === void 0) {
          listeners.set(eventName, newNode);
          return;
        }
        var prev = null;
        while (node != null) {
          if (node.listener === listener && node.listenerType === listenerType) {
            return;
          }
          prev = node;
          node = node.next;
        }
        prev.next = newNode;
      },
      removeEventListener: function removeEventListener(eventName, listener, options) {
        if (listener == null) {
          return;
        }
        var listeners = getListeners(this);
        var capture = isObject(options) ? Boolean(options.capture) : Boolean(options);
        var listenerType = capture ? CAPTURE : BUBBLE;
        var prev = null;
        var node = listeners.get(eventName);
        while (node != null) {
          if (node.listener === listener && node.listenerType === listenerType) {
            if (prev !== null) {
              prev.next = node.next;
            } else if (node.next !== null) {
              listeners.set(eventName, node.next);
            } else {
              listeners["delete"](eventName);
            }
            return;
          }
          prev = node;
          node = node.next;
        }
      },
      dispatchEvent: function dispatchEvent(event) {
        if (event == null || typeof event.type !== "string") {
          throw new TypeError('"event.type" should be a string.');
        }
        var listeners = getListeners(this);
        var eventName = event.type;
        var node = listeners.get(eventName);
        if (node == null) {
          return true;
        }
        var wrappedEvent = wrapEvent(this, event);
        var prev = null;
        while (node != null) {
          if (node.once) {
            if (prev !== null) {
              prev.next = node.next;
            } else if (node.next !== null) {
              listeners.set(eventName, node.next);
            } else {
              listeners["delete"](eventName);
            }
          } else {
            prev = node;
          }
          setPassiveListener(wrappedEvent, node.passive ? node.listener : null);
          if (typeof node.listener === "function") {
            try {
              node.listener.call(this, wrappedEvent);
            } catch (err) {
              if (typeof console !== "undefined" && typeof console.error === "function") {
                console.error(err);
              }
            }
          } else if (node.listenerType !== ATTRIBUTE && typeof node.listener.handleEvent === "function") {
            node.listener.handleEvent(wrappedEvent);
          }
          if (isStopped(wrappedEvent)) {
            break;
          }
          node = node.next;
        }
        setPassiveListener(wrappedEvent, null);
        setEventPhase(wrappedEvent, 0);
        setCurrentTarget(wrappedEvent, null);
        return !wrappedEvent.defaultPrevented;
      }
    };
    (0, _defineProperty["default"])(EventTarget.prototype, "constructor", {
      value: EventTarget,
      configurable: true,
      writable: true
    });
    if (typeof window !== "undefined" && typeof window.EventTarget !== "undefined") {
      (0, _setPrototypeOf["default"])(EventTarget.prototype, window.EventTarget.prototype);
    }
    exports2.defineEventAttribute = defineEventAttribute;
    exports2.EventTarget = EventTarget;
    exports2["default"] = EventTarget;
    module2.exports = EventTarget;
    module2.exports.EventTarget = module2.exports["default"] = EventTarget;
    module2.exports.defineEventAttribute = defineEventAttribute;
  }
});

// ../abort-controller/dist/abort-controller.js
var require_abort_controller = __commonJS({
  "../abort-controller/dist/abort-controller.js": function _abortController_dist_abortControllerJs(exports2, module2) {
    "use strict";

    (0, _defineProperty["default"])(exports2, "__esModule", {
      value: true
    });
    var eventTargetShim = require_event_target_shim();
    var AbortSignal2 = /*#__PURE__*/function (_eventTargetShim$Even) {
      function AbortSignal2() {
        var _this;
        (0, _classCallCheck2["default"])(this, AbortSignal2);
        _this = _callSuper(this, AbortSignal2);
        throw new TypeError("AbortSignal cannot be constructed directly");
        return _this;
      }
      (0, _inherits2["default"])(AbortSignal2, _eventTargetShim$Even);
      return (0, _createClass2["default"])(AbortSignal2, [{
        key: "aborted",
        get: function get() {
          var aborted = abortedFlags.get(this);
          if (typeof aborted !== "boolean") {
            throw new TypeError("Expected 'this' to be an 'AbortSignal' object, but got ".concat(this === null ? "null" : (0, _typeof2["default"])(this)));
          }
          return aborted;
        }
      }]);
    }(eventTargetShim.EventTarget);
    eventTargetShim.defineEventAttribute(AbortSignal2.prototype, "abort");
    function createAbortSignal() {
      var signal = (0, _create["default"])(AbortSignal2.prototype);
      eventTargetShim.EventTarget.call(signal);
      abortedFlags.set(signal, false);
      return signal;
    }
    function abortSignal(signal) {
      if (abortedFlags.get(signal) !== false) {
        return;
      }
      abortedFlags.set(signal, true);
      signal.dispatchEvent({
        type: "abort"
      });
    }
    var abortedFlags = /* @__PURE__ */new _weakMap["default"]();
    (0, _defineProperties["default"])(AbortSignal2.prototype, {
      aborted: {
        enumerable: true
      }
    });
    if (typeof _symbol["default"] === "function" && (0, _typeof2["default"])(_toStringTag["default"]) === "symbol") {
      (0, _defineProperty["default"])(AbortSignal2.prototype, _toStringTag["default"], {
        configurable: true,
        value: "AbortSignal"
      });
    }
    var AbortController2 = /*#__PURE__*/function () {
      function AbortController2() {
        (0, _classCallCheck2["default"])(this, AbortController2);
        signals.set(this, createAbortSignal());
      }
      return (0, _createClass2["default"])(AbortController2, [{
        key: "signal",
        get: function get() {
          return getSignal(this);
        }
      }, {
        key: "abort",
        value: function abort() {
          abortSignal(getSignal(this));
        }
      }]);
    }();
    var signals = /* @__PURE__ */new _weakMap["default"]();
    function getSignal(controller) {
      var signal = signals.get(controller);
      if (signal == null) {
        throw new TypeError("Expected 'this' to be an 'AbortController' object, but got ".concat(controller === null ? "null" : (0, _typeof2["default"])(controller)));
      }
      return signal;
    }
    (0, _defineProperties["default"])(AbortController2.prototype, {
      signal: {
        enumerable: true
      },
      abort: {
        enumerable: true
      }
    });
    if (typeof _symbol["default"] === "function" && (0, _typeof2["default"])(_toStringTag["default"]) === "symbol") {
      (0, _defineProperty["default"])(AbortController2.prototype, _toStringTag["default"], {
        configurable: true,
        value: "AbortController"
      });
    }
    exports2.AbortController = AbortController2;
    exports2.AbortSignal = AbortSignal2;
    exports2["default"] = AbortController2;
    module2.exports = AbortController2;
    module2.exports.AbortController = module2.exports["default"] = AbortController2;
    module2.exports.AbortSignal = AbortSignal2;
  }
});

// src/index.js
var import_abort_controller = __toESM(require_abort_controller());
var NativeAbortController = (typeof window === "undefined" ? {} : window).AbortController;
var AbortController = NativeAbortController || import_abort_controller["default"];
var NativeAbortSignal = (typeof window === "undefined" ? {} : window).AbortSignal;
var AbortSignal = NativeAbortSignal || import_abort_controller.AbortSignal;
module.exports = {
  AbortController: AbortController,
  AbortSignal: AbortSignal,
  "default": AbortController
};
