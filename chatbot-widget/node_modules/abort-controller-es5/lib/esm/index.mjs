import _Reflect$construct from "@babel/runtime-corejs3/core-js-stable/reflect/construct";
import _classCallCheck from "@babel/runtime-corejs3/helpers/classCallCheck";
import _createClass from "@babel/runtime-corejs3/helpers/createClass";
import _possibleConstructorReturn from "@babel/runtime-corejs3/helpers/possibleConstructorReturn";
import _isNativeReflectConstruct from "@babel/runtime-corejs3/helpers/isNativeReflectConstruct";
import _getPrototypeOf from "@babel/runtime-corejs3/helpers/getPrototypeOf";
import _inherits from "@babel/runtime-corejs3/helpers/inherits";
import _createForOfIteratorHelper from "@babel/runtime-corejs3/helpers/createForOfIteratorHelper";
import _typeof from "@babel/runtime-corejs3/helpers/typeof";
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? _Reflect$construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
import _Object$create from "@babel/runtime-corejs3/core-js-stable/object/create";
import _Object$defineProperty from "@babel/runtime-corejs3/core-js-stable/object/define-property";
import _Object$getOwnPropertyDescriptor from "@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor";
import _Object$getOwnPropertyNames from "@babel/runtime-corejs3/core-js-stable/object/get-own-property-names";
import _Object$getPrototypeOf from "@babel/runtime-corejs3/core-js-stable/object/get-prototype-of";
import _WeakMap from "@babel/runtime-corejs3/core-js-stable/weak-map";
import _Date$now from "@babel/runtime-corejs3/core-js-stable/date/now";
import _Object$keys from "@babel/runtime-corejs3/core-js-stable/object/keys";
import _Object$setPrototypeOf from "@babel/runtime-corejs3/core-js-stable/object/set-prototype-of";
import _Map from "@babel/runtime-corejs3/core-js-stable/map";
import _Array$isArray from "@babel/runtime-corejs3/core-js-stable/array/is-array";
import _Object$defineProperties from "@babel/runtime-corejs3/core-js-stable/object/define-properties";
import _Symbol from "@babel/runtime-corejs3/core-js-stable/symbol";
import _Symbol$toStringTag from "@babel/runtime-corejs3/core-js-stable/symbol/to-string-tag";
var __create = _Object$create;
var __defProp = _Object$defineProperty;
var __getOwnPropDesc = _Object$getOwnPropertyDescriptor;
var __getOwnPropNames = _Object$getOwnPropertyNames;
var __getProtoOf = _Object$getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __commonJS = function __commonJS(cb, mod) {
  return function __require() {
    return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
      exports: {}
    }).exports, mod), mod.exports;
  };
};
var __copyProps = function __copyProps(to, from, except, desc) {
  if (from && _typeof(from) === "object" || typeof from === "function") {
    var _iterator = _createForOfIteratorHelper(__getOwnPropNames(from)),
      _step;
    try {
      var _loop = function _loop() {
        var key = _step.value;
        if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
          get: function get() {
            return from[key];
          },
          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
      };
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        _loop();
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
  }
  return to;
};
var __toESM = function __toESM(mod, isNodeMode, target) {
  return target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
    value: mod,
    enumerable: true
  }) : target, mod);
};

// ../abort-controller/node_modules/event-target-shim/dist/event-target-shim.js
var require_event_target_shim = __commonJS({
  "../abort-controller/node_modules/event-target-shim/dist/event-target-shim.js": function _abortController_node_modules_eventTargetShim_dist_eventTargetShimJs(exports, module) {
    "use strict";

    _Object$defineProperty(exports, "__esModule", {
      value: true
    });
    var privateData = /* @__PURE__ */new _WeakMap();
    var wrappers = /* @__PURE__ */new _WeakMap();
    function pd(event) {
      var retv = privateData.get(event);
      console.assert(retv != null, "'this' is expected an Event object, but got", event);
      return retv;
    }
    function setCancelFlag(data) {
      if (data.passiveListener != null) {
        if (typeof console !== "undefined" && typeof console.error === "function") {
          console.error("Unable to preventDefault inside passive event listener invocation.", data.passiveListener);
        }
        return;
      }
      if (!data.event.cancelable) {
        return;
      }
      data.canceled = true;
      if (typeof data.event.preventDefault === "function") {
        data.event.preventDefault();
      }
    }
    function Event(eventTarget, event) {
      privateData.set(this, {
        eventTarget: eventTarget,
        event: event,
        eventPhase: 2,
        currentTarget: eventTarget,
        canceled: false,
        stopped: false,
        immediateStopped: false,
        passiveListener: null,
        timeStamp: event.timeStamp || _Date$now()
      });
      _Object$defineProperty(this, "isTrusted", {
        value: false,
        enumerable: true
      });
      var keys = _Object$keys(event);
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        if (!(key in this)) {
          _Object$defineProperty(this, key, defineRedirectDescriptor(key));
        }
      }
    }
    Event.prototype = {
      get type() {
        return pd(this).event.type;
      },
      get target() {
        return pd(this).eventTarget;
      },
      get currentTarget() {
        return pd(this).currentTarget;
      },
      composedPath: function composedPath() {
        var currentTarget = pd(this).currentTarget;
        if (currentTarget == null) {
          return [];
        }
        return [currentTarget];
      },
      get NONE() {
        return 0;
      },
      get CAPTURING_PHASE() {
        return 1;
      },
      get AT_TARGET() {
        return 2;
      },
      get BUBBLING_PHASE() {
        return 3;
      },
      get eventPhase() {
        return pd(this).eventPhase;
      },
      stopPropagation: function stopPropagation() {
        var data = pd(this);
        data.stopped = true;
        if (typeof data.event.stopPropagation === "function") {
          data.event.stopPropagation();
        }
      },
      stopImmediatePropagation: function stopImmediatePropagation() {
        var data = pd(this);
        data.stopped = true;
        data.immediateStopped = true;
        if (typeof data.event.stopImmediatePropagation === "function") {
          data.event.stopImmediatePropagation();
        }
      },
      get bubbles() {
        return Boolean(pd(this).event.bubbles);
      },
      get cancelable() {
        return Boolean(pd(this).event.cancelable);
      },
      preventDefault: function preventDefault() {
        setCancelFlag(pd(this));
      },
      get defaultPrevented() {
        return pd(this).canceled;
      },
      get composed() {
        return Boolean(pd(this).event.composed);
      },
      get timeStamp() {
        return pd(this).timeStamp;
      },
      get srcElement() {
        return pd(this).eventTarget;
      },
      get cancelBubble() {
        return pd(this).stopped;
      },
      set cancelBubble(value) {
        if (!value) {
          return;
        }
        var data = pd(this);
        data.stopped = true;
        if (typeof data.event.cancelBubble === "boolean") {
          data.event.cancelBubble = true;
        }
      },
      get returnValue() {
        return !pd(this).canceled;
      },
      set returnValue(value) {
        if (!value) {
          setCancelFlag(pd(this));
        }
      },
      initEvent: function initEvent() {}
    };
    _Object$defineProperty(Event.prototype, "constructor", {
      value: Event,
      configurable: true,
      writable: true
    });
    if (typeof window !== "undefined" && typeof window.Event !== "undefined") {
      _Object$setPrototypeOf(Event.prototype, window.Event.prototype);
      wrappers.set(window.Event.prototype, Event);
    }
    function defineRedirectDescriptor(key) {
      return {
        get: function get() {
          return pd(this).event[key];
        },
        set: function set(value) {
          pd(this).event[key] = value;
        },
        configurable: true,
        enumerable: true
      };
    }
    function defineCallDescriptor(key) {
      return {
        value: function value() {
          var event = pd(this).event;
          return event[key].apply(event, arguments);
        },
        configurable: true,
        enumerable: true
      };
    }
    function defineWrapper(BaseEvent, proto) {
      var keys = _Object$keys(proto);
      if (keys.length === 0) {
        return BaseEvent;
      }
      function CustomEvent(eventTarget, event) {
        BaseEvent.call(this, eventTarget, event);
      }
      CustomEvent.prototype = _Object$create(BaseEvent.prototype, {
        constructor: {
          value: CustomEvent,
          configurable: true,
          writable: true
        }
      });
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        if (!(key in BaseEvent.prototype)) {
          var descriptor = _Object$getOwnPropertyDescriptor(proto, key);
          var isFunc = typeof descriptor.value === "function";
          _Object$defineProperty(CustomEvent.prototype, key, isFunc ? defineCallDescriptor(key) : defineRedirectDescriptor(key));
        }
      }
      return CustomEvent;
    }
    function getWrapper(proto) {
      if (proto == null || proto === Object.prototype) {
        return Event;
      }
      var wrapper = wrappers.get(proto);
      if (wrapper == null) {
        wrapper = defineWrapper(getWrapper(_Object$getPrototypeOf(proto)), proto);
        wrappers.set(proto, wrapper);
      }
      return wrapper;
    }
    function wrapEvent(eventTarget, event) {
      var Wrapper = getWrapper(_Object$getPrototypeOf(event));
      return new Wrapper(eventTarget, event);
    }
    function isStopped(event) {
      return pd(event).immediateStopped;
    }
    function setEventPhase(event, eventPhase) {
      pd(event).eventPhase = eventPhase;
    }
    function setCurrentTarget(event, currentTarget) {
      pd(event).currentTarget = currentTarget;
    }
    function setPassiveListener(event, passiveListener) {
      pd(event).passiveListener = passiveListener;
    }
    var listenersMap = /* @__PURE__ */new _WeakMap();
    var CAPTURE = 1;
    var BUBBLE = 2;
    var ATTRIBUTE = 3;
    function isObject(x) {
      return x !== null && _typeof(x) === "object";
    }
    function getListeners(eventTarget) {
      var listeners = listenersMap.get(eventTarget);
      if (listeners == null) {
        throw new TypeError("'this' is expected an EventTarget object, but got another value.");
      }
      return listeners;
    }
    function defineEventAttributeDescriptor(eventName) {
      return {
        get: function get() {
          var listeners = getListeners(this);
          var node = listeners.get(eventName);
          while (node != null) {
            if (node.listenerType === ATTRIBUTE) {
              return node.listener;
            }
            node = node.next;
          }
          return null;
        },
        set: function set(listener) {
          if (typeof listener !== "function" && !isObject(listener)) {
            listener = null;
          }
          var listeners = getListeners(this);
          var prev = null;
          var node = listeners.get(eventName);
          while (node != null) {
            if (node.listenerType === ATTRIBUTE) {
              if (prev !== null) {
                prev.next = node.next;
              } else if (node.next !== null) {
                listeners.set(eventName, node.next);
              } else {
                listeners["delete"](eventName);
              }
            } else {
              prev = node;
            }
            node = node.next;
          }
          if (listener !== null) {
            var newNode = {
              listener: listener,
              listenerType: ATTRIBUTE,
              passive: false,
              once: false,
              next: null
            };
            if (prev === null) {
              listeners.set(eventName, newNode);
            } else {
              prev.next = newNode;
            }
          }
        },
        configurable: true,
        enumerable: true
      };
    }
    function defineEventAttribute2(eventTargetPrototype, eventName) {
      _Object$defineProperty(eventTargetPrototype, "on".concat(eventName), defineEventAttributeDescriptor(eventName));
    }
    function defineCustomEventTarget(eventNames) {
      function CustomEventTarget() {
        EventTarget2.call(this);
      }
      CustomEventTarget.prototype = _Object$create(EventTarget2.prototype, {
        constructor: {
          value: CustomEventTarget,
          configurable: true,
          writable: true
        }
      });
      for (var i = 0; i < eventNames.length; ++i) {
        defineEventAttribute2(CustomEventTarget.prototype, eventNames[i]);
      }
      return CustomEventTarget;
    }
    function EventTarget2() {
      if (this instanceof EventTarget2) {
        listenersMap.set(this, /* @__PURE__ */new _Map());
        return;
      }
      if (arguments.length === 1 && _Array$isArray(arguments[0])) {
        return defineCustomEventTarget(arguments[0]);
      }
      if (arguments.length > 0) {
        var types = new Array(arguments.length);
        for (var i = 0; i < arguments.length; ++i) {
          types[i] = arguments[i];
        }
        return defineCustomEventTarget(types);
      }
      throw new TypeError("Cannot call a class as a function");
    }
    EventTarget2.prototype = {
      addEventListener: function addEventListener(eventName, listener, options) {
        if (listener == null) {
          return;
        }
        if (typeof listener !== "function" && !isObject(listener)) {
          throw new TypeError("'listener' should be a function or an object.");
        }
        var listeners = getListeners(this);
        var optionsIsObj = isObject(options);
        var capture = optionsIsObj ? Boolean(options.capture) : Boolean(options);
        var listenerType = capture ? CAPTURE : BUBBLE;
        var newNode = {
          listener: listener,
          listenerType: listenerType,
          passive: optionsIsObj && Boolean(options.passive),
          once: optionsIsObj && Boolean(options.once),
          next: null
        };
        var node = listeners.get(eventName);
        if (node === void 0) {
          listeners.set(eventName, newNode);
          return;
        }
        var prev = null;
        while (node != null) {
          if (node.listener === listener && node.listenerType === listenerType) {
            return;
          }
          prev = node;
          node = node.next;
        }
        prev.next = newNode;
      },
      removeEventListener: function removeEventListener(eventName, listener, options) {
        if (listener == null) {
          return;
        }
        var listeners = getListeners(this);
        var capture = isObject(options) ? Boolean(options.capture) : Boolean(options);
        var listenerType = capture ? CAPTURE : BUBBLE;
        var prev = null;
        var node = listeners.get(eventName);
        while (node != null) {
          if (node.listener === listener && node.listenerType === listenerType) {
            if (prev !== null) {
              prev.next = node.next;
            } else if (node.next !== null) {
              listeners.set(eventName, node.next);
            } else {
              listeners["delete"](eventName);
            }
            return;
          }
          prev = node;
          node = node.next;
        }
      },
      dispatchEvent: function dispatchEvent(event) {
        if (event == null || typeof event.type !== "string") {
          throw new TypeError('"event.type" should be a string.');
        }
        var listeners = getListeners(this);
        var eventName = event.type;
        var node = listeners.get(eventName);
        if (node == null) {
          return true;
        }
        var wrappedEvent = wrapEvent(this, event);
        var prev = null;
        while (node != null) {
          if (node.once) {
            if (prev !== null) {
              prev.next = node.next;
            } else if (node.next !== null) {
              listeners.set(eventName, node.next);
            } else {
              listeners["delete"](eventName);
            }
          } else {
            prev = node;
          }
          setPassiveListener(wrappedEvent, node.passive ? node.listener : null);
          if (typeof node.listener === "function") {
            try {
              node.listener.call(this, wrappedEvent);
            } catch (err) {
              if (typeof console !== "undefined" && typeof console.error === "function") {
                console.error(err);
              }
            }
          } else if (node.listenerType !== ATTRIBUTE && typeof node.listener.handleEvent === "function") {
            node.listener.handleEvent(wrappedEvent);
          }
          if (isStopped(wrappedEvent)) {
            break;
          }
          node = node.next;
        }
        setPassiveListener(wrappedEvent, null);
        setEventPhase(wrappedEvent, 0);
        setCurrentTarget(wrappedEvent, null);
        return !wrappedEvent.defaultPrevented;
      }
    };
    _Object$defineProperty(EventTarget2.prototype, "constructor", {
      value: EventTarget2,
      configurable: true,
      writable: true
    });
    if (typeof window !== "undefined" && typeof window.EventTarget !== "undefined") {
      _Object$setPrototypeOf(EventTarget2.prototype, window.EventTarget.prototype);
    }
    exports.defineEventAttribute = defineEventAttribute2;
    exports.EventTarget = EventTarget2;
    exports["default"] = EventTarget2;
    module.exports = EventTarget2;
    module.exports.EventTarget = module.exports["default"] = EventTarget2;
    module.exports.defineEventAttribute = defineEventAttribute2;
  }
});

// ../abort-controller/dist/abort-controller.mjs
var import_event_target_shim = __toESM(require_event_target_shim(), 1);
var AbortSignal = /*#__PURE__*/function (_import_event_target_) {
  function AbortSignal() {
    var _this;
    _classCallCheck(this, AbortSignal);
    _this = _callSuper(this, AbortSignal);
    throw new TypeError("AbortSignal cannot be constructed directly");
    return _this;
  }
  _inherits(AbortSignal, _import_event_target_);
  return _createClass(AbortSignal, [{
    key: "aborted",
    get: function get() {
      var aborted = abortedFlags.get(this);
      if (typeof aborted !== "boolean") {
        throw new TypeError("Expected 'this' to be an 'AbortSignal' object, but got ".concat(this === null ? "null" : _typeof(this)));
      }
      return aborted;
    }
  }]);
}(import_event_target_shim.EventTarget);
(0, import_event_target_shim.defineEventAttribute)(AbortSignal.prototype, "abort");
function createAbortSignal() {
  var signal = _Object$create(AbortSignal.prototype);
  import_event_target_shim.EventTarget.call(signal);
  abortedFlags.set(signal, false);
  return signal;
}
function abortSignal(signal) {
  if (abortedFlags.get(signal) !== false) {
    return;
  }
  abortedFlags.set(signal, true);
  signal.dispatchEvent({
    type: "abort"
  });
}
var abortedFlags = /* @__PURE__ */new _WeakMap();
_Object$defineProperties(AbortSignal.prototype, {
  aborted: {
    enumerable: true
  }
});
if (typeof _Symbol === "function" && _typeof(_Symbol$toStringTag) === "symbol") {
  _Object$defineProperty(AbortSignal.prototype, _Symbol$toStringTag, {
    configurable: true,
    value: "AbortSignal"
  });
}
var AbortController = /*#__PURE__*/function () {
  function AbortController() {
    _classCallCheck(this, AbortController);
    signals.set(this, createAbortSignal());
  }
  return _createClass(AbortController, [{
    key: "signal",
    get: function get() {
      return getSignal(this);
    }
  }, {
    key: "abort",
    value: function abort() {
      abortSignal(getSignal(this));
    }
  }]);
}();
var signals = /* @__PURE__ */new _WeakMap();
function getSignal(controller) {
  var signal = signals.get(controller);
  if (signal == null) {
    throw new TypeError("Expected 'this' to be an 'AbortController' object, but got ".concat(controller === null ? "null" : _typeof(controller)));
  }
  return signal;
}
_Object$defineProperties(AbortController.prototype, {
  signal: {
    enumerable: true
  },
  abort: {
    enumerable: true
  }
});
if (typeof _Symbol === "function" && _typeof(_Symbol$toStringTag) === "symbol") {
  _Object$defineProperty(AbortController.prototype, _Symbol$toStringTag, {
    configurable: true,
    value: "AbortController"
  });
}
var abort_controller_default = AbortController;

// src/index.mjs
var NativeAbortController = (typeof window === "undefined" ? {} : window).AbortController;
var AbortController2 = NativeAbortController || abort_controller_default;
var NativeAbortSignal = (typeof window === "undefined" ? {} : window).AbortSignal;
var AbortSignal2 = NativeAbortSignal || AbortSignal;
var src_default = AbortController2;
export { AbortController2 as AbortController, AbortSignal2 as AbortSignal, src_default as default };
