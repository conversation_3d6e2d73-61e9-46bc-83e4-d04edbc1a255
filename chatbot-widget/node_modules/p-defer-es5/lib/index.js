"use strict";

var _interopRequireDefault = require("@babel/runtime-corejs3/helpers/interopRequireDefault")["default"];
var _promise = _interopRequireDefault(require("@babel/runtime-corejs3/core-js-stable/promise"));
// ../p-defer/index.js
function pDefer() {
  var deferred = {};
  deferred.promise = new _promise["default"](function (resolve, reject) {
    deferred.resolve = resolve;
    deferred.reject = reject;
  });
  return deferred;
}

// src/index.js
module.exports = pDefer;
