# 🔧 Bot Framework Connection Troubleshooting Guide

## Quick Diagnosis

If you're seeing "Taking longer than usual to connect" or connection errors, follow these steps:

### 1. **Immediate Checks**

**Open Browser Developer Tools (F12) and check:**
- **Console Tab**: Look for error messages
- **Network Tab**: Check for failed requests to `directline.botframework.com`
- **Application Tab**: Check if any CORS errors are blocking requests

### 2. **Token Validation**

**Check your Direct Line token:**
```javascript
// Paste this in browser console to check token expiration
const token = "YOUR_TOKEN_HERE";
const payload = JSON.parse(atob(token.split('.')[1]));
const now = Math.floor(Date.now() / 1000);
console.log('Token expires:', new Date(payload.exp * 1000));
console.log('Is expired:', payload.exp < now);
```

### 3. **Connection Test**

**Test Direct Line API directly:**
```javascript
// Paste this in browser console
fetch('https://directline.botframework.com/v3/directline/conversations', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer YOUR_TOKEN_HERE',
        'Content-Type': 'application/json'
    }
}).then(r => r.json()).then(console.log).catch(console.error);
```

## Common Issues & Solutions

### ❌ **Issue: "Token expired" or 401 Unauthorized**

**Symptoms:**
- Connection fails immediately
- 401 error in network tab
- "Token expired" message

**Solutions:**
1. **Generate new token** in Azure Portal:
   - Go to your Bot Service → Channels → Direct Line
   - Click "Show" next to Secret keys
   - Copy a fresh secret key

2. **Check token format**:
   - Should start with letters/numbers
   - Contains dots (JWT format)
   - No extra spaces or characters

### ❌ **Issue: "Failed to connect" or Network Errors**

**Symptoms:**
- Long connection timeout
- Network errors in console
- CORS errors

**Solutions:**
1. **Check network connectivity**:
   ```bash
   # Test if Direct Line is accessible
   curl -I https://directline.botframework.com/v3/directline
   ```

2. **Corporate firewall/proxy**:
   - WebSocket connections may be blocked
   - Contact IT to whitelist `*.botframework.com`
   - Try from different network (mobile hotspot)

3. **CORS configuration**:
   - Ensure bot allows your domain
   - Check Azure Bot Service CORS settings

### ❌ **Issue: Bot doesn't respond to messages**

**Symptoms:**
- Connection successful
- Can send messages
- No responses from bot

**Solutions:**
1. **Check bot server status**:
   - Verify bot endpoint is running
   - Test bot in Azure Portal Web Chat
   - Check bot logs for errors

2. **Validate bot endpoint**:
   ```javascript
   // Test bot endpoint directly
   fetch('YOUR_BOT_ENDPOINT/api/messages', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify({
           type: 'message',
           text: 'test',
           from: { id: 'test-user' }
       })
   });
   ```

### ❌ **Issue: Widget loads but shows blank/error**

**Symptoms:**
- Widget appears but content is empty
- JavaScript errors in console
- Widget initialization fails

**Solutions:**
1. **Check widget files**:
   - Ensure CSS and JS files are accessible
   - Verify correct file paths
   - Check for 404 errors in network tab

2. **Enable debug mode**:
   ```javascript
   // Add debug props to widget
   <ChatbotWidget
       directLineToken="YOUR_TOKEN"
       debug={true}
       onError={(error) => console.error('Widget Error:', error)}
   />
   ```

## Step-by-Step Debugging Process

### Step 1: Use the Debug Tool

1. Open `debug-bot-connection.html` in your browser
2. Enter your Direct Line token
3. Run all tests to identify the issue
4. Follow specific recommendations

### Step 2: Enable Detailed Logging

```javascript
// Enable WebChat debug logging
localStorage.setItem('botframework-webchat:debug', '*');

// Enable widget debug mode
const widget = ChatbotWidget.init({
    directLineToken: 'YOUR_TOKEN',
    debug: true,
    onError: (error) => {
        console.error('Detailed error:', error);
        // Send to your error tracking service
    }
});
```

### Step 3: Test Bot Independently

**Test your bot without the widget:**

1. **Azure Portal Test**:
   - Go to Bot Service → Test in Web Chat
   - Send test messages
   - Verify bot responds correctly

2. **Direct API Test**:
   ```bash
   # Test bot endpoint
   curl -X POST "YOUR_BOT_ENDPOINT/api/messages" \
        -H "Content-Type: application/json" \
        -d '{"type":"message","text":"hello","from":{"id":"test"}}'
   ```

3. **Bot Framework Emulator**:
   - Download Bot Framework Emulator
   - Connect to your bot endpoint
   - Test conversation flow

### Step 4: Network Diagnostics

**Check network connectivity:**

```javascript
// Test various endpoints
const endpoints = [
    'https://directline.botframework.com/v3/directline',
    'https://directline.botframework.com/health',
    'YOUR_BOT_ENDPOINT/api/messages'
];

endpoints.forEach(async (url) => {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        console.log(`${url}: ${response.status}`);
    } catch (error) {
        console.error(`${url}: ${error.message}`);
    }
});
```

## Environment-Specific Issues

### **Development Environment**
- Use `localhost` or `127.0.0.1` for local testing
- Ensure dev server is running on correct port
- Check for HTTPS requirements

### **Production Environment**
- Verify SSL certificates are valid
- Check CDN/proxy configurations
- Ensure all assets are accessible

### **Corporate Networks**
- WebSocket connections may be blocked
- Proxy servers might interfere
- Contact IT for firewall exceptions

## Advanced Debugging

### **Monitor Connection States**

```javascript
// Add to your widget implementation
directLine.connectionStatus$.subscribe(status => {
    const states = {
        0: 'Uninitialized',
        1: 'Connecting', 
        2: 'Online',
        3: 'ExpiredToken',
        4: 'FailedToConnect',
        5: 'Ended'
    };
    console.log('Connection state:', states[status]);
});
```

### **Capture Network Traffic**

1. Open Browser DevTools → Network tab
2. Filter by "directline.botframework.com"
3. Look for failed requests or unusual response codes
4. Check request/response headers for clues

### **Test with Different Tokens**

1. Generate multiple Direct Line secrets
2. Test with each one individually
3. Compare behavior between tokens
4. Check if issue is token-specific

## Getting Help

If issues persist after following this guide:

1. **Collect Debug Information**:
   - Browser console logs
   - Network tab screenshots
   - Token validation results
   - Bot endpoint test results

2. **Check Azure Service Health**:
   - Visit Azure Status page
   - Check Bot Framework service status
   - Look for known issues

3. **Contact Support**:
   - Include all debug information
   - Specify exact error messages
   - Provide steps to reproduce

## Prevention Tips

1. **Monitor token expiration** and refresh proactively
2. **Set up health checks** for your bot endpoint
3. **Use error tracking** to catch issues early
4. **Test in multiple environments** before deployment
5. **Keep backup tokens** for emergency use
