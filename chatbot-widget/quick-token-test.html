<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Token Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Quick Direct Line Token Test</h1>
    
    <div class="info">
        <strong>Current Token Being Tested:</strong><br>
        <code>rD8nsFmme2HPerfuMuhnqxAzpGK4tXY9RG2mlOqq3ygcOgu7vJutJQQJ99BAACi5YpzAArohAAABAZBS2FVD.10uUUSUGercwr9x6MVOY28kQiZHNEa0cd88jORWQF7mYMzX0WapQJQQJ99BFAC5RqLJAArohAAABAZBS3eHt</code>
    </div>

    <button onclick="testToken()">🧪 Test Current Token</button>
    <button onclick="testCORS()">🌐 Test CORS</button>
    <button onclick="testWebSocket()">🔌 Test WebSocket</button>
    
    <div id="results"></div>

    <h2>📋 Quick Fixes</h2>
    <div class="info">
        <h3>If token test fails:</h3>
        <ol>
            <li><strong>Generate New Token:</strong>
                <ul>
                    <li>Go to <a href="https://portal.azure.com" target="_blank">Azure Portal</a></li>
                    <li>Navigate to your Bot Service</li>
                    <li>Go to Channels → Direct Line</li>
                    <li>Click "Show" next to Secret keys</li>
                    <li>Copy a fresh secret key</li>
                </ul>
            </li>
            <li><strong>Update Token in Code:</strong>
                <div class="code">
// In src/main.tsx, replace the directLineToken value:<br>
directLineToken="YOUR_NEW_TOKEN_HERE"
                </div>
            </li>
        </ol>
    </div>

    <script>
        const TOKEN = "rD8nsFmme2HPerfuMuhnqxAzpGK4tXY9RG2mlOqq3ygcOgu7vJutJQQJ99BAACi5YpzAArohAAABAZBS2FVD.10uUUSUGercwr9x6MVOY28kQiZHNEa0cd88jORWQF7mYMzX0WapQJQQJ99BFAC5RqLJAArohAAABAZBS3eHt";
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testToken() {
            clearResults();
            addResult('🔄 Testing Direct Line token...', 'info');
            
            try {
                // First, check token format and expiration
                const parts = TOKEN.split('.');
                if (parts.length !== 3) {
                    addResult('❌ Invalid token format - not a valid JWT', 'error');
                    return;
                }
                
                try {
                    const payload = JSON.parse(atob(parts[1]));
                    const now = Math.floor(Date.now() / 1000);
                    
                    if (payload.exp && payload.exp < now) {
                        addResult('❌ Token has EXPIRED! Generate a new token from Azure Portal.', 'error');
                        addResult(`Token expired: ${new Date(payload.exp * 1000).toLocaleString()}`, 'error');
                        return;
                    } else if (payload.exp) {
                        addResult(`✅ Token is valid until: ${new Date(payload.exp * 1000).toLocaleString()}`, 'success');
                    }
                } catch (e) {
                    addResult('⚠️ Could not decode token payload, but format looks correct', 'info');
                }
                
                // Test actual Direct Line connection
                addResult('🔄 Testing Direct Line API connection...', 'info');
                
                const response = await fetch('https://directline.botframework.com/v3/directline/conversations', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✅ SUCCESS! Direct Line connection works perfectly!', 'success');
                    addResult(`Conversation ID: ${data.conversationId}`, 'success');
                    addResult('🎉 Your token is valid and the connection is working. The issue might be elsewhere.', 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Direct Line API Error: ${response.status} ${response.statusText}`, 'error');
                    addResult(`Error details: ${errorText}`, 'error');
                    
                    if (response.status === 401) {
                        addResult('🔑 This is an authentication error. Generate a new token from Azure Portal.', 'error');
                    } else if (response.status === 403) {
                        addResult('🚫 This is a permissions error. Check your bot configuration.', 'error');
                    }
                }
                
            } catch (error) {
                addResult(`❌ Network Error: ${error.message}`, 'error');
                addResult('🌐 This could be a CORS issue or network connectivity problem.', 'error');
            }
        }
        
        async function testCORS() {
            clearResults();
            addResult('🌐 Testing CORS configuration...', 'info');
            
            const endpoints = [
                'https://directline.botframework.com/v3/directline',
                'https://directline.botframework.com/health'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    addResult(`🔄 Testing ${endpoint}...`, 'info');
                    
                    const response = await fetch(endpoint, {
                        method: 'HEAD',
                        mode: 'cors'
                    });
                    
                    addResult(`✅ ${endpoint} - CORS OK`, 'success');
                    
                } catch (error) {
                    addResult(`❌ ${endpoint} - CORS Error: ${error.message}`, 'error');
                }
            }
        }
        
        function testWebSocket() {
            clearResults();
            addResult('🔌 Testing WebSocket connectivity...', 'info');
            
            try {
                const ws = new WebSocket('wss://directline.botframework.com/v3/directline/conversations/test/stream');
                
                ws.onopen = function() {
                    addResult('✅ WebSocket connection successful!', 'success');
                    ws.close();
                };
                
                ws.onerror = function(error) {
                    addResult('❌ WebSocket connection failed', 'error');
                    addResult('This might indicate firewall or proxy issues', 'error');
                };
                
                ws.onclose = function(event) {
                    if (event.code !== 1000) {
                        addResult(`⚠️ WebSocket closed with code: ${event.code}`, 'info');
                    }
                };
                
                // Timeout after 5 seconds
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        addResult('⏱️ WebSocket connection timeout', 'error');
                        ws.close();
                    }
                }, 5000);
                
            } catch (error) {
                addResult(`❌ WebSocket Error: ${error.message}`, 'error');
            }
        }
        
        // Auto-run token test on page load
        window.addEventListener('load', () => {
            setTimeout(testToken, 1000);
        });
    </script>
</body>
</html>
