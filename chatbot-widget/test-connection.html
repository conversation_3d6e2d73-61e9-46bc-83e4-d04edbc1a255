<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Status Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connecting { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .connected { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .disconnected { background-color: #e2e3e5; border: 1px solid #d6d8db; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔗 DirectLine Connection Status Test</h1>
    
    <div id="status" class="status connecting">
        <strong>Status:</strong> <span id="status-text">Initializing...</span>
    </div>
    
    <div id="log" class="log">Starting connection test...\n</div>
    
    <button onclick="testConnection()">🔄 Test Connection</button>
    <button onclick="clearLog()">🗑️ Clear Log</button>

    <script type="module">
        // Connection Status Constants (matching our widget)
        const ConnectionStatus = {
            Uninitialized: 0,
            Connecting: 1,
            Online: 2,
            ExpiredToken: 3,
            FailedToConnect: 4,
            Ended: 5
        };

        const TOKEN = 'Dq5tsShm9mSLmI2ff2stqhFeR3keZUIMQrztTsBICveTcA5SFDNVJQQJ99BGACi5YpzAArohAAABAZBSlObL.DeCn7xRecJIYbs6mAMSU6rHeOjRBOHYaW1arNaldoyGKwjYlMwNTJQQJ99BGACi5YpzAArohAAABAZBS3Ulm';

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusElement = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            statusText.textContent = status;
            statusElement.className = `status ${className}`;
        }

        function getStatusName(statusCode) {
            return Object.keys(ConnectionStatus).find(
                key => ConnectionStatus[key] === statusCode
            ) || `Unknown(${statusCode})`;
        }

        async function testConnection() {
            log('🔄 Starting DirectLine connection test...');
            updateStatus('Testing...', 'connecting');

            try {
                // Import DirectLine dynamically
                const { createDirectLine } = await import('https://cdn.jsdelivr.net/npm/botframework-webchat@latest/lib/index.js');
                
                log('✅ Bot Framework WebChat loaded successfully');

                const directLineOptions = {
                    secret: TOKEN,
                    domain: 'https://directline.botframework.com/v3/directline',
                    pollingInterval: 1000
                };

                log('🔗 Creating DirectLine connection...');
                const directLine = createDirectLine(directLineOptions);

                // Subscribe to connection status
                directLine.connectionStatus$.subscribe({
                    next: (connectionStatus) => {
                        const statusName = getStatusName(connectionStatus);
                        log(`📡 Connection status: ${connectionStatus} (${statusName})`);

                        switch (connectionStatus) {
                            case ConnectionStatus.Uninitialized:
                                updateStatus('Uninitialized', 'connecting');
                                break;
                            case ConnectionStatus.Connecting:
                                updateStatus('Connecting...', 'connecting');
                                break;
                            case ConnectionStatus.Online:
                                updateStatus('Connected ✅', 'connected');
                                log('🎉 Connection successful!');
                                break;
                            case ConnectionStatus.ExpiredToken:
                                updateStatus('Token Expired ❌', 'error');
                                log('❌ Token has expired');
                                break;
                            case ConnectionStatus.FailedToConnect:
                                updateStatus('Failed to Connect ❌', 'error');
                                log('❌ Connection failed');
                                break;
                            case ConnectionStatus.Ended:
                                updateStatus('Disconnected', 'disconnected');
                                log('🔌 Connection ended');
                                break;
                            default:
                                updateStatus(`Unknown Status: ${connectionStatus}`, 'error');
                                log(`⚠️ Unknown status code: ${connectionStatus}`);
                                break;
                        }
                    },
                    error: (error) => {
                        log(`❌ Connection error: ${error.message}`);
                        updateStatus('Connection Error ❌', 'error');
                    }
                });

                log('👂 Subscribed to connection status updates');

            } catch (error) {
                log(`❌ Test failed: ${error.message}`);
                updateStatus('Test Failed ❌', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // Auto-start test
        window.testConnection = testConnection;
        window.clearLog = clearLog;
        
        // Start test automatically
        setTimeout(testConnection, 1000);
    </script>
</body>
</html>
