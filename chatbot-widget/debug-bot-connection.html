<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Framework Connection Debugger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #f9f9f9;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .log-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Bot Framework Connection Debugger</h1>
        <p>This tool helps diagnose connection issues with your Bot Framework chatbot widget.</p>

        <div class="test-section">
            <h2>1. Direct Line Token Configuration</h2>
            <label for="tokenInput">Enter your Direct Line Token:</label>
            <input type="text" id="tokenInput" placeholder="Paste your Direct Line token here..." 
                   value="rD8nsFmme2HPerfuMuhnqxAzpGK4tXY9RG2mlOqq3ygcOgu7vJutJQQJ99BAACi5YpzAArohAAABAZBS2FVD.10uUUSUGercwr9x6MVOY28kQiZHNEa0cd88jORWQF7mYMzX0WapQJQQJ99BFAC5RqLJAArohAAABAZBS3eHt">
            <button onclick="validateToken()">Validate Token</button>
            <button onclick="testDirectLineConnection()">Test Direct Line Connection</button>
            <div id="tokenResults"></div>
        </div>

        <div class="test-section">
            <h2>2. Network Connectivity Tests</h2>
            <button onclick="testCORSAndConnectivity()">Test CORS & Connectivity</button>
            <button onclick="testDirectLineEndpoints()">Test Direct Line Endpoints</button>
            <div id="networkResults"></div>
        </div>

        <div class="test-section">
            <h2>3. Bot Server Health Check</h2>
            <button onclick="testBotHealth()">Check Bot Server Health</button>
            <button onclick="sendTestMessage()">Send Test Message</button>
            <div id="botHealthResults"></div>
        </div>

        <div class="test-section">
            <h2>4. Browser Console Logs</h2>
            <button onclick="enableDetailedLogging()">Enable Detailed Logging</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <div class="log-output" id="logOutput">Console logs will appear here...</div>
        </div>

        <div class="test-section">
            <h2>5. Widget Integration Test</h2>
            <button onclick="testWidgetIntegration()">Test Widget with Current Token</button>
            <div id="widgetTestResults"></div>
            <div id="testWidgetContainer"></div>
        </div>

        <div class="test-section">
            <h2>6. Troubleshooting Steps</h2>
            <div id="troubleshootingSteps">
                <h3>Common Issues and Solutions:</h3>
                <ul>
                    <li><strong>Token Expired:</strong> Direct Line tokens expire after a certain time. Generate a new token from Azure Portal.</li>
                    <li><strong>CORS Issues:</strong> Ensure your bot allows requests from your domain.</li>
                    <li><strong>Bot Server Down:</strong> Check if your bot service is running and accessible.</li>
                    <li><strong>Network Firewall:</strong> Corporate firewalls may block WebSocket connections.</li>
                    <li><strong>Invalid Token:</strong> Verify the token is correctly copied without extra spaces.</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let logBuffer = [];
        
        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logBuffer.push(logEntry);
            updateLogDisplay();
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToOutput(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToOutput(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToOutput(args.join(' '), 'warn');
        };
        
        function updateLogDisplay() {
            const logOutput = document.getElementById('logOutput');
            logOutput.textContent = logBuffer.slice(-50).join('\n'); // Show last 50 logs
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        function clearLogs() {
            logBuffer = [];
            updateLogDisplay();
        }
        
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        async function validateToken() {
            clearResults('tokenResults');
            const token = document.getElementById('tokenInput').value.trim();
            
            if (!token) {
                showResult('tokenResults', '❌ Please enter a Direct Line token', 'error');
                return;
            }
            
            // Basic token format validation
            if (!token.includes('.')) {
                showResult('tokenResults', '❌ Token format appears invalid (should contain dots)', 'error');
                return;
            }
            
            try {
                // Try to decode the JWT token to check expiration
                const parts = token.split('.');
                if (parts.length !== 3) {
                    showResult('tokenResults', '❌ Invalid JWT token format', 'error');
                    return;
                }
                
                const payload = JSON.parse(atob(parts[1]));
                const now = Math.floor(Date.now() / 1000);
                
                if (payload.exp && payload.exp < now) {
                    showResult('tokenResults', '❌ Token has expired. Please generate a new token.', 'error');
                    return;
                }
                
                showResult('tokenResults', '✅ Token format is valid and not expired', 'success');
                
                if (payload.exp) {
                    const expiryDate = new Date(payload.exp * 1000);
                    showResult('tokenResults', `ℹ️ Token expires: ${expiryDate.toLocaleString()}`, 'info');
                }
                
            } catch (error) {
                showResult('tokenResults', '⚠️ Could not decode token, but format looks correct', 'warning');
            }
        }
        
        async function testDirectLineConnection() {
            clearResults('tokenResults');
            const token = document.getElementById('tokenInput').value.trim();
            
            if (!token) {
                showResult('tokenResults', '❌ Please enter a token first', 'error');
                return;
            }
            
            try {
                showResult('tokenResults', '🔄 Testing Direct Line connection...', 'info');
                
                // Test the Direct Line conversation endpoint
                const response = await fetch('https://directline.botframework.com/v3/directline/conversations', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('tokenResults', '✅ Direct Line connection successful!', 'success');
                    showResult('tokenResults', `ℹ️ Conversation ID: ${data.conversationId}`, 'info');
                } else {
                    const errorText = await response.text();
                    showResult('tokenResults', `❌ Direct Line connection failed: ${response.status} ${response.statusText}`, 'error');
                    showResult('tokenResults', `Error details: ${errorText}`, 'error');
                }
                
            } catch (error) {
                showResult('tokenResults', `❌ Network error: ${error.message}`, 'error');
                console.error('Direct Line test error:', error);
            }
        }
        
        async function testCORSAndConnectivity() {
            clearResults('networkResults');
            
            const endpoints = [
                'https://directline.botframework.com/v3/directline/conversations',
                'https://webchat-mockbot.azurewebsites.net/api/messages'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    showResult('networkResults', `🔄 Testing ${endpoint}...`, 'info');
                    
                    const response = await fetch(endpoint, {
                        method: 'OPTIONS',
                        mode: 'cors'
                    });
                    
                    showResult('networkResults', `✅ ${endpoint} - CORS preflight successful`, 'success');
                    
                } catch (error) {
                    showResult('networkResults', `❌ ${endpoint} - CORS/Network error: ${error.message}`, 'error');
                }
            }
        }
        
        async function testDirectLineEndpoints() {
            clearResults('networkResults');
            
            const endpoints = [
                'https://directline.botframework.com/v3/directline',
                'https://directline.botframework.com/health'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    showResult('networkResults', `🔄 Testing ${endpoint}...`, 'info');
                    
                    const response = await fetch(endpoint);
                    
                    if (response.ok) {
                        showResult('networkResults', `✅ ${endpoint} - Accessible`, 'success');
                    } else {
                        showResult('networkResults', `⚠️ ${endpoint} - Status: ${response.status}`, 'warning');
                    }
                    
                } catch (error) {
                    showResult('networkResults', `❌ ${endpoint} - Error: ${error.message}`, 'error');
                }
            }
        }
        
        function enableDetailedLogging() {
            // Enable WebChat debugging
            window.localStorage.setItem('botframework-webchat:debug', '*');
            showResult('networkResults', '✅ Detailed logging enabled. Check browser console for WebChat debug logs.', 'success');
            console.log('WebChat debugging enabled');
        }
        
        async function testWidgetIntegration() {
            clearResults('widgetTestResults');
            const token = document.getElementById('tokenInput').value.trim();
            
            if (!token) {
                showResult('widgetTestResults', '❌ Please enter a token first', 'error');
                return;
            }
            
            try {
                // Load the widget script if not already loaded
                if (!window.ChatbotWidget) {
                    showResult('widgetTestResults', '🔄 Loading widget script...', 'info');
                    
                    const script = document.createElement('script');
                    script.src = './dist/chatbot-widget.umd.cjs';
                    script.onload = () => {
                        showResult('widgetTestResults', '✅ Widget script loaded', 'success');
                        initializeTestWidget(token);
                    };
                    script.onerror = () => {
                        showResult('widgetTestResults', '❌ Failed to load widget script', 'error');
                    };
                    document.head.appendChild(script);
                } else {
                    initializeTestWidget(token);
                }
                
            } catch (error) {
                showResult('widgetTestResults', `❌ Widget test error: ${error.message}`, 'error');
            }
        }
        
        function initializeTestWidget(token) {
            try {
                const container = document.getElementById('testWidgetContainer');
                container.innerHTML = '<div id="test-widget-instance"></div>';
                
                const widget = window.ChatbotWidget.init({
                    containerId: 'test-widget-instance',
                    directLineToken: token,
                    userID: 'debug-user-' + Date.now(),
                    username: 'Debug User',
                    position: 'bottom-right',
                    minimized: false,
                    onToggle: (isOpen) => {
                        console.log('Widget toggled:', isOpen);
                    }
                });
                
                showResult('widgetTestResults', '✅ Test widget initialized successfully', 'success');
                showResult('widgetTestResults', 'ℹ️ Check the widget below and browser console for any errors', 'info');
                
            } catch (error) {
                showResult('widgetTestResults', `❌ Widget initialization error: ${error.message}`, 'error');
                console.error('Widget init error:', error);
            }
        }
        
        // Initialize logging on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Bot Framework Connection Debugger loaded');
            logToOutput('Debugger initialized', 'info');
        });
    </script>
</body>
</html>
